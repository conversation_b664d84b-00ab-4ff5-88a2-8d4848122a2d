package com.sankuai.walleops.cloud.triage.pojo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CloudTriageEventUpdateRequest {
    private Long id;

    private String informationId;

    /**
     * 事件id
     */
    private String eventId;

    /**
     * record名称
     */
    private String recordName;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车辆id
     */
    private String vehicleId;

    /**
     * 事件发生时间
     */
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date eventTime;

    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date recoveryTime;

    /**
     * 事件类型，-1表示未知，0表碰撞检测事件，1表示车辆停止不前，其他待补充
     */
    private Integer eventType;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 状态， 0表示未处理，1表示进行中，2表示完成，3表示取消
     */
    private Integer status;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 处理开始时间
     */
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date operateStartTime;

    /**
     * 处理结束时间
     */
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date operateEndTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用车目的
     */
    private String purpose;

    /**
     * 场地
     */
    private String place;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private int isDeleted;
    // 上报人
    private String reporter;

    /**
     * 是否检查车辆状态字段
     */
    private Boolean isCheckVehicleStatus;

    /**
     * 是否发起呼叫
     */
    private Integer mrmCalled;
}
