package com.sankuai.walleops.cloud.triage.component.filter;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class EventFilterChain {

    @Resource
    private List<EventFilter> eventFilterList;

    @MdpConfig("event_filters_switch: false")
    private Boolean eventFiltersSwitch = Boolean.FALSE;

    /**
     * 前一个Filter返回false，执行下一个Filter
     * @param informationDTO 消息内容
     * */
    public boolean doFilter(InformationDTO informationDTO) {
        // 过滤器开关关闭
        if(eventFiltersSwitch == Boolean.FALSE) {
            return false;
        }

        for(EventFilter filter: eventFilterList) {
            // 如果任何一个filter返回true，那么整个filter chain返回true
            if(filter.doFilter(informationDTO, this)) {
                return true;
            }
        }

        return false;
    }
}
