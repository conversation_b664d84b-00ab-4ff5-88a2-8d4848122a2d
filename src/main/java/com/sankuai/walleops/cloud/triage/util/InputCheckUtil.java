package com.sankuai.walleops.cloud.triage.util;

import com.sankuai.walleops.cloud.triage.util.exception.ParamInputErrorException;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 上游入参校验，仅校验当前是否合法，是否满足入参条件
 *
 */
@Slf4j
public class InputCheckUtil {

    /**
     * 判断空
     *
     * @param obj
     * @param msg
     */
    public static void isNull(Object obj, String msg) throws ParamInputErrorException {
        if (obj != null) {
            throwBizException(msg);
        }
    }

    public static void isNull(Object obj, String msg, String... replaces) throws ParamInputErrorException {
        if (obj != null) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }


    /**
     * 不为null，否则抛出异常
     *
     * @param obj
     * @param msg
     */
    public static void isNotNull(Object obj, String msg) throws ParamInputErrorException {
        if (obj == null) {
            throwBizException(msg);
        }
    }

    public static void isNotTrue(boolean bool, String msg) throws ParamInputErrorException {
        if (bool) {
            throwBizException(msg);
        }
    }

    public static void isNotTrue(boolean bool, String msg, String... replaces) throws ParamInputErrorException {
        if (bool) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }

    public static void isNotNull(Object obj, String msg, String... replaces) throws ParamInputErrorException {
        if (obj == null) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }


    /**
     * 字符串不为空，否则抛出异常
     *
     * @param str
     * @param msg
     */
    public static void isNotBlank(String str, String msg) throws ParamInputErrorException {
        if (StringUtils.isBlank(str)) {
            throwBizException(msg);
        }
    }

    public static void isNotBlank(String str, String msg, String... replaces) throws ParamInputErrorException {
        if (StringUtils.isBlank(str)) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }

    /**
     * 列表不为空，否则抛出异常
     *
     * @param collection
     * @param msg
     */
    public static void isNotEmpty(Collection<?> collection, String msg) throws ParamInputErrorException {
        if (collection == null || collection.isEmpty()) {
            throwBizException(msg);
        }
    }

    public static void isNotEmpty(Collection<?> collection, String msg, String... replaces)
            throws ParamInputErrorException {
        if (collection == null || collection.isEmpty()) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }

    private static void throwBizException(String msg) throws ParamInputErrorException {
        throw new ParamInputErrorException(msg);
    }

    private static String replaceMsg(String msg, Object... replace) {
        String str = msg;
        if (StringUtils.isNotBlank(str) && replace != null && replace.length > 0) {
            for (Object temp : replace) {
                str = str.replaceFirst("\\{}", "" + temp);
            }
        }
        return str;
    }
}
