package com.sankuai.walleops.cloud.triage.thrift.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "碰撞标签查询")
public class CollisionAlertQueryRequest {

    @ThriftField(1)
    @FieldDoc(description = "车架号")
    private String vin;

    @ThriftField(2)
    @FieldDoc(description = "异常时间")
    private Long eventTime;
}
