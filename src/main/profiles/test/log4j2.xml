<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">
    <appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%p] %t [%c{1}#%M:%L] TRACE=%traceId} %m%n" />
        </Console>

        <!--默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，非阻塞模式-->
        <XMDFile name="XMDFileAppender" fileName="triage.log" sizeBasedTriggeringSize="512M"
                 rolloverMax="30">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%p] %t [%c{1}#%M:%L] TRACE=%traceId} %m%n" />
        </XMDFile>

        <!--日志远程上报-->
        <AsyncScribe name="ScribeAsyncAppender" blocking="false">
            <!--远程日志默认使用appkey作为日志名(app.properties文件中的app.name字段)，也可自定义scribeCategory属性，scribeCategory优先级高于appkey-->
            <LcLayout/>
        </AsyncScribe>

        <AsyncScribe name="AsyncScribeAppender_com.sankuai.walleops.cloud.triage" blocking="false">
            <Property name="scribeCategory">com.sankuai.walleops.cloud.triage</Property>
            <!-- 如果要开启丢失率检测，请放开下面代码注释 -->
            <!-- <Property name="checkLoss">true</Property> -->
            <LcLayout/>
        </AsyncScribe>

        <CatAppender name="catAppender"/>
        <MDPTraceAppender name="mdpTrace"/>
    </appenders>

    <loggers>
        <!--远程日志，详细使用说明参见 MDP 文档中日志中心部分 https://km.sankuai.com/custom/onecloud/page/424836119#id-3%E6%97%A5%E5%BF%97%E4%B8%AD%E5%BF%83 -->
        <logger name="scribe" level="info" additivity="false">
            <appender-ref ref="ScribeAsyncAppender" />
        </logger>

        <root level="info">
            <appender-ref ref="XMDFileAppender"/>
            <appender-ref ref="Console" />
            <appender-ref ref="catAppender"/>
            <appender-ref ref="mdpTrace"/>
            <appender-ref ref="AsyncScribeAppender_com.sankuai.walleops.cloud.triage"/>
        </root>
    </loggers>
</configuration>