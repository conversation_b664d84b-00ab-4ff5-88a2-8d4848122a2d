package com.sankuai.walleops.cloud.triage.component.handleStrategy;
import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.proxy.SquirrelProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component("collision-detection-handle")
@Slf4j
public class CollisionDetectionHandleStrategy extends HandleStrategy {

    @Resource
    private InformationApiService informationApiService;
    @Resource
    private SquirrelProxy squirrelProxy;

    @Override
    public void process(BizCloudTriageEvent cloudTriageEvent) {
        log.info("CollisionDetectionHandleStrategy is processing, param is {}", cloudTriageEvent);
        setAutoCancelAndTimeoutCall(cloudTriageEvent);
    }

    public void setAutoCancel(BizCloudTriageEvent cloudTriageEvent){
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("vinList", cloudTriageEvent.getVin());
            List<VehicleRealtimeStatusDTO> resultList = informationApiService.callVehicleStatusService(param);
            if (!resultList.isEmpty()) {
                VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = resultList.get(0);
                //对于VHR <= 1  的车设置 系统自动取消，这里的VHR <= 1 包括两种情况
                //1）VHR 为 空   2）VHR == 1
                Integer isVhrMultiple = vehicleRealtimeStatusDTO.getIsVhrMultiple();
                if(isVhrMultiple != null && isVhrMultiple.intValue() == 0) { // VHR<=1
                    cloudTriageEvent.setOperator("system");
                    cloudTriageEvent.setStatus(3);
                }
            }
        } catch (Exception e) {
            log.error("query information vhr error: {}", cloudTriageEvent);
        }
    }
    public void setAutoCancelAndTimeoutCall(BizCloudTriageEvent cloudTriageEvent){
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("vinList", cloudTriageEvent.getVin());
            List<VehicleRealtimeStatusDTO> resultList = informationApiService.callVehicleStatusService(param);
            if (!resultList.isEmpty()) {
                VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = resultList.get(0);
                //对于VHR <= 1  的车设置 系统自动取消，这里的VHR <= 1 包括两种情况
                //1）VHR 为 空   2）VHR == 1
                Integer isVhrMultiple = vehicleRealtimeStatusDTO.getIsVhrMultiple();
                if(isVhrMultiple != null && isVhrMultiple.intValue() == 0) { // VHR<=1
                    cloudTriageEvent.setOperator("system");
                    // 3 表示取消状态
                    cloudTriageEvent.setStatus(3);
                }
                // 存储 VHR>1车辆的碰撞检测类型事件 至Squirrel中
                if(EventTypeEnum.COLLISION_DETECTION.getCode().equals(cloudTriageEvent.getEventType())
                        && vehicleRealtimeStatusDTO.getIsVhrMultiple() != null
                        && vehicleRealtimeStatusDTO.getIsVhrMultiple() == 1) {
                    String field = cloudTriageEvent.getEventId();
                    long value = cloudTriageEvent.getEventTime().getTime();
                    squirrelProxy.hset(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY, CommonConstant.KEY_ACCIDENT_HASH, field, value);
                }
            }
        } catch (Exception e) {
            log.error("query information vhr error: {}", cloudTriageEvent);
        }
    }

    public void setTimeoutCall(BizCloudTriageEvent cloudTriageEvent) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("vinList", cloudTriageEvent.getVin());
            List<VehicleRealtimeStatusDTO> resultList = informationApiService.callVehicleStatusService(param);
            if (!resultList.isEmpty()) {
                VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = resultList.get(0);
                // 存储 VHR>1车辆的碰撞检测类型事件 至Squirrel中
                if(EventTypeEnum.COLLISION_DETECTION.getCode().equals(cloudTriageEvent.getEventType())
                        && vehicleRealtimeStatusDTO.getIsVhrMultiple() != null
                        && vehicleRealtimeStatusDTO.getIsVhrMultiple() == 1) {
                    String field = cloudTriageEvent.getEventId();
                    long value = cloudTriageEvent.getEventTime().getTime();
                    squirrelProxy.hset(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY, CommonConstant.KEY_ACCIDENT_HASH, field, value);
                }
            }
        } catch (Exception e) {
            log.error("query information purpose and place error: {}", cloudTriageEvent);
        }
    }

}

