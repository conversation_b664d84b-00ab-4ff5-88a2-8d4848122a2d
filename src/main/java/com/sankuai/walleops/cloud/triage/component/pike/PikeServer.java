package com.sankuai.walleops.cloud.triage.component.pike;

import com.alibaba.fastjson.JSON;
import com.sankuai.pike.message.api.common.enums.MessagePushStatusEnum;
import com.sankuai.pike.message.api.rpc.server.entity.GroupMessageResponse;
import com.sankuai.pike.message.sdk.PikeMessageServerClient;
import com.sankuai.pike.message.sdk.listener.ListenerHolder;
import com.sankuai.pike.message.sdk.lite.callback.MessageCallback;
import com.sankuai.pike.message.sdk.lite.entity.MessageRequest;
import com.sankuai.pike.message.sdk.lite.entity.MessageResponse;
import com.sankuai.pike.message.sdk.lite.entity.PikeGroupMessage;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.proxy.SquirrelProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PikeServer {
    private static final String PIKE_BIZID_CLOUD_TRIAGE = "cloud_triage_new_event";

    @Resource
    private PikeConnectListener pikeConnectListener;

    @Resource
    private PikeMessageListener pikeMessageListener;

    @Resource
    private SquirrelProxy squirrelProxy;

    @PostConstruct
    public void init() {
        ListenerHolder.registerLister(PIKE_BIZID_CLOUD_TRIAGE, pikeConnectListener);
        ListenerHolder.registerLister(PIKE_BIZID_CLOUD_TRIAGE, pikeMessageListener);
        ListenerHolder.publishListenerAsMTthriftService();
    }

    public void notifyNewEvent(BizCloudTriageEvent cloudTriageEvent) {
//        Map<String, String> allField = squirrelProxy.hgetall(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY,
//                                                            CommonConstant.TRIAGE_NEW_EVENT_CLIENT_KEY);
//
//        for(Map.Entry<String, String> entry: allField.entrySet()) {
//            String[] keySplit = entry.getKey().split("_");
//            String token = keySplit[1];
//
//            Map<String, String> params = new HashMap<>();
//            params.put("newEvent", "1");
//            sendMessage(PIKE_BIZID_CLOUD_TRIAGE, token, params);
//        }

        Map<String, Object> params = new HashMap<>();
        params.put("newEvent", 1);
        sendGroupMessage(PIKE_BIZID_CLOUD_TRIAGE, params);
    }

    public void notifyNewEvent() {
        Map<String, Object> params = new HashMap<>();
        params.put("newEvent", 1);
        sendGroupMessage(PIKE_BIZID_CLOUD_TRIAGE, params);
    }

    /**
     * 服务端发送信息至客户端
     * @param bizId 业务ID
     * @param token 连接唯一标识
     * @param params 参数
     *
     * */
    public void sendMessage(String bizId, String token, Map<String, String> params) {
        MessageRequest request = MessageRequest.newTokenMessageRequest(bizId, token);
        request.setStringContent(JSON.toJSONString(params));
        log.info("发送信息至客户端 by pike, request: {}", request);

        PikeMessageServerClient.sendMessageToSDK(request, new MessageCallback() {
            @Override
            public void onResponse(MessageResponse response) {
                log.info("发送信息至客户端 by pike 成功, response: {}", response);
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("发送信息至客户端 by pike 失败", throwable);
            }
        });
    }

    /**
     * 群发消息至bizId的所有客户端
     * @param bizId 业务ID
     * @param params 参数
     * */
    public void sendGroupMessage(String bizId, Map<String, Object> params) {
        PikeGroupMessage groupMessage = PikeGroupMessage.newAppIdBasedGroupMessage(0, bizId);
        groupMessage.setStringContent(JSON.toJSONString(params));
        log.info("群发信息至所有连接客户端 by pike, group_request: {}", groupMessage);
        GroupMessageResponse response = PikeMessageServerClient.sendGroupMessage(groupMessage);
        if(response.getStatus() == MessagePushStatusEnum.ACK_SUCCESS.status 
                || response.getStatus() == MessagePushStatusEnum.PIKE_GROUP_PUSH_SUCCESS.status) {
            log.info("群发信息至所有连接客户端 by pike 成功, response: {}", response);
        } else {
            log.error("群发信息至所有连接客户端 by pike 失败, response: {}", response);
        }
    }

    /**
     * 批量发送消息至pike连接集合
     * @param bizId 业务ID
     * @param tokenGroup token集合
     * @param params 参数
     * */
    public void sendBatchMessage(String bizId, List<String> tokenGroup, Map<String, String> params) {
        List<MessageRequest> requestList = tokenGroup.stream()
                .map(item -> MessageRequest.newTokenMessageRequest(bizId, item)).collect(Collectors.toList());

        requestList.parallelStream().forEach(item -> PikeMessageServerClient.sendMessageToSDK(item, new MessageCallback() {
            @Override
            public void onResponse(MessageResponse response) {
                log.info("发送信息至客户端 by pike 成功, response: {}", response);
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("发送信息至客户端 by pike 失败", throwable);
            }
        }));

    }
}
