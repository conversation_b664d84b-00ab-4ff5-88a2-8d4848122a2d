package com.sankuai.walleops.cloud.triage.util;

import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DateUtil {

    public static final String YMD_HMS = "yyyy-MM-dd HH:mm:ss";
    /**
     * 1970-01-01 08:00:01
     */
    public static final Date EPOCH_START = new Date(1000L); // 1970-01-01 08:00:01

    public static final String YMD_HMS_UNSIGNED = "yyyyMMddHHmmss";

    public static final String YMD_HMS_SSS_UNSIGNED = "yyyyMMddHHmmssSSS";

    public static final String YMD = "yyyyMMdd";

    public static final String UTC_FORMATTER = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";

    public static String format(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(YMD_HMS).format(date);
    }

    public static String format(Date date, String pattern) {
        if (date == null || pattern == null) {
            return null;
        }
        return new SimpleDateFormat(pattern).format(date);
    }

    public static Date parseDate(String dateFormat) {
        try {
            return new SimpleDateFormat(YMD_HMS).parse(dateFormat);
        } catch (ParseException e) {
            log.error("parseDate error: {}", dateFormat, e);
        }
        return null;
    }

    /**
     * 获取当前日期的起始时间（即当天的00:00:00.000）
     *
     * @return 当前日期的起始时间
     */
    public static Date getHeadOfToday() {
        Calendar calendar = Calendar.getInstance(); // 获取Calendar实例，表示当前时间
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
        calendar.set(Calendar.MINUTE, 0); // 设置分钟为0
        calendar.set(Calendar.SECOND, 0); // 设置秒为0
        calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0
        return calendar.getTime(); // 返回设置后的时间
    }

    /**
     * 获取指定日期的当天的起始时间（即当天的00:00:00.000）
     *
     * @param date 指定的日期
     * @return 指定日期的当天的起始时间
     */
    public static Date getHeadOfToday(Date date) {
        Calendar calendar = Calendar.getInstance(); // 获取Calendar实例
        calendar.setTime(date); // 设置Calendar的时间为指定的日期
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
        calendar.set(Calendar.MINUTE, 0); // 设置分钟为0
        calendar.set(Calendar.SECOND, 0); // 设置秒为0
        calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0
        return calendar.getTime(); // 返回设置后的时间
    }

    public static Date getSecondsPreviousTime(int seconds) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, -seconds);
        return calendar.getTime();
    }

    public static String replaceTimeChar(String timeStr, boolean startOrEnd) {
        if (StringUtils.isEmpty(timeStr)) {
            return "null";
        }
        // 日期加时分秒
        timeStr = timeStr.replace("-", "").replace(" ", "")
                .replace(":", "");
        if (timeStr.length() == 8) {
            if (startOrEnd) {
                timeStr = timeStr + CommonConstant.HEAD_SECOND;
            } else {
                timeStr = timeStr + CommonConstant.TAIL_SECOND;
            }
        }
        return timeStr;
    }

    /**
     * 获取指定时间某几个时间单位之前的时间
     *
     * @param date
     * @param timeUnit
     * @param num
     * @return
     */
    public static Date getBeforeTime(Date date, TimeUnit timeUnit, Integer num) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        switch (timeUnit) {
            case DAYS:
                localDateTime = localDateTime.minusDays(num);
                break;
            case HOURS:
                localDateTime = localDateTime.minusHours(num);
                break;
            case MINUTES:
                localDateTime = localDateTime.minusMinutes(num);
                break;
            case SECONDS:
                localDateTime = localDateTime.minusSeconds(num);
                break;
            case MILLISECONDS:
                localDateTime = localDateTime.minusNanos(num * 1000000L);
                break;
            case MICROSECONDS:
                localDateTime = localDateTime.minusNanos(num * 1000L);
                break;
            default:
                throw new IllegalArgumentException("不支持的时间单位");
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当前时间之后某几个时间单位的时间
     *
     * @param timeUnit 时间单位
     * @param num      数量
     * @return 当前时间之后某几个时间单位的时间
     */
    public static Date getAfterTime(Date date, TimeUnit timeUnit, Integer num) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        switch (timeUnit) {
            case DAYS:
                localDateTime = localDateTime.plusDays(num);
                break;
            case HOURS:
                localDateTime = localDateTime.plusHours(num);
                break;
            case MINUTES:
                localDateTime = localDateTime.plusMinutes(num);
                break;
            case SECONDS:
                localDateTime = localDateTime.plusSeconds(num);
                break;
            case MILLISECONDS:
                localDateTime = localDateTime.plusNanos(num * 1000000L);
                break;
            case MICROSECONDS:
                localDateTime = localDateTime.plusNanos(num * 1000L);
                break;
            default:
                throw new IllegalArgumentException("不支持的时间单位");
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 将毫秒时间戳转换为秒并四舍五入
     *
     * @param timestampInMillis
     * @return
     */
    public static Long roundAndConvertToSeconds(long timestampInMillis) {
        return Math.round(timestampInMillis / 1000.0);
    }


}
