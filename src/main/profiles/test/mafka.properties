mdp.mafka.consumer[0].bgNameSpace=waimai
mdp.mafka.consumer[0].appkey=com.sankuai.walleops.cloud.triage
mdp.mafka.consumer[0].topicName=walle.data.iiirc.information
mdp.mafka.consumer[0].subscribeGroup=com.sankuai.walleops.cloud.triage
mdp.mafka.consumer[0].listenerId=cloudTriageEventConsumer
#
#
mdp.mafka.consumer[2].bgNameSpace=waimai
mdp.mafka.consumer[2].appkey=com.sankuai.walleops.cloud.triage
mdp.mafka.consumer[2].topicName=walleops.cloud.triage.event.information
mdp.mafka.consumer[2].subscribeGroup=walleops.cloud.triage.event.information.consumer
mdp.mafka.consumer[2].listenerId=cloudTriageEventCommonConsumer
#
#
mdp.mafka.consumer[3].bgNameSpace=waimai
mdp.mafka.consumer[3].appkey=com.sankuai.walleops.cloud.triage
mdp.mafka.consumer[3].topicName=walleeve.common.event.mrm
mdp.mafka.consumer[3].subscribeGroup=walleops.cloud.triage.common.event.mrm.consumer
mdp.mafka.consumer[3].listenerId=commonEventMrmConsumer
#
#
mdp.mafka.producer[0].producerName=infoProducer
mdp.mafka.producer[0].bgNameSpace=waimai
mdp.mafka.producer[0].appkey=com.sankuai.walledata.data.calculator
mdp.mafka.producer[0].topicName=walle.data.iiirc.information
#
#
mdp.mafka.consumer[1].bgNameSpace=waimai
mdp.mafka.consumer[1].appkey=com.sankuai.walleops.cloud.triage
mdp.mafka.consumer[1].topicName=walle.sceneranking.event.information
mdp.mafka.consumer[1].subscribeGroup=protectionSystem_to_cloudTriage
mdp.mafka.consumer[1].listenerId=eventFromPsConsumer
#
#
mdp.mafka.producer[1].producerName=eventMsgProducer
mdp.mafka.producer[1].bgNameSpace=waimai
mdp.mafka.producer[1].appkey=com.sankuai.walleops.cloud.triage
mdp.mafka.producer[1].topicName=cloud.triage.output.event.message
#
#
mdp.mafka.producer[2].producerName=moveCarEventProducer
mdp.mafka.producer[2].bgNameSpace=waimai
mdp.mafka.producer[2].appkey=com.sankuai.wallemonitor.risk.center
mdp.mafka.producer[2].topicName=wallemonitor.risk.event.message

