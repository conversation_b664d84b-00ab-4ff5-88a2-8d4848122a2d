package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum EventOperatorTypeEnum {
    CLOUD_SECURITY(0, "云安全"),
    NEARBY_RESCUE(1, "近场"),
    COCKPIT(2, "坐席");// 云辅助 + 云控

    private final Integer code;
    private final String desc;


    /**
     * 根据code获取desc
     *
     * @param code
     * @return
     */
    public static String getMsgByCode(Integer code) {
        for (EventOperatorTypeEnum typeEnum : EventOperatorTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }

}
