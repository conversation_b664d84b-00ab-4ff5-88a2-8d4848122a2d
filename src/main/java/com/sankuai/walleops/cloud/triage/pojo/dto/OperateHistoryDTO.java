package com.sankuai.walleops.cloud.triage.pojo.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class OperateHistoryDTO {
    private String logSource; //系统来源，CloudTriage-FE
    private String logCategory; //具体页面，TriageDetailPage
    private Integer logType; //业务埋点
    private String logName; //操作，call_remote_control
    private String logTag; //日志标签
    private String bizUniqueKey; //异常id
    private String vin; //车架号
    private Object content; //自定义内容
    private Long logTime; //埋点发生时间，毫秒
    private Long reportTime; //埋点上报时间，毫秒
}
