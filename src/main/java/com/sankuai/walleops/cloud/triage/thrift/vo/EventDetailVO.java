package com.sankuai.walleops.cloud.triage.thrift.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "异常事件详情VO")
public class EventDetailVO {

    @ThriftField(1)
    @FieldDoc(description = "异常ID")
    private String eventId;

    @ThriftField(2)
    @FieldDoc(description = "车架号")
    private String vin;

    @ThriftField(3)
    @FieldDoc(description = "车牌号")
    private String vehicleId;

    @ThriftField(4)
    @FieldDoc(description = "异常时间")
    private Long eventTime;

    @ThriftField(5)
    @FieldDoc(description = "异常类型")
    private Integer eventType;

    @ThriftField(6)
    @FieldDoc(description = "异常类型描述")
    private String eventTypeDes;

    @ThriftField(7)
    @FieldDoc(description = "状态， 0表示未处理，1表示进行中，2表示完成，3表示取消")
    private Integer status;

    @ThriftField(8)
    @FieldDoc(description = "状态描述")
    private String statusDes;


}
