package com.sankuai.walleops.cloud.triage.pojo.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/28
 */
@Data
public class VehicleRealtimeStatusDTO {
    private String vin;
    private String vehicleId;
    private String name;
    private String vehicleType;
    private String vehicleTypeDesc;
    private String park;
    private String parkDesc;
    private String purpose;
    private Double longitude;
    private Double latitude;
    private Double height;
    private Double speed;
    private Integer driveMode;
    private String driveModeDesc;
    private Double mileage;
    private Double enableBattery;
    private Double backupBattery;
    private String diskSysWwn;
    private String diskDataWwn;
    private String autocarVersion;
    private Integer gear;
    private String gearDesc;
    private Integer frontDoorStatus;
    private String frontDoorDesc;
    private Integer rearDoorStatus;
    private String rearDoorDesc;
    private Integer cockpitStatus;
    private String cockpitStatusDesc;
    private Double wspOdoMeter;
    private Integer subwaybillStatus;
    private String subwaybillStatusDesc;
    private Integer tripStatus;
    private String tripStatusDesc;
    private String currentTripNodeCode;
    private String currentTripNodeDesc;
    private String nextTripNodeCode;
    private String nextTripNodeDesc;
    private Integer accidentStatus;
    private String accidentStatusDesc;
    private Integer isVhrMultiple; // VHR是否大于1：0否|1是
    private String isVhrMultipleDesc;
    private Integer faultStatus;
    private String faultStatusDesc;
    private Long updateTimestamp;
}
