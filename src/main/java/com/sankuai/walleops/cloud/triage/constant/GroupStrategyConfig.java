package com.sankuai.walleops.cloud.triage.constant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/** 存放用车目的、事件类型等常量组合
 *
 *
 *
 */
public class GroupStrategyConfig {


    //存放组合1 目的
    public static final List<String> GROUP1_PURPOSE = Arrays.asList(
            "业务运营",
            "业务运营-白领外卖",
            "业务运营-坪山",
            "业务运营-自动化交付",
            "业务探索",
            "业务运营-顺平路站",
            "业务运营-崔各庄东站",
            "业务运营-高新站",
            "业务运营-颐丰",
            "业务运营-后沙峪白领外卖2",
            "业务运营-深圳新澜站",
            "业务运营-新澜"
    );
    //存放组合2 目的
    public static final List<String> GROUP2_PURPOSE = Arrays.asList(
            "路测-测试",
            "研发测试",
            "发布测试",
            "扩路测试",
            "开环测试",
            "高精地图采集",
            "高精地图测试",
            "路测-专项",
            "专用车辆",
            "云控测试",
            "上海支援-运营",
            "上海支援-路测",
            "路测-AB测试",
            "路测-马坡测试",
            "路测-北小营测试",
            "路测-后沙峪测试",
            "路测-亦庄测试",
            "路测-深圳测试",
            "路测-AB后沙峪测试",
            "路测-AB亦庄测试",
            "路测-AB深圳测试",
            "路测-AB校园测试",
            "路测-AB扩路测试",
            "路测-AB后沙峪全流程测试",
            "研发测试-校园VHR测试",
            "提速测试",
            "仿真静态测试",
            "场景采集测试",
            "人机对比测试",
            "Devel-辅路测试",
            "Devel-主路测试",
            "Beta-辅路测试",
            "Beta-主路测试",
            "其他",
            "路测-仁和测试",
            "云辅助专项",
            "路测-扩路测试-深圳",
            "路测-提速测试-深圳",
            "路测-扩路测试-北京",
            "路测-提速测试-北京",
            "路测-云辅助beta-深圳",
            "路测-云辅助beta-北京",
            "其他-车辆改装",
            "其他-车辆维修",
            "其他-车辆标定",
            "其他-车辆功能验证",
            "其他-其他测试",
            "路测-点位测试-深圳",
            "路测-双端测试-北京"

    );
    //列表中的 8， 13， 15 为自动驾驶异常退出的相关事件，需要进行绑定
    //绑定原因请见 CloudTriageEventController 中的 extendCoredumpType 函数
    public static final List<Integer> GROUP1_EVENTTYPE = Collections.unmodifiableList(Arrays.asList(
            //            EventTypeEnum.getCodeByEventName("departure-failed"),// 发车失败
            //            EventTypeEnum.getCodeByEventName("arrive-timeout"),  //到达超时
            //            EventTypeEnum.getCodeByEventName("coredump"),   //  自动驾驶异常退出
            //            EventTypeEnum.getCodeByEventName("autodrive-abnormal-exit"),   //  自动驾驶异常退出
            //            EventTypeEnum.getCodeByEventName("autodrive-process-abnormal-exit"),   //  自动驾驶异常退出

            //            EventTypeEnum.getCodeByEventName("power-warning"),   //  电量告警
            //            EventTypeEnum.getCodeByEventName("roadBlockage-MoveVehicle"),   //  堵路需挪车
            //            EventTypeEnum.getCodeByEventName("autoParking-MeetExpectation"),   //  自动泊车不符合预期
            //            EventTypeEnum.getCodeByEventName("parkingPosition-MoveVehicle"),   //  车辆停靠位置不合理需挪车
            //            EventTypeEnum.getCodeByEventName("riderHoldGoods-Parking"),   //  骑手未取完货需停车
            //            EventTypeEnum.getCodeByEventName("other"),   //  其他
            //            EventTypeEnum.getCodeByEventName("routing-issue"),    //  路由问题
            //            EventTypeEnum.getCodeByEventName("vehicle-standstill"),  //  车辆停滞不前
            EventTypeEnum.getCodeByEventName("trafficCongestion-alert"), //堵路告警
            EventTypeEnum.getCodeByEventName("collision-detection-low"), //  L2-L  事故检测
            EventTypeEnum.getCodeByEventName("collision-detection"),      // L2-H 事故检测
            EventTypeEnum.getCodeByEventName("collision-detection-high"),  // L1   碰撞检测
            //            EventTypeEnum.getCodeByEventName("battery-replace-stop"),       //电池需换电请停车
            //            EventTypeEnum.getCodeByEventName("order-completed-return"),     //订单完成可以返回
            //            EventTypeEnum.getCodeByEventName("vehicle-not-parked-long-time"), //车辆长时间没有驻车
            //            EventTypeEnum.getCodeByEventName("close-cleaning-sensor"),        // 关闭清洁传感器
            //            EventTypeEnum.getCodeByEventName("road-test-departure"),        // 路测发车

            EventTypeEnum.getCodeByEventName("public-removal"),              // 公众挪车
            EventTypeEnum.getCodeByEventName("retrograde"),             // 逆行
            EventTypeEnum.getCodeByEventName("special_area_stranding"),              // 施工区域停滞
            EventTypeEnum.getCodeByEventName("drive_on_traffic_line"),              // 非法压线
            EventTypeEnum.getCodeByEventName("auxiliary-road-to-main-road"), //辅路车借道主路
            EventTypeEnum.getCodeByEventName("main-road-to-auxiliary-road-return"), // 主路车误入辅路后返回
            EventTypeEnum.getCodeByEventName("resume-route-after-return"), //掉头后恢复路由
            EventTypeEnum.getCodeByEventName("pull-over-and-exit-control"), // 靠边停车退控
            EventTypeEnum.getCodeByEventName("scan-code-to-lift-barrier") // 扫码抬杆
    ));

    public static final List<Integer> GROUP2_EVENT_TYPE = Collections.unmodifiableList(Arrays.asList(
            //            EventTypeEnum.getCodeByEventName("vehicle-standstill"),   //  车辆停滞不前
            //            EventTypeEnum.getCodeByEventName("coredump"),   //  自动驾驶异常退出
            //            EventTypeEnum.getCodeByEventName("autodrive-abnormal-exit"),   //  自动驾驶异常退出
            //            EventTypeEnum.getCodeByEventName("autodrive-process-abnormal-exit"),   //  自动驾驶异常退出
            //
            //            EventTypeEnum.getCodeByEventName("roadBlockage-MoveVehicle"),   //  堵路需挪车
            //            EventTypeEnum.getCodeByEventName("autoParking-MeetExpectation"),   //  自动泊车不符合预期
            //            EventTypeEnum.getCodeByEventName("parkingPosition-MoveVehicle"),   //  车辆停靠位置不合理需挪车
            //            EventTypeEnum.getCodeByEventName("other"),   //  其他
            //            EventTypeEnum.getCodeByEventName("routing-issue"),   //  路由问题
            EventTypeEnum.getCodeByEventName("trafficCongestion-alert"), //堵路告警
            EventTypeEnum.getCodeByEventName("collision-detection-low"), // low事故检测
            EventTypeEnum.getCodeByEventName("collision-detection"),      // L2-H 事故检测
            EventTypeEnum.getCodeByEventName("collision-detection-high"),  // L1   碰撞检测
            //            EventTypeEnum.getCodeByEventName("battery-replace-stop"),       //电池需换电请停车
            //            EventTypeEnum.getCodeByEventName("order-completed-return"),     //订单完成可以返回
            //            EventTypeEnum.getCodeByEventName("vehicle-not-parked-long-time"), //车辆长时间没有驻车
            //            EventTypeEnum.getCodeByEventName("close-cleaning-sensor"),        // 关闭清洁传感器
            EventTypeEnum.getCodeByEventName("virtual-order-departure"),       // 虚拟单发车
            //            EventTypeEnum.getCodeByEventName("road-test-departure"),        // 路测发车
            EventTypeEnum.getCodeByEventName("public-removal"),             // 公众挪车
            EventTypeEnum.getCodeByEventName("retrograde"),             // 逆行
            EventTypeEnum.getCodeByEventName("special_area_stranding"),              // 施工区域停滞
            EventTypeEnum.getCodeByEventName("drive_on_traffic_line"),              // 非法压线
            EventTypeEnum.getCodeByEventName("auxiliary-road-to-main-road"), //辅路车借道主路
            EventTypeEnum.getCodeByEventName("main-road-to-auxiliary-road-return"), // 主路车误入辅路后返回
            EventTypeEnum.getCodeByEventName("resume-route-after-return"), //掉头后恢复路由
            EventTypeEnum.getCodeByEventName("pull-over-and-exit-control"), // 靠边停车退控
            EventTypeEnum.getCodeByEventName("scan-code-to-lift-barrier") // 扫码抬杆
    ));



    public static List<String> getMergedPurposeList() {
        List<String> mergedList = new ArrayList<>();
        mergedList.addAll(GROUP1_PURPOSE);
        mergedList.addAll(GROUP2_PURPOSE);
        return mergedList;
    }

    public static List<List<Integer>> getCombineEventTypeList(){
        List<List<Integer>> CombineList = new ArrayList<>();
        CombineList.add(GROUP1_EVENTTYPE);
        CombineList.add(GROUP2_EVENT_TYPE);
        return  CombineList;
    }
}
