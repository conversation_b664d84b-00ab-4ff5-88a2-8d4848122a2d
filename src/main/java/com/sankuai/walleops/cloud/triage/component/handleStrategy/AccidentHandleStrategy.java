package com.sankuai.walleops.cloud.triage.component.handleStrategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.component.CommonFunctionService;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.service.ExternalInterfaceService;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component("collision-detection-high-handle")
@Slf4j
public class AccidentHandleStrategy extends HandleStrategy {

    @MdpConfig("information.center.url.prefix")
    String dcHostName;

    @Resource
    ExternalInterfaceService externalInterfaceService;

    @Resource
    private CommonFunctionService commonFunctionService;

    @Override
    public void process(BizCloudTriageEvent cloudTriageEvent) {
        log.info("AccidentHandleStrategy is processing, param is {}", cloudTriageEvent);
        //对于置信度为L1的碰撞检测信号设置已完成状态
        // setHandleStatusAndEventType(cloudTriageEvent);
        //上报事故到安全中心 -> 信息中心 -> 云分诊
        // reportAccident(cloudTriageEvent);
        // setEventDesc(cloudTriageEvent);
        commonFunctionService.setAutoCancel(cloudTriageEvent);
    }

    private void setEventDesc(BizCloudTriageEvent cloudTriageEvent){
        String remark = cloudTriageEvent.getRemark();
        if (!StringUtils.isEmpty(remark)){
            JSONObject jsonObject = JSON.parseObject(remark);
            jsonObject.put("reportSourceDesc", "系统识别的高置信度碰撞检测");
            cloudTriageEvent.setRemark(jsonObject.toJSONString());
        } else {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("reportSourceDesc", "系统识别的高置信度碰撞检测");
            cloudTriageEvent.setRemark(jsonObject.toJSONString());
        }
    }

    private void setHandleStatusAndEventType(BizCloudTriageEvent cloudTriageEvent){
        cloudTriageEvent.setOperator("system");
        cloudTriageEvent.setStatus(EventStatusEnum.COMPLETED.getCode());
        cloudTriageEvent.setEventType(EventTypeEnum.COLLISION_DETECTION.getCode());
    }

    public void reportAccident(BizCloudTriageEvent cloudTriageEvent){
        String requestUrl   =  dcHostName + "/data-center/accident/add/v3";
        Map<String, Object> param = new HashMap<>();
        param.put("vin", cloudTriageEvent.getVin());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = sdf.format(cloudTriageEvent.getEventTime());
        param.put("accidentTime",formattedDate );
        param.put("checkLatestAccident", false );
        // source的映射关系是维护在 信息中心 的，更改起来比较麻烦
        // source = 5， 表示系统自动识别上报的事故
        param.put("source", 5);
        //自动上报的事故，将四个字段设置为默认值 -1
        param.put("isAnyoneInjured", -1);
        param.put("isBothPartiesAccident",-1);
        param.put("isBothPartiesAccident", -1);
        param.put("collisionSeverity",-1);
        if(cloudTriageEvent.getLongitude() != null && cloudTriageEvent.getLatitude() != null){
            String locationGps = cloudTriageEvent.getLongitude()+","+cloudTriageEvent.getLatitude();
            param.put("locationGps",locationGps);
            param.put("locationName",externalInterfaceService.getLocationNameByGps(locationGps));
        }
        try{
            String response = CommonUtil.doPost(requestUrl, param,null);
            log.info("reportAccident, request = {}, response = {}", param, response);
        }
        catch (Exception e){
            log.error("reportAccident is failed, param = {}",param);
        }

    }

}
