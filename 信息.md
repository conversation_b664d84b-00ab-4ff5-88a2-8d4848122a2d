# 分步指导AI实现需求的方法

## 步骤概述

1. **明确分步意图**：在需求开始时明确表达"请按步骤实现，每步等我确认后再继续"
   
2. **提供总体框架**：先描述整个需求的大致框架和最终目标，但不详细展开所有细节

3. **只详述当前步骤**：每次只详细描述当前需要完成的步骤，保留后续步骤的概述

4. **设定明确边界**：为每个步骤设定清晰的范围界限，如"这一步只关注X功能，不要涉及Y功能"

5. **采用引导式提问**：使用"完成这步后，关于下一步的X问题，你有什么建议？"的方式引导

6. **请求阶段性成果**：明确要求"请先展示X部分的设计/代码，我确认后再实现Y部分"

7. **控制信息量**：每次只提供完成当前步骤所需的信息，避免提供过多后续步骤的细节

8. **主动审核确认**：每个步骤完成后，先审核并确认"这部分已经符合要求，现在我们可以进行下一步"

9. **明确下一步指令**：在确认当前步骤后，明确给出下一步的具体指令

10. **及时纠正偏离**：如果AI开始超出当前步骤范围，立即提醒"请先完成当前步骤，不要超前"

## 示例对话

**用户**：我需要开发一个用户管理系统。请按步骤实现，每步等我确认后再继续。

**AI**：收到，我会按步骤来实现用户管理系统。请问您希望我先从哪个部分开始？

**用户**：整个系统需要实现用户注册、登录、信息管理功能。请先设计用户数据模型。

**AI**：[提供用户数据模型设计]

**用户**：数据模型看起来不错。现在请实现用户注册功能的API接口设计，暂不需要实现具体代码。

**AI**：[提供用户注册API接口设计]

...以此类推
