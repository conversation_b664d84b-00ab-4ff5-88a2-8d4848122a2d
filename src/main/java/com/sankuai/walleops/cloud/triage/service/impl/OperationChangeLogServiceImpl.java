package com.sankuai.walleops.cloud.triage.service.impl;

import com.sankuai.walleops.cloud.triage.dal.entity.OperationChangeLog;
import com.sankuai.walleops.cloud.triage.mapper.OperationChangeLogMapper;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperationLogDTO;
import com.sankuai.walleops.cloud.triage.service.OperationChangeLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class OperationChangeLogServiceImpl implements OperationChangeLogService {

    @Resource
    private OperationChangeLogMapper operationChangeLogMapper;

    /**
     * 记录操作流水日志
     * @param operationLogDTO  操作变更信息结构体
     */
    @Override
    public void insertOperationLog(OperationLogDTO operationLogDTO) {
        OperationChangeLog entity = new OperationChangeLog();
        // 1 参数传递
        entity.setRelatedId(operationLogDTO.getRelatedId());
        entity.setChangeType(operationLogDTO.getChangeType());
        entity.setChangeContent(operationLogDTO.getChangeContent());
        entity.setChangeDesc(operationLogDTO.getChangeDesc());
        entity.setStatus(operationLogDTO.getStatus());
        entity.setOperator(operationLogDTO.getOperator());
        // 2 插入数据
        operationChangeLogMapper.insertSelective(entity);
    }
}
