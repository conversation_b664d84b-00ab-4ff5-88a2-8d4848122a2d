package com.sankuai.walleops.cloud.triage.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class EventMessageDTO {
    /**
     * 异常事件工单唯一ID
     */
    private String eventId;

    /**
     * 异常事件类型
     */
    private Integer eventType;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 异常事件状态
     */
    private Integer status;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 扩展字段
     */
    private Object context;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class contextDTO{
        /**
         * 异常事件类型描述
         */
        private String eventTypeDesc;

        /**
         * 异常事件状态描述
         */
        private String statusDes;

        /**
         *  操作人
         */
        private String operator;

    }
}
