package com.sankuai.walleops.cloud.triage.service.impl;

import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleops.cloud.triage.adaptor.CallCloudOperationServiceAdaptor;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.GrayConfigDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.GrayConfigDTO.GrayConfigItem;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudOperationRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.CockpitHandleService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CockpitHandleServiceImpl implements CockpitHandleService {

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private CallCloudOperationServiceAdaptor cloudOperationServiceAdaptor;

    @ConfigValue(key = "call.cockpit.config", defaultValue = "")
    private GrayConfigDTO grayConfigDTO;

    /**
     * 查询未处理的碰撞检测事件列表
     *
     * @param vin
     * @return
     */
    @Override
    public List<BizCloudTriageEvent> queryUnCheckCollisionDetectionEventList(String vin) {
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        // 指定VIN
        request.setVinList(Collections.singletonList(vin));
        // 所有的碰撞检测事件类型
        request.setEventType(EventTypeEnum.getCollisionDetectionTypeList());
        // 未处理（未检核）
        List<Integer> inStatusList = new ArrayList<>();
        inStatusList.add(EventStatusEnum.INITED.getCode());
        inStatusList.add(EventStatusEnum.HANDLING.getCode());
        request.setInStatusList(inStatusList);
        // 查询云控处置
        request.setOperatorType(EventOperatorTypeEnum.COCKPIT.getCode());
        // 设置查询事件
        // fixme: 修改时间支持配置
        Date startTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, 30);
        request.setStartTime(DatetimeUtil.formatTime(startTime));
        request.setEndTime(DatetimeUtil.formatTime(new Date()));
        return cloudTriageEventService.commonQuery(request);
    }

    /**
     * 判断是否灰度
     *
     * @param eventType
     * @param purpose
     * @return
     */
    @Override
    public Boolean isInGray(Integer eventType, String purpose) {
        if (Objects.isNull(eventType) || Objects.isNull(grayConfigDTO)) {
            return false;
        }
        Map<String, GrayConfigItem> stringGrayConfigItemMap = grayConfigDTO.getConfigs();
        if (MapUtils.isEmpty(stringGrayConfigItemMap) || !stringGrayConfigItemMap.containsKey(
                String.valueOf(eventType))) {
            return false;
        }
        GrayConfigItem grayConfigItem = stringGrayConfigItemMap.get(String.valueOf(eventType));
        return grayConfigItem.isInGrayScope(purpose);
    }

    /**
     * 取消呼叫云控处置
     *
     * @param vin
     * @param eventType
     */
    @Override
    public void requestCloudOperation(String vin, Integer eventType, String action) throws Exception {
        if (StringUtils.isBlank(vin) || Objects.isNull(eventType) || Objects.isNull(grayConfigDTO)) {
            throw new IllegalArgumentException("参数错误");
        }
        Map<String, GrayConfigItem> stringGrayConfigItemMap = grayConfigDTO.getConfigs();
        if (MapUtils.isEmpty(stringGrayConfigItemMap) || !stringGrayConfigItemMap.containsKey(
                String.valueOf(eventType))) {
            throw new IllegalArgumentException("未找到对应的灰度配置");
        }
        GrayConfigItem grayConfigItem = stringGrayConfigItemMap.get(String.valueOf(eventType));
        CloudOperationRequest request = CloudOperationRequest.builder()
                .vin(vin)
                .reason(grayConfigItem.getReason())
                .source(CommonConstant.REQUEST_CLOUD_OPERATION_SOURCE)
                .timestamp(new Date().getTime())
                .action(action)
                .needCancelCommand(true).build();
        cloudOperationServiceAdaptor.requestCloudOperation(request);
    }
}
