package com.sankuai.walleops.cloud.triage.pojo.dto;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class NotificationMessageDTO {

    /**
     * 车架号
     */
    String vin;

    /**
     *  车辆号
     */
    String vehicleName;

    /**
     *  车牌号
     */
    String vehicleId;

    /**
     * 异常事件类型
     */
    Integer eventType;

    /**
     * 工单状态
     */
    Integer status;

    /**
     * 动作
    */
    String action;

    /**
     * 操作时间
     */
    Date operationTime;

    /**
     * 操作人mis
     */
    String operationMisId;

    /**
     * 未更新时长
     */
    Long unUpdateTimeLen;

    /**
     * 事件ID
     */
    String eventId;

    /**
     * 处置方
     */
    Integer operatorType;
}
