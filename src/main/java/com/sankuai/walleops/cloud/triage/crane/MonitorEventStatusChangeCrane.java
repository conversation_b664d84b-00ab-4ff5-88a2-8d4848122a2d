package com.sankuai.walleops.cloud.triage.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.walleops.cloud.triage.adaptor.CheckVehicleConnectionStatusAdaptor;
import com.sankuai.walleops.cloud.triage.adaptor.eve.EVEAdaptor;
import com.sankuai.walleops.cloud.triage.constant.ActionEnum;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.OperationChangeTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.NotificationMessageDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperationLogDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusV2;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.response.VehicleRealtimeV2Response;
import com.sankuai.walleops.cloud.triage.service.AbnormalOrderReportService;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.OperationChangeLogService;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 轮询云分诊事件状态
 *
 * <AUTHOR>
 * @date 2024/05/13
 */
@CraneConfiguration
@Slf4j
public class MonitorEventStatusChangeCrane {

    /**
     * 工单状态未处理的最大超时时长
     */
    @MdpConfig("order.unprocessed.status.max.duration")
    private Integer orderUnprocessedStatusMaxDuration = 5;

    /**
     * 工单状态进行中未处理的最大超时时长
     */
    @MdpConfig("order.uncompleted.status.max.duration")
    private Integer orderUncompletedStatusMaxDuration = 15;


    @Resource
    CloudTriageEventService cloudTriageEventService;

    @Resource
    AbnormalOrderReportService abnormalOrderReportService;

    @Resource
    OperationChangeLogService operationChangeLogService;

    @Resource
    EVEAdaptor eveAdaptor;

    @Resource
    CheckVehicleConnectionStatusAdaptor checkVehicleConnectionStatusAdaptor;

    /**
     * v2接口灰度白名单配置
     */
    @MdpConfig("vehicle.status.v2.interface.gray.vin.set")
    private HashSet<String> v2InterfaceGrayVinSet;

    @Crane("monitor.event.status.change")
    public void monitorEventStatusChange() {
        log.info("monitorEventStatusChangeCrane.monitorEventStatusChange started");

        // 1. 每10s 轮询一次自动呼叫云控处理的工单
        // 查询N分钟之内创建后未处理，M分钟内处理未完成的工单列表
        Date minCreateTime = DateUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, orderUnprocessedStatusMaxDuration);
        Date maxCreateTime = new Date();
        Date minOperateStartTime = DateUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, orderUncompletedStatusMaxDuration);
        Date maxOperateStartTime = new Date();
        List<BizCloudTriageEvent> eventList = cloudTriageEventService.getUnprocessedAndUncompletedEvents(
                minCreateTime,
                maxCreateTime,
                minOperateStartTime,
                maxOperateStartTime);
        if (CollectionUtils.isEmpty(eventList)) {
            log.info("[monitorEventStatusChange] 最近无{}分钟未处理完成/{}分钟处理未完成的分诊工单",
                    orderUnprocessedStatusMaxDuration, orderUncompletedStatusMaxDuration);
            return;
        }
        log.info("[monitorEventStatusChange] {}分钟未处理完成/{}分钟处理未完成的分诊工单 = {}",
                orderUnprocessedStatusMaxDuration,
                orderUncompletedStatusMaxDuration,
                eventList );

        // 2. list<BizCloudTriageEvent> 中找到distinct的vinList，查询状态总线
        List<String> vinList = eventList.stream().map(BizCloudTriageEvent::getVin).distinct()
                .collect(Collectors.toList());

        Map<String, VehicleStatusDTO> vehicleStatusMap = eveAdaptor.getVehicleStatusByDataBus(vinList).stream()
                .collect(Collectors.toMap(VehicleStatusDTO::getVin, Function.identity()));

        // 查询车辆状态v2接口（根据灰度白名单）
        VehicleRealtimeV2Response statusV2BA = null;
        Map<String, VehicleStatusV2> vehicleStatusMapV2 = null;
        try {
            // 过滤出在灰度白名单中的VIN
            List<String> grayVinList = vinList.stream()
                    .filter(this::isInV2InterfaceGray)
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(grayVinList)) {
                log.info("[monitorEventStatusChange] v2接口灰度白名单命中VIN: {}", grayVinList);
                statusV2BA = checkVehicleConnectionStatusAdaptor.getVehicleRealtimeStatusV2BA(grayVinList);
                // 转化为map
                if (Objects.nonNull(statusV2BA)) {
                    vehicleStatusMapV2 = statusV2BA.getData().stream()
                            .collect(Collectors.toMap(VehicleStatusV2::getVin, Function.identity()));
                }
            } else {
                log.info("[monitorEventStatusChange] 无VIN命中v2接口灰度白名单或者接口调用异常，跳过v2接口调用");
            }
        } catch (Exception e) {
            log.error("[monitorEventStatusChange] v2接口调用异常", e);
        }

        // 3. 处理每个工单
        List<String> statusUpdatedEventIDList = Lists.newArrayList();

        for (BizCloudTriageEvent event : eventList) {
            String successProcessEventId = processEvent(event, vehicleStatusMap,vehicleStatusMapV2);
            if (Objects.nonNull(successProcessEventId)) {
                statusUpdatedEventIDList.add(successProcessEventId);
            }
        }
        log.info("[monitorEventStatusChange] 更新了状态的eventIdList:{}",
                JacksonUtils.serialize(statusUpdatedEventIDList));
    }

    /**
     * 处理单个event的状态更新
     *
     * @param event 工单
     * @return
     */
    private String processEvent(BizCloudTriageEvent event, Map<String, VehicleStatusDTO> vehicleStatusMap, Map<String, VehicleStatusV2> vehicleStatusMapV2) {
        String vin = event.getVin();
        Integer beforeStatus = event.getStatus();
        VehicleStatusDTO vehicleStatusDTO = vehicleStatusMap.get(vin);
        Integer updatedEventStatus = getUpdatedEventStatus(event, vehicleStatusDTO,vehicleStatusMapV2);
        if (Objects.isNull(updatedEventStatus)) {
            return null;
        }
        event.setStatus(updatedEventStatus);
        // 维护处置时间
        // 当将工单状态更新为已完成时，设置操作结束时间
        if(updatedEventStatus.equals(EventStatusEnum.COMPLETED.getCode())){
            event.setOperateEndTime(new Date());
        }
        // 当将工单状态更新处理中时，设置操作开始时间
        else if(updatedEventStatus.equals(EventStatusEnum.HANDLING.getCode())){
            event.setOperateStartTime(new Date());
        }

        log.info("processEvent# eventId: {}, vin: {}, 状态变更: {} -> {}",
                event.getEventId(), event.getVin(), beforeStatus, updatedEventStatus);

        // 只有云控连接的时候需要更新操作人，检测到退控时云控操作人为空
        if (Objects.nonNull(vehicleStatusDTO) && Objects.nonNull(vehicleStatusDTO.getRemoteControlMis())) {
            String remoteControlMis = vehicleStatusDTO.getRemoteControlMis();
            event.setOperator(remoteControlMis);
            log.info("processEvent# eventId: {}, vin: {}, 设置云控操作人: {}",
                    event.getEventId(), event.getVin(), remoteControlMis);
        }
        try {
            // 更新状态
            Boolean result = cloudTriageEventService.updateBizCloudTriageEvent(event);
            if (result) {
                // 记录流水
                operationChangeLogService.insertOperationLog(OperationLogDTO.builder().relatedId(event.getId())
                        .changeType(OperationChangeTypeEnum.UPDATE_ORDER.getType())
                        .changeContent(CommonUtil.getChangeDiff(EventStatusEnum.getMsgByCode(beforeStatus)
                                , EventStatusEnum.getMsgByCode(updatedEventStatus)))
                        .status(event.getStatus())
                        .operator(event.getOperator()).build());
            }
            // 发送大象notification
            sendEventStatusUpdateNotification(beforeStatus, event);
            return event.getEventId();
        } catch (Exception e) {
            log.error("[monitorEventStatusChange] processEvent failed, eventID：{}, e:", event.getEventId(), e);
        }
        return null;
    }

    /**
     * 根据工单当前状态和坐席连接状态判断更新后的工单状态
     *
     * @param event            工单(非null)
     * @param vehicleStatusDTO 车辆状态
     * @param vehicleStatusMapV2       v2接口响应数据map
     * @return 更新后的工单状态, 如果不更新返回null
     */
    private Integer getUpdatedEventStatus(BizCloudTriageEvent event, VehicleStatusDTO vehicleStatusDTO,Map<String, VehicleStatusV2> vehicleStatusMapV2 ) {
        // 1. 获取连接状态（优先v2，降级EVE）
        Boolean isConnected = getRemoteControlConnectionStatus(event.getVin(), vehicleStatusDTO, vehicleStatusMapV2);

        // 2. 根据连接状态计算新的事件状态
        return calculateNewEventStatus(event.getStatus(), isConnected);
    }

    /**
     * 获取远程控制连接状态（优先v2接口，降级EVE数据总线）
     *
     * @param vin              车辆VIN码
     * @param vehicleStatusDTO EVE数据总线车辆状态
     * @param vehicleStatusMapV2       v2接口响应数据map
     * @return 连接状态，null表示无法获取状态
     */
    private Boolean getRemoteControlConnectionStatus(String vin, VehicleStatusDTO vehicleStatusDTO, Map<String, VehicleStatusV2> vehicleStatusMapV2) {
        // 优先使用v2接口
        if (Objects.nonNull(vehicleStatusMapV2) && vehicleStatusMapV2.containsKey(vin)) {
            Boolean v2Status = getConnectionStatusFromV2(vin, vehicleStatusMapV2.get(vin));
            if (v2Status != null) {
                return v2Status;
            }
        }
        // 降级到EVE数据总线
        return getConnectionStatusFromEVE(vin, vehicleStatusDTO);
    }

    /**
     * 从v2接口获取连接状态
     *
     * @param vin        车辆VIN码
     * @param vehicleStatusV2 v2接口响应数据
     * @return 连接状态，null表示无法从v2接口获取状态
     */
    private Boolean getConnectionStatusFromV2(String vin, VehicleStatusV2 vehicleStatusV2) {

        // 查找当前车辆的v2状态数据
        if (Objects.isNull(vehicleStatusV2)) {
            return null;
        }
        // 使用坐席状态映射
        Boolean isConnected = checkVehicleConnectionStatusAdaptor.isCockpitConnected(vehicleStatusV2.getCockpitStatus()) ;
        log.info("使用v2坐席状态判断连接状态，vin: {}, 坐席状态: {}, 连接状态: {}",
                vin, vehicleStatusV2.getCockpitStatus().getValue(), isConnected);
        return isConnected;
    }

    /**
     * 从EVE数据总线获取连接状态
     *
     * @param vin              车辆VIN码
     * @param vehicleStatusDTO EVE数据总线车辆状态
     * @return 连接状态，null表示无法从EVE获取状态
     */
    private Boolean getConnectionStatusFromEVE(String vin, VehicleStatusDTO vehicleStatusDTO) {
        log.info("v2坐席状态不可用，降级到EVE数据总线，vin: {}", vin);

        if (Objects.isNull(vehicleStatusDTO) || Objects.isNull(vehicleStatusDTO.getMonitorCompute())) {
            log.info("未获取到云控连接状态");
            return null;
        }

        Boolean isConnected = vehicleStatusDTO.getMonitorCompute().getIsRemoteControlConnected();
        if (Objects.isNull(isConnected)) {
            log.info("未获取到云控连接状态");
            return null;
        }
        return isConnected;
    }

    /**
     * 根据当前事件状态和连接状态计算新的事件状态
     *
     * @param currentStatus 当前事件状态
     * @param isConnected   连接状态
     * @return 新的事件状态，null表示不需要更新
     */
    private Integer calculateNewEventStatus(Integer currentStatus, Boolean isConnected) {
        if (isConnected == null) {
            return null;
        }
        // 未处理 + 已连接 -> 处理中
        if (Objects.equals(EventStatusEnum.INITED.getCode(), currentStatus) && isConnected) {
            return EventStatusEnum.HANDLING.getCode();
        }

        // 处理中 + 未连接 -> 已完成
        if (Objects.equals(EventStatusEnum.HANDLING.getCode(), currentStatus) && !isConnected) {
            return EventStatusEnum.COMPLETED.getCode();
        }
        return null;
    }



    /**
     * 发送事件状态更新通知
     */
    private void sendEventStatusUpdateNotification(Integer oldStatus, BizCloudTriageEvent event) {
        if (Objects.equals(oldStatus, event.getStatus())) {
            log.info("前后状态无变化，无需通知");
            return;
        }
        String operationMisId = event.getOperator();
        String vehicleName = abnormalOrderReportService.getVehicleNameFromEventId(event.getEventId());
        String action = ActionEnum.determineAction(oldStatus, event.getStatus());
        NotificationMessageDTO notificationMessageDTO = NotificationMessageDTO.builder()
                .vin(event.getVin())
                .vehicleName(vehicleName)
                .vehicleId(event.getVehicleId())
                .eventType(event.getEventType())
                .status(event.getStatus())
                .action(action)
                .operationTime(event.getUpdateTime())
                .operationMisId(operationMisId)
                .build();
        // 发送消息给工单上报人
        abnormalOrderReportService.sendProcessMessageToReporter(event.getReporter(), notificationMessageDTO);
    }

    /**
     * 判断VIN是否在v2接口灰度白名单中
     *
     * @param vin 车辆VIN码
     * @return true表示在白名单中，false表示不在白名单中
     */
    private Boolean isInV2InterfaceGray(String vin) {
        // 1. 检查是否配置了灰度白名单
        if (CollectionUtils.isEmpty(v2InterfaceGrayVinSet) || StringUtils.isBlank(vin)) {
            return false;
        }

        // 2. 检查是否全量发布，或者VIN命中灰度白名单
        return (v2InterfaceGrayVinSet.contains(CommonConstant.ALL_STRING)
                // VIN必须转大写进行匹配
                || v2InterfaceGrayVinSet.contains(vin.toUpperCase()));
    }

}
