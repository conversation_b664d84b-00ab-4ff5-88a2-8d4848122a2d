package com.sankuai.walleops.cloud.triage.adaptor;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 视频码率变更事件适配器
 */
@Slf4j
@Component
public class BitrateChangeAdaptor {

    @MdpConfig("spiderHostName")
    String HostName;
    @MdpConfig("spider.clientId")
    String clientId;
    @MdpConfig("spider.clientSecret")
    String clientSecret;

    /**
     * 处理视频码率变更事件
     *
     * @param vin
     * @param eventTime
     */
    public void handleBitrateChangeEvent(String vin, Date eventTime) {
        if (StringUtils.isBlank(vin) || eventTime == null) {
            log.error("handleBitrateChangeEvent param error, vin:{}, eventTime:{}", vin, eventTime);
            return;
        }
        //1)  请求环视视频  云端打捞视频是队列，先进先出，首先触发切换码率
        String bitrateChangePath = "/api/customer/v1/vehicle/cmd";
        String url = HostName + bitrateChangePath;
        //接口参数
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("type", "bitrate_change");
        requestParam.put("vin", vin);
        requestParam.put("data", "");
        //BA鉴权参数
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("clientId", clientId);
        headerParam.put("clientSecret", clientSecret);
        headerParam.put("path", bitrateChangePath);
        headerParam.put("vin", vin);
        headerParam.put("type", "bitrate_change");
        try {
            log.info("handleBitrateChangeEvent, requestParam = {}", requestParam);
            String response = CommonUtil.doPostToSpiderCmdPath(url, requestParam, headerParam);
            log.info("handleBitrateChangeEvent, response = {}", response);
            if (StringUtils.isBlank(response)) {
                log.error("handleBitrateChangeEvent, response is null");
                return;
            }
            // 使用 JacksonUtils 解析 response
            JSONObject responseJson = JacksonUtils.from(response, JSONObject.class);
            Integer code = responseJson.getInteger("code");
            if (code == null || code != 0) {
                log.error("handleBitrateChangeEvent, response code is not 0");
            }

        } catch (Exception e) {
            log.error("/api/customer/v1/vehicle/cmd, request is failed", e);
        }
    }
}
