package com.sankuai.walleops.cloud.triage.controller;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleeve.dto.TokenCheckDTO;
import com.sankuai.walleops.cloud.triage.adaptor.ReportMoveCarServiceAdaptor;
import com.sankuai.walleops.cloud.triage.component.WechatAuthAdminService;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.pojo.request.MoveCarRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.VinEncryptRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.pojo.vo.EncryptVinVO;
import com.sankuai.walleops.cloud.triage.pojo.vo.VehicleMoveStatusVO;
import com.sankuai.walleops.cloud.triage.service.AESAdminService;
import com.sankuai.walleops.cloud.triage.service.WeChatAppletMoveCarService;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import com.sankuai.walleops.cloud.triage.util.exception.ParamInputErrorException;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/output/api")
@Slf4j
public class WeChatAppletMoveCarController {

    @Resource
    private WeChatAppletMoveCarService weChatAppletMoveCarService;

    @Resource
    private WechatAuthAdminService wechatAuthAdminService;

    @Resource
    private AESAdminService aesAdminService;

    @Resource
    private ReportMoveCarServiceAdaptor reportMoveCarServiceAdaptor;

    @MdpConfig("moveCar.report.logic.migrate.gray")
    private HashSet<String> grayVehicleIdList;

    @PostMapping("/moveCarEvent/report")
    public CommonResponse createMoveCarEvent(@RequestHeader("token") String token,
            @RequestBody MoveCarRequest request) {
        log.info("createMoveCarEvent, MoveCarRequest = {}", request);
        try {
            // 设置灰度，命中灰度逻辑的车辆执行新逻辑，否则执行新逻辑
            if (isGrayVin(request.getVehicleId())) {
                log.info("createMoveCarEvent, vehicleId = {}, 命中灰度逻辑", request.getVehicleId());
                return reportMoveCarServiceAdaptor.reportMoveCarEvent(request, token);
            }
            // 1 登陆态校验  登陆态-openID
            InputCheckUtil.isNotBlank(token, "用户登陆态不可为空");
            TokenCheckDTO tokenCheckDTO = wechatAuthAdminService.checkToken(token);
            if (Objects.isNull(tokenCheckDTO) || !tokenCheckDTO.getIsValid()) {
                return CommonResponse.builder().ret(HttpStatus.UNAUTHORIZED.value()).msg("登陆态过期，请重新登陆")
                        .build();
            }
            // 2 入参校验 车牌号/异常事件类型
            InputCheckUtil.isNotBlank(request.getVehicleId(), "车牌号不可为空");
            InputCheckUtil.isNotNull(request.getEventType(), "异常事件类型不可为空");
            InputCheckUtil.isNotBlank(request.getMoveCarReason(), "挪车原因不可为空");

            // 3 生成工单
            CommonResponse response = weChatAppletMoveCarService.checkAndCreateMoveCarOrder(request,
                    tokenCheckDTO.getOpenId());
            log.info("createMoveCarEvent, response = {}", response);
            return response;
        } catch (ParamInputErrorException e) {
            return CommonResponse.builder().ret(ResponseCodeEnum.BAD_REQUEST.getCode()).msg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("createMoveCarEvent error ", e);
            return CommonResponse.failed("system error");
        }
    }

    @GetMapping("/moveCarEvent/query")
    public CommonResponse queryMoveCarEvent(@RequestHeader("token") String token, String vehicleId) {
        log.info("queryMoveCarEvent, vehicleId = {}", vehicleId);

        try {
            // 设置灰度，命中灰度逻辑的车辆执行新逻辑，否则执行新逻辑
            if (isGrayVin(vehicleId)) {
                log.info("queryMoveCarEvent, vehicleId = {}, 命中灰度逻辑", vehicleId);
                return reportMoveCarServiceAdaptor.queryMoveCarEvent(vehicleId, token);
            }

            // 1 登陆态校验
            InputCheckUtil.isNotBlank(token, "用户登陆态不可为空");
            TokenCheckDTO tokenCheckDTO = wechatAuthAdminService.checkToken(token);
            if (Objects.isNull(tokenCheckDTO) || !tokenCheckDTO.getIsValid()) {
                return CommonResponse.builder().ret(HttpStatus.UNAUTHORIZED.value()).msg("登陆态过期，请重新登陆")
                        .build();
            }
            // 2 入参校验 车牌号
            InputCheckUtil.isNotBlank(vehicleId, "车牌号不可为空");
            // 3 数据库查询数据
            VehicleMoveStatusVO vehicleMoveStatusVO = weChatAppletMoveCarService.getMoveCarStatusByVehicleId(vehicleId);
            return CommonResponse.success(vehicleMoveStatusVO);
        } catch (ParamInputErrorException e) {
            return CommonResponse.builder().ret(ResponseCodeEnum.BAD_REQUEST.getCode()).msg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("queryMoveCarEvent error ", e);
            return CommonResponse.failed("system error");
        }
    }

    @PostMapping("/vinList/encrypt")
    public CommonResponse encryptVinList(@RequestBody VinEncryptRequest request) {
        log.info("encryptVinList, request = {}", request);
        try {
            // 1 入参校验
            InputCheckUtil.isNotNull(request.getVinList(), "车架号列表不可为空");
            // 2 数据库查询数据
            List<EncryptVinVO> encryptVinVOList = aesAdminService.encryptVinList(request.getVinList());
            return CommonResponse.success(encryptVinVOList);
        } catch (ParamInputErrorException e) {
            return CommonResponse.builder().ret(ResponseCodeEnum.BAD_REQUEST.getCode()).msg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("encryptVinList error ", e);
            return CommonResponse.failed("system error");
        }
    }

    @GetMapping("/getVehicleId")
    public CommonResponse getVehicleIdByEncryptVin(@RequestHeader("token") String token,
            @RequestParam String encryptVin) {
        log.info("getVehicleIdByEncryptVin, encryptVin = {}", encryptVin);

        try {
            // 1 登陆态校验
            InputCheckUtil.isNotBlank(token, "用户登陆态不可为空");
            TokenCheckDTO tokenCheckDTO = wechatAuthAdminService.checkToken(token);
            if (Objects.isNull(tokenCheckDTO) || !tokenCheckDTO.getIsValid()) {
                return CommonResponse.builder().ret(HttpStatus.UNAUTHORIZED.value()).msg("登陆态过期，请重新登陆")
                        .build();
            }
            // 2 入参校验
            InputCheckUtil.isNotBlank(encryptVin, "vin不可为空");
            // 3 数据库查询数据
            String vehicleId = aesAdminService.getVehicleIdByEncryptVin(encryptVin);
            if(StringUtils.isBlank(vehicleId)){
                return CommonResponse.failed("获取车牌号失败");
            }
            return CommonResponse.success(vehicleId);
        } catch (ParamInputErrorException e) {
            return CommonResponse.builder().ret(ResponseCodeEnum.BAD_REQUEST.getCode()).msg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("queryMoveCarEvent error ", e);
            return CommonResponse.failed("system error");
        }
    }

    /**
     * 判断是否灰度车辆
     *
     * @param vehicleId
     * @return
     */
    private Boolean isGrayVin(String vehicleId) {
        // 1 检查是否打开灰度开关
        if (CollectionUtils.isEmpty(grayVehicleIdList) || StringUtils.isBlank(vehicleId)) {
            return false;
        }
        // 2 检查是否全量发布，或者异常事件类型命中灰度开关
        return (grayVehicleIdList.contains(CommonConstant.ALL_STRING)
                //必须要转大写
                || grayVehicleIdList.contains(vehicleId.toUpperCase()));
    }


}
