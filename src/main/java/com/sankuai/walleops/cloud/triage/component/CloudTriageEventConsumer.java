package com.sankuai.walleops.cloud.triage.component;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.mafka.consumer.AbstractMdpListener;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.walleops.cloud.triage.component.pike.PikeServer;
import com.sankuai.walleops.cloud.triage.component.filter.EventFilterChain;
import com.sankuai.walleops.cloud.triage.component.stratery.EventStrategy;
import com.sankuai.walleops.cloud.triage.constant.*;
import com.sankuai.walleops.cloud.triage.pojo.dto.*;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.proxy.SquirrelProxy;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@Component
@Slf4j
public class CloudTriageEventConsumer {

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private InformationApiService informationApiService;

    @Resource
    private Map<String, EventStrategy> eventStrategyMap;

    @Resource
    private PikeServer pikeServer;

    @Resource
    private SquirrelProxy squirrelProxy;

    @MdpConfig("departure_failed_code_filter_switch: true")
    private Boolean departureFailedCodeFilterSwitch = Boolean.TRUE;

    @MdpConfig("departure_failed_code_list: [\"02-021-0007\", \"02-010-0002\", \"02-010-0003\", \"02-021-0001\", \"02-021-0006\"]")
    private String[] departureFailedCodeArray = {"02-021-0007", "02-010-0002", "02-010-0003", "02-021-0001", "02-021-0006"};

    @MdpConfig("event.information.code.filter.list")
    ArrayList<Integer> informationFilterList;

    @MdpConfig("event.filter.list.by.vhr.ii")
    public static volatile ArrayList<Integer> eventTypeFilterListByVhr;

    @Resource
    private EventFilterChain eventFilterChain;

    @MdpMafkaMsgReceive
    public ConsumeStatus consume(String message, AbstractMdpListener.MdpMqContext context) {
        InformationDTO informationDTO;

        try {
            informationDTO = JSON.parseObject(message, InformationDTO.class);
            log.info("msgId: {}, receive information: {}", context.getMessage().getMessageID(),
                    informationDTO.printMsg());

            if(eventFilterChain.doFilter(informationDTO)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            if(filterDepartureFailedCode(informationDTO)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

        } catch (Exception e) {
            log.error("parse cloud triage message error: {}", message, e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // todo:  屏蔽 1001 车辆停滞不前，1004 到达超时，2002 电量告警 以及 事故检测 2001事件（从保障中心获取）
        // informationFilterList，后续过滤需求可通过lion配置即可
        Integer informationCode = informationDTO.getInformationCode();
        for(Integer code : informationFilterList){
            if(InformationTypeEnum.getByCode(code).getCode().equals(informationCode)){
                log.info("informationCode is {} , filtered", informationCode );
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }

        BizCloudTriageEvent cloudTriageEvent = null;
        try {

            InformationTypeEnum informationTypeEnum = InformationTypeEnum.getByCode(informationCode);
            EventTypeEnum eventTypeEnum = null;
            if(informationTypeEnum != null) {
                eventTypeEnum = EventTypeEnum.valueOf(informationTypeEnum.name());
            }

            if (informationTypeEnum == null || eventTypeEnum == null) {
                log.info("informationTypeEnum is empty: {}, {}", informationCode, informationDTO.getInformationId());
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            EventStrategy eventStrategy = eventStrategyMap.get(informationTypeEnum.name().toLowerCase());
            cloudTriageEvent = eventStrategy.transferEvent(informationDTO, eventTypeEnum);
        } catch (Exception e) {
            log.error("transfer data error: {}, {}", informationDTO, cloudTriageEvent, e);
        }
        if (cloudTriageEvent == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //根据vhr的屏蔽，在lion中设置事件的过滤
        String vin  = cloudTriageEvent.getVin();
        if(StringUtils.isBlank(vin)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        Boolean isVhrMultiple = informationApiService.queryIsVhrMultipleByVin(vin);
        if(eventTypeFilterListByVhr.contains(informationCode) && !isVhrMultiple){
            log.info("vhr <= 1 filter, cloudTriageEvent = {}", cloudTriageEvent);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        cloudTriageEvent.setInformationId(informationDTO.getInformationId());

        addExtraInfo(cloudTriageEvent);
        try {
            if(InformationTypeEnum.ACCIDENT.getCode().equals(informationCode)) {
                int result = cloudTriageEventService.upsertAccident(cloudTriageEvent);
                if(result < 1) {
                    log.info("upsert accident failed, CloudTriageEvent: {}", cloudTriageEvent);
                }
            } else {
                int result = cloudTriageEventService.save(cloudTriageEvent);
                // 事故数据存在业务库，有可能会不断更新
                if (result < 1 && InformationTypeEnum.ACCIDENT.getCode().equals(informationCode)) {
                    cloudTriageEventService.updateOnly(cloudTriageEvent);
                }
            }

            // 触发新事件通知
            pikeServer.notifyNewEvent(cloudTriageEvent);
        } catch (Exception e) {
            log.error("save cloudTriageEvent error: {}", cloudTriageEvent, e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }



    public void addExtraInfo(BizCloudTriageEvent cloudTriageEvent) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("vinList", cloudTriageEvent.getVin());
            List<VehicleRealtimeStatusDTO> resultList = informationApiService.callVehicleStatusService(param);
            if (!resultList.isEmpty()) {
                VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = resultList.get(0);
                cloudTriageEvent.setPurpose(vehicleRealtimeStatusDTO.getPurpose());
                cloudTriageEvent.setPlace(vehicleRealtimeStatusDTO.getPark());

                // 存储 VHR>1车辆的碰撞检测类型事件 至Squirrel中
                if(EventTypeEnum.COLLISION_DETECTION.getCode().equals(cloudTriageEvent.getEventType())
                    && vehicleRealtimeStatusDTO.getIsVhrMultiple() != null
                    && vehicleRealtimeStatusDTO.getIsVhrMultiple() == 1) {
                    String field = cloudTriageEvent.getEventId();
                    long value = cloudTriageEvent.getEventTime().getTime();
                    squirrelProxy.hset(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY, CommonConstant.KEY_ACCIDENT_HASH, field, value);
                }
            }
        } catch (Exception e) {
            log.error("query information purpose and place error: {}", cloudTriageEvent);
        }
    }

    /**
     * @param information
     * @return true: 过滤当前消息，false: 不过滤当前消息
     * */
    private Boolean filterDepartureFailedCode(InformationDTO information) {
        // 如果未开启过滤开关(false)，返回false，不过滤当前消息
        if(departureFailedCodeFilterSwitch == Boolean.FALSE) {
            return Boolean.FALSE;
        }

        if(information.getInformationCode().equals(InformationTypeEnum.DEPARTURE_FAILED.getCode())) {
            DepartureFailedEventDTO departureFailedEventDTO =
                    JSON.parseObject(information.getData(), DepartureFailedEventDTO.class);

            // 发车失败处置码白名单：02-021-0007, 02-010-0002, 02-010-0003, 02-021-0001, 02-021-0006
            List<String> departureFailedCodeList = Arrays.asList(departureFailedCodeArray);
            String code = departureFailedEventDTO.getCode();

            // 如果处置码不在白名单中，过滤此消息
            if(!departureFailedCodeList.contains(code)) {
                return Boolean.TRUE;
            } else {
                try {
                    Map<String, Object> param = new HashMap<>();
                    param.put("vinList", departureFailedEventDTO.getVin());
                    List<VehicleRealtimeStatusDTO> resultList = informationApiService.callVehicleStatusService(param);
                    if(!resultList.isEmpty()) {
                        VehicleRealtimeStatusDTO realtimeStatusDTO = resultList.get(0);
                        // 如果处置码在白名单中 & 坐席状态为已连接，那么过滤此消息
                        if(realtimeStatusDTO.getCockpitStatus().intValue() == 2) {
                            return Boolean.TRUE;
                        }
                    }
                } catch (Exception e) {
                    log.error("查询信息中心失败", e);
                }
            }
        }

        return Boolean.FALSE;
    }

}
