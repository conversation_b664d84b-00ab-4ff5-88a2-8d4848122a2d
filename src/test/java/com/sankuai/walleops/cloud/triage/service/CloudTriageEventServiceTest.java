package com.sankuai.walleops.cloud.triage.service;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.walleops.cloud.triage.BaseTest;
import com.sankuai.walleops.cloud.triage.component.EventFromPsConsumer;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;

//@Ignore
@ContextConfiguration(classes = CloudTriageEventServiceTest.TestConfig.class)
@TestPropertySource(properties = {
        "mdp.mafka.producer[0].producerName=eventMsgProducer",
        "mdp.mafka.producer[0].bgNameSpace=waimai",
        "mdp.mafka.producer[0].appkey=com.sankuai.walleops.cloud.triage",
        "mdp.mafka.producer[0].topicName=cloud.triage.output.event.message",
        "mdp.mafka.producer[2].producerName=moveCarEventProducer",
        "mdp.mafka.producer[2].bgNameSpace=waimai",
        "mdp.mafka.producer[2].appkey=com.sankuai.wallemonitor.risk.center",
        "mdp.mafka.producer[2].topicName=wallemonitor.risk.event.message"
})
public class CloudTriageEventServiceTest extends BaseTest {

    @Configuration
    static class TestConfig {
        @Bean(name = "eventMsgProducer")
        public IProducerProcessor<?, ?> eventMsgProducer() {
            return Mockito.mock(IProducerProcessor.class);
        }

        @Bean(name = "moveCarEventProducer")
        public IProducerProcessor<?, ?> moveCarEventProducer() {
            return Mockito.mock(IProducerProcessor.class);
        }
    }

    @Resource
    CloudTriageEventService cloudTriageEventService;

    @Resource
    private EventFromPsConsumer eventFromPsConsumer;

    @Test
    public void queryLatestEventDetailByUniqueKey() {
        BizCloudTriageEvent result = cloudTriageEventService.queryLatestEventDetailByUniqueKey("2025", new Date());
        System.out.println("result: " + result);
    }

    @Test
    public void testPageQueryWithOperationTypeAndMinUpdateTime() {
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        request.setInStatusList(EventStatusEnum.getUnCompletedStatus());
        request.setOperationType(1);
        request.setMinUpdateTime("2024-05-14 14:55:00");
        List<BizCloudTriageEvent> eventList = cloudTriageEventService.pageQuery(request);
        System.out.println(JacksonUtils.serialize(eventList.stream().map(BizCloudTriageEvent::getEventId).collect(
                Collectors.toList())));

    }

    @Test
    public void queryByUpdateTimeRangeAndOperationTypeTest() {
        List<BizCloudTriageEvent> eventList = cloudTriageEventService.queryByUpdateTimeRangeAndOperationType(
                1,
                Arrays.asList(EventStatusEnum.INITED.getCode()),
                new Date(),
                DateUtil.getHeadOfToday(),
                EventOperatorTypeEnum.CLOUD_SECURITY.getCode());
        System.out.println("eventList = " + eventList);
    }

    @Test
    public void testConsumer() {
        eventFromPsConsumer.consume("", null);
    }

    @Resource
    private CockpitHandleService cockpitHandleService;

    @Test
    public void testQueryByEventId() throws Exception {
        cockpitHandleService.requestCloudOperation("LA71AUB18S0508638", 23,
                CommonConstant.CANCEL_CLOUD_OPERATION_ACTION);
    }

}
