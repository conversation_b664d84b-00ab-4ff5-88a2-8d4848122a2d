package com.sankuai.walleops.cloud.triage.service.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleops.cloud.triage.component.LockService;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.VideoDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.config.NearbyRescueHandleConfig;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.CollisionDetectionHandleService;
import com.sankuai.walleops.cloud.triage.service.VideoService;
import com.sankuai.walleops.cloud.triage.thrift.vo.NearbyRescueCollisionDetectionDetailVO;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import com.sankuai.walleops.cloud.triage.util.exception.ParamInputErrorException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class NearbyRescueHandleServiceImpl implements CollisionDetectionHandleService {

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private LockService lockService;

    @Resource
    private VideoService videoService;

    @ConfigValue(key = "nearby.rescue.handle.config", defaultValue = "")
    private NearbyRescueHandleConfig handleConfig;

    /**
     * 近场处理碰撞检测事件
     *
     * @param cloudTriageEvent
     */
    @Override
    public void handleCollisionDetectionEvent(BizCloudTriageEvent cloudTriageEvent) {
        if (cloudTriageEvent == null) {
            return;
        }
        // todo: 近场处置，一辆车同一时间只允许一个碰撞存在
        // 全量查询当前车辆一段时间内是否存在未结束的碰撞检测事件
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        // 查询条件：vin + 时间范围 + 事件类型 + 状态
        request.setVinList(Collections.singletonList(cloudTriageEvent.getVin()));
        Date startTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, handleConfig.getQueryRangeMinS());
        request.setStartTime(DatetimeUtil.formatTime(startTime));
        request.setEndTime(DatetimeUtil.formatTime(new Date()));
        request.setEventType(EventTypeEnum.getCollisionDetectionTypeList());
        request.setInStatusList(EventStatusEnum.getUnCompletedStatus());
        List<BizCloudTriageEvent> collisionDetectionEventList = cloudTriageEventService.commonQuery(request);
        if (CollectionUtils.isNotEmpty(collisionDetectionEventList)) {
            cloudTriageEvent.setStatus(EventStatusEnum.CANCELED.getCode());
        }
        try {
            int result = cloudTriageEventService.save(cloudTriageEvent);
            if (result < 1) {
                log.info("database write failure! cloudTriageEvent = {}", cloudTriageEvent);
            }
            //当数据插入成功后，开始触发高清视频打捞
            startHDVideoSalvage(cloudTriageEvent.getVin(), cloudTriageEvent.getEventTime());
            //            pikeServer.notifyNewEvent(cloudTriageEvent);
        } catch (Exception e) {
            log.error("save cloudTriageEvent error: {}", cloudTriageEvent, e);
        }

    }

    /**
     * 查询当前车辆最近30分钟内未处理的碰撞检测事件
     *
     * @param vin
     * @return
     */
    @Override
    public NearbyRescueCollisionDetectionDetailVO queryRealtimeCollisionDetectionEvent(String vin) {
        // 查询当前事件
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        request.setVinList(Collections.singletonList(vin));
        request.setEventType(EventTypeEnum.getCollisionDetectionTypeList());
        request.setInStatusList(EventStatusEnum.getUnCompletedStatus());
        // 查询近场处置
        request.setOperatorType(EventOperatorTypeEnum.NEARBY_RESCUE.getCode());
        Date startTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, handleConfig.getQueryRangeMinS());
        request.setStartTime(DatetimeUtil.formatTime(startTime));
        request.setEndTime(DatetimeUtil.formatTime(new Date()));
        request.setPage(1);
        request.setPage(1);
        List<BizCloudTriageEvent> bizCloudTriageEventList = cloudTriageEventService.commonQuery(request);
        if (org.springframework.util.CollectionUtils.isEmpty(bizCloudTriageEventList)) {
            return null;
        }
        if (bizCloudTriageEventList.size() > 1) {
            log.error("vin = {}, 查询到多个事件", vin);
        }
        BizCloudTriageEvent event = bizCloudTriageEventList.get(0);

        return NearbyRescueCollisionDetectionDetailVO.builder()
                .eventId(event.getEventId())
                .eventType(event.getEventType())
                .eventTypeDes(EventTypeEnum.getMsgByCode(event.getEventType()))
                .vin(event.getVin())
                .vehicleId(event.getVehicleId())
                .status(event.getStatus())
                .statusDes(EventStatusEnum.getMsgByCode(event.getStatus()))
                .eventTime(event.getEventTime().getTime() / 1000)
                .createTime(event.getCreateTime().getTime() / 1000)
                .build();
    }

    /**
     * 更新碰撞检测事件状态
     *
     * @param request
     * @return
     */
    @Override
    public ResponseCodeEnum updateCollisionDetectionEvent(CloudTriageEventUpdateRequest request) throws Exception {

        String eventId = request.getEventId();
        Integer status = request.getStatus();
        BizCloudTriageEvent event = cloudTriageEventService.queryAllByUniqueKey(eventId, null);
        InputCheckUtil.isNotNull(event, "事件不存在");
        InputCheckUtil.isNotTrue(status <= event.getStatus(), "事件状态不可回退");
        if (!lockService.lock(CommonConstant.EVENT_QU_LOCK, eventId, CommonConstant.LOCK_EXPIRE_SECONDS)) {
            return ResponseCodeEnum.OPERATING_BY_OTHERS;
        }
        try {
            event = cloudTriageEventService.queryAllByUniqueKey(eventId, null);
            if (status <= event.getStatus()) {
                return ResponseCodeEnum.STATUS_NOT_ROLLBACK;
            }
            // 更新事件状态
            int result = cloudTriageEventService.queryThenUpdate(request);
            if (result < 1) {
                return ResponseCodeEnum.FAILED;
            }
            // 创建mq并发送消息
            // 只对状态发生变化的更新操作进行输出，防止重复发送mq以及修改车辆历史异常工单状态对 当前车辆状态发生影响
            if (!Objects.equals(request.getStatus(), event.getStatus())) {
                cloudTriageEventService.createAndSendMQ(event, EventStatusEnum.getByCode(request.getStatus()),
                        CommonConstant.SYSTEM);
            }
            return ResponseCodeEnum.SUCCESS;
        } catch (Exception e) {
            log.error("queryThenUpdate error: {}", eventId, e);
        } finally {
            lockService.unLock(CommonConstant.EVENT_QU_LOCK, eventId);
        }
        return ResponseCodeEnum.FAILED;
    }

    /**
     * 触发高清视频打捞
     *
     * @param vin
     * @param eventTime
     */
    public void startHDVideoSalvage(String vin, Date eventTime) throws ParamInputErrorException {
        // 1）触发视频码率切换， todo 历史逻辑
        InputCheckUtil.isNotEmpty(handleConfig.getVideoRequestDurationList(), "视频请求时长配置为空");
        List<Integer> videoRequestDuration = handleConfig.getVideoRequestDurationList();
        // todo : 为什么不直接使用上文中的事故时间，因为该值与数据库值不统一，而前端是基于数据库中的值进行请求，因为这里读取数据库保持一致
        long accidentTime = DateUtil.roundAndConvertToSeconds(eventTime.getTime());
        //2）请求完整时间段的高清环视视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(0),
                accidentTime, Arrays.asList("v_loop"), true);
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(0),
                accidentTime + videoRequestDuration.get(1), Arrays.asList("v_loop"), true);
        //3）请求完整时间段的高清拼接视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(2),
                accidentTime, Arrays.asList("v_concat"), true);
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(2),
                accidentTime + videoRequestDuration.get(3), Arrays.asList("v_concat"), true);
    }

    /**
     * 请求高清视频
     *
     * @param vin
     * @param start_ts
     * @param end_ts
     * @param perspectives
     * @param immediateRetrieve
     */
    private void requestHDVideoByType(String vin, Long start_ts, Long end_ts, List<String> perspectives,
            Boolean immediateRetrieve) {
        VideoDTO videoDTO = VideoDTO.builder()
                .vin(vin)
                .start_ts(start_ts)
                .end_ts(end_ts)
                .position(perspectives)
                .immediateRetrieve(immediateRetrieve).build();
        videoService.getHDVideo(videoDTO);
    }
}
