package com.sankuai.walleops.cloud.triage.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Set;

@Data
public class TrafficJamEndEventContentDTO {
    @JSONField(name = "start_time")
    private Data startTime;

    @JSONField(name = "recall_time")
    private Data recallTime;

    @JSONField(name = "end_time")
    private Data endTime;

    @JSONField(name = "in_junction")
    private Integer inJunction;

    @JSONField(name = "x")
    private double x;

    @JSONField(name = "y")
    private double y;

    @JSONField( name = "initial_vehicle_set" )
    Set<String> initialVehicleSet;

    @JSONField( name = "recall_vehicle_set" )
    Set<String> recallVehicleSet;

    @JSONField( name = "accumulate_vehicle_set" )
    Set<String> accumulateVehicleSet;

}
