package com.sankuai.walleops.cloud.triage.adapter;

import com.sankuai.walleops.cloud.triage.BaseTest;
import com.sankuai.walleops.cloud.triage.adaptor.CheckVehicleConnectionStatusAdaptor;
import com.sankuai.walleops.cloud.triage.pojo.response.VehicleRealtimeV2Response;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class CheckVehicleConnectionStatusAdaptorTest extends BaseTest {

    @Resource
    private CheckVehicleConnectionStatusAdaptor checkVehicleConnectionStatusAdaptor;

    @Test
    public void testGetVehicleRealtimeStatusV2BA_Success() {
        List<String> vinList = Arrays.asList("lmtzsv027mc042469","LMTZSV021NC091894");

        // 执行测试
        VehicleRealtimeV2Response response = checkVehicleConnectionStatusAdaptor.getVehicleRealtimeStatusV2BA(vinList);
        log.info("response:{}", response);

    }
}