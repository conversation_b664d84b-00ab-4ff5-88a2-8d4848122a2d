package com.sankuai.walleops.cloud.triage.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DataSourceConfigConstant {
    /**
     * ops数据源
     */
    public static final String OPS_DATASOURCE = "opsDataSource";
    /**
     * ops数据源
     */
    public static final String OPS_MAPPER_PATH = "classpath*:/ops/mapper/*.xml";
    /**
     * ops sqlSessionFactory
     */
    public static final String OPS_SQL_SESSION_FACTORY = "opsSqlSessionFactory";
    /**
     * ops template
     */
    public static final String OPS_TEMPLATE = "opsSqlSessionTemplate";
}
