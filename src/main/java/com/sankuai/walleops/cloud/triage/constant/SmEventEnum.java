package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SmEventEnum {
    /*
           该枚举值用于状态监控系统传递的消息类型，目前只从状态监控系统接收 碰撞检测 事件（sm 表示状态监控）
     */

    COLLISION_DETECTION(1001, "事故检测",1),
    COLLISION_DETECTION_GRADE(1005, "事故检测",1);

    private final Integer code;
    private final String msg;
    private final Integer type;

    public static Integer getTypeByCode(Integer code) {
        for (SmEventEnum event : SmEventEnum.values()) {
            if (event.getCode().equals(code)) {
                return event.getType();
            }
        }
        return null; // 如果找不到对应的 code 值，则返回 null
    }

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static SmEventEnum getByCode(Integer code) {
        for (SmEventEnum event : SmEventEnum.values()) {
            if (event.getCode().equals(code)) {
                return event;
            }
        }
        return null;
    }
}
