package com.sankuai.walleops.cloud.triage.adaptor;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.carosscan.request.DxGroupMsgSendRequest;
import com.sankuai.carosscan.response.DxGroupCreateResponse;
import com.sankuai.carosscan.service.IThriftDxNoticeService;
import com.sankuai.walleeve.commons.enums.ErrorCode.EveStatusCode;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.JacksonUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DXMessageAdaptor {

    @MdpThriftClient(remoteAppKey = "com.sankuai.carosscan.common.output", timeout = 2000)
    private IThriftDxNoticeService iThriftDxNoticeService;

    /**
     * 发送大象消息
     *
     * @param gid
     * @param msgBody
     */
    public void sendDxMessage(Long gid, String msgBody) {
        try {
            Map<String, Object> msgBodyMap = new HashMap<>();
            msgBodyMap.put("text", msgBody);
            DxGroupMsgSendRequest request = DxGroupMsgSendRequest.builder()
                    .gid(gid)
                    .bodyJson(JacksonUtils.to(msgBodyMap))
                    .messageType("text")
                    .extension("{}")
                    .build();
            log.info("sendDxMessage:{}", request);
            EveThriftResponse<DxGroupCreateResponse> response = iThriftDxNoticeService.sendDxMessage(request);
            log.info("sendDxMessage:{}", response);
            if (Objects.isNull(response) || !Objects.equals(response.getCode(), EveStatusCode.OK.getCode())) {
                throw new RuntimeException("发送大象消息失败");
            }
        } catch (Exception e) {
            log.error("大象通知异常", e);
        }
    }


}
