package com.sankuai.walleops.cloud.triage.component.stratery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.DockingTimeoutEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component("docking_timeout")
public class DockingTimeoutStrategy extends EventStrategy{

    @Override
    public BizCloudTriageEvent transferEvent(InformationDTO information, EventTypeEnum eventTypeEnum) {
        DockingTimeoutEventDTO eventDTO;
        try {
            eventDTO = JSON.parseObject(information.getData(), DockingTimeoutEventDTO.class);
        } catch (Exception e) {
            log.error("information data parse error: {}", information.getData(), e);
            return null;
        }

        Date eventTime = new Date(eventDTO.getEventTimestamp());
        String eventId = CommonUtil.generateEventId(eventTime, eventTypeEnum.getEventName(),
                eventDTO.getVehicleName());

        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
        cloudTriageEvent.setEventId(eventId);
        cloudTriageEvent.setEventTime(eventTime);
        cloudTriageEvent.setVehicleId(eventDTO.getVehicleId());
        cloudTriageEvent.setEventType(eventTypeEnum.getCode());
        cloudTriageEvent.setVin(eventDTO.getVin());
        cloudTriageEvent.setLongitude(eventDTO.getLongitude() == null ? "" : eventDTO.getLongitude().toString());
        cloudTriageEvent.setLatitude(eventDTO.getLatitude() == null ? "" : eventDTO.getLatitude().toString());

        JSONObject remarkJson = new JSONObject();
        remarkJson.put("rule_id", eventDTO.getRuleId() == null ? "" : eventDTO.getRuleId());
        remarkJson.put("vehicle_name", eventDTO.getVehicleName() == null ? "" : eventDTO.getVehicleName());

        String content = eventDTO.getContent();
        if(StringUtils.isNotBlank(content)) {
            JSONObject contentJson = JSON.parseObject(content);

            String tripNodeId = contentJson.getString("trip_node_id");
            remarkJson.put("trip_node_id", StringUtils.isBlank(tripNodeId) ? "" : tripNodeId);

            String tripNodeName = contentJson.getString("trip_node_name");
            remarkJson.put("trip_node_name", StringUtils.isBlank(tripNodeName) ? "" : tripNodeName);

            String datasource = contentJson.getString("datasource");
            remarkJson.put("datasource", StringUtils.isBlank(datasource) ? "" : datasource);
        }
        cloudTriageEvent.setRemark(remarkJson.toJSONString());


        return cloudTriageEvent;
    }
}
