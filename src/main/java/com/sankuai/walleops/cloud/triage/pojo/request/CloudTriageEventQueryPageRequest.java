package com.sankuai.walleops.cloud.triage.pojo.request;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/25
 */
@Data
public class CloudTriageEventQueryPageRequest extends RangeTimePageRequest {
    private Long id;

    /**
     * 事件id
     */
    private String eventId;


    /**
     * 信息id
     */
    private String informationId;

    /**
     * record名称
     */
    private String recordName;

    /**
     * 车架号
     */
    private List<String> vinList;

    /**
     * 车辆id
     */
    private List<String> vehicleIdList;


    /**
     * 事件类型，-1表示未知，0表碰撞检测事件，1表示车辆停止不前，其他待补充
     */
    private List<Integer> eventType;

    /**
     * 状态， 0表示未处理，1表示进行中，2表示完成，3表示取消
     */
    private Integer status;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 用车目的
     */
    private List<String> purpose;

    /**
     * 场地
     */
    private List<String> place;

    private String remark;


    private List<String> groupPurpose;
    private List<Integer> groupEventType;

    /**
     * 命中状态列表
     */
    private List<Integer> inStatusList;

    /**
     * 分诊工单的处理方（见OrderOperationTypeEnum）
     */
    private Integer operationType;

    /**
     * 最小更新时间
     */
    private String minUpdateTime;

    /**
     * 最大更新时间
     */
    private String maxUpdateTime;

    /**
     * 上报人
     */
    private String reporter;

    /**
     * 异常事件发生时间
     */
    private String eventTime;

    /**
     * 处置方
     */
    private Integer operatorType;

    /**
     * 是否发起呼叫
     */
    private Integer mrmCalled;

}
