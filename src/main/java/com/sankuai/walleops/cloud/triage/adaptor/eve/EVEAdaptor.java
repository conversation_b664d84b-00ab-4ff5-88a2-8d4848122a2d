package com.sankuai.walleops.cloud.triage.adaptor.eve;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.response.ExternalSystemResponse;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import com.sankuai.walleops.cloud.triage.util.JsonUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Component
public class EVEAdaptor {

    /**
     * 数据总线服务域名
     */
    @MdpConfig("data.bus.service.host.name")
    String dataBusServiceHostName;

    /**
     * 数据总线服务key (总线服务定制key)
     */
    private static final String CLOUD_TRIAGE_BUS_DATA_KEY = "cloud_triage";

    /**
     * 获取车辆实时数据接口url
     */
    private static final String QUERY_REAL_DATA_URL = "/eve/online/rest/data/bus/batch/get";

    /**
     * 通过vinList获取车辆总线状态
     *
     * @param vinList 车辆列表
     * @return
     */
    public List<VehicleStatusDTO> getVehicleStatusByDataBus(List<String> vinList) {
        String jsonData = getDataFromDataBus(vinList);
        if (StringUtils.isEmpty(jsonData)) {
            return new ArrayList<>();
        }
        List<VehicleStatusDTO> response = JsonUtil.fromJson(jsonData, new TypeReference<List<VehicleStatusDTO>>() {
        });
        log.info("getVehicleStatusByDataBus# response = {}", response);
        return response;
    }

    /**
     * 通过VIN码从数据总线获取单个车辆的状态信息
     *
     * @param vin 车辆的唯一识别码
     * @return VehicleStatusDTO 车辆状态数据传输对象，如果没有找到则返回null
     */
    public VehicleStatusDTO getVehicleStatusFromDataBusByVin(String vin) {
        List<VehicleStatusDTO> vehicleStatusDTOList = getVehicleStatusByDataBus(Collections.singletonList(vin));
        if (CollectionUtils.isEmpty(vehicleStatusDTOList)) {
            return null;
        }
        return vehicleStatusDTOList.get(0);
    }


    /**
     * 从数据总线获取车辆实时状态数据
     *
     * @param vinList 车辆列表
     * @return
     */
    private String getDataFromDataBus(List<String> vinList) {
        Map<String, Object> param = new HashMap<>();
        param.put("key", CLOUD_TRIAGE_BUS_DATA_KEY);
        param.put("vin_list", vinList);
        String url = dataBusServiceHostName + QUERY_REAL_DATA_URL;
        try {
            log.info("getDataFromDataBus, param: {}", param);
            String response = CommonUtil.doPostWithAccessAuth(url, JsonUtil.toJson(param), null);
            log.info("getDataFromDataBus, response: {}", response);

            ExternalSystemResponse externalSystemResponse = JsonUtil.fromJson(response, ExternalSystemResponse.class);
            if (externalSystemResponse.getCode().equals(CommonConstant.RESPONSE_SUCCESS)) {
                return JsonUtil.toJson(externalSystemResponse.getData());
            }
        } catch (Exception e) {
            log.error("getDataFromDataBus error", e);
        }
        return "";
    }
}
