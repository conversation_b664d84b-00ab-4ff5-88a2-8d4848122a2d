package com.sankuai.walleops.cloud.triage.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.walleops.cloud.triage.constant.CollisionDetectionReassignReasonEnum;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.RemarkDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.config.CollisionHandleTimeoutCheckCraneConfig;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.CockpitHandleService;
import com.sankuai.walleops.cloud.triage.service.impl.NearbyRescueHandleServiceImpl;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.transaction.annotation.Transactional;

@CraneConfiguration
@Slf4j
public class CollisionHandleTimeoutCheckCrane {

    /**
     * 超时未开始处置检测配置
     */
    @ConfigValue(key = "collision.handle.timeout.check.crane.config", defaultValue = "30")
    private CollisionHandleTimeoutCheckCraneConfig craneConfig;

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private NearbyRescueHandleServiceImpl nearbyRescueHandleService;

    @Resource
    private CockpitHandleService cockpitHandleService;

    /**
     * 检测超时未开始处置事件
     */
    @Crane("check.collision.timeout.unHandle")
    public void checkCollisionTimeoutUnHandle() throws Exception {
        log.info("开始检测超时未开始处置的事件");
        InputCheckUtil.isNotNull(craneConfig, "超时未开始处置检测配置为空");
        // 1. 查询所有未开始处置且超过指定时间的事件
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        request.setEventType(EventTypeEnum.getCollisionDetectionTypeList());
        request.setStatus(EventStatusEnum.INITED.getCode());
        // 查询近场处置
        request.setOperatorType(EventOperatorTypeEnum.NEARBY_RESCUE.getCode());
        Date startTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, craneConfig.getQueryRangeMinS());
        request.setStartTime(DatetimeUtil.formatTime(startTime));
        Date endTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.SECONDS, craneConfig.getUnhandledTimeoutSecS());
        request.setEndTime(DatetimeUtil.formatTime(endTime));
        List<BizCloudTriageEvent> bizCloudTriageEventList = cloudTriageEventService.commonQuery(request);
        if (CollectionUtils.isEmpty(bizCloudTriageEventList)) {
            log.info("未查询到超时未开始处置的事件");
            return;
        }
        // 2. 对超时事件进行标记或告警
        for (BizCloudTriageEvent event : bizCloudTriageEventList) {
            // 2.1 标记事件为超时未开始处置
            try {
                // todo: 保障事务性
                ((CollisionHandleTimeoutCheckCrane) AopContext.currentProxy()).reassign(event,
                        CollisionDetectionReassignReasonEnum.TIMEOUT_NO_PROCESS);
            } catch (Exception e) {
                log.error("更新事件状态失败, eventId: {}", event.getEventId(), e);
            }

        }
    }

    /**
     * 检测超时未处置完成事件
     */
    @Crane("check.collision.timeout.unCompleted")
    public void checkCollisionDetectionTimeoutUnCompleted() throws Exception {
        log.info("检测超时未处置完成事件");
        // 1. 查询所有开始处置但未处置完成的事件
        InputCheckUtil.isNotNull(craneConfig, "未处理超时时间配置为空");
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        request.setEventType(EventTypeEnum.getCollisionDetectionTypeList());
        request.setStatus(EventStatusEnum.HANDLING.getCode());
        // 查询近场处置
        request.setOperatorType(EventOperatorTypeEnum.NEARBY_RESCUE.getCode());
        Date startTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, craneConfig.getQueryRangeMinS());
        request.setStartTime(DatetimeUtil.formatTime(startTime));
        Date endTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, craneConfig.getHandledTimeoutMinS());
        request.setMaxUpdateTime(DatetimeUtil.formatTime(endTime));
        List<BizCloudTriageEvent> bizCloudTriageEventList = cloudTriageEventService.commonQuery(request);
        if (CollectionUtils.isEmpty(bizCloudTriageEventList)) {
            log.info("未查询到超时未处置完成的事件");
            return;
        }
        // 2. 对超时事件进行标记或告警
        for (BizCloudTriageEvent event : bizCloudTriageEventList) {
            // 2.1 标记事件为超时未开始处置
            try {
                // todo: 保障事务性
                ((CollisionHandleTimeoutCheckCrane) AopContext.currentProxy()).reassign(event,
                        CollisionDetectionReassignReasonEnum.TIMEOUT_NO_COMPLETE);
            } catch (Exception e) {
                log.error("更新事件状态失败, eventId: {}", event.getEventId(), e);
            }

        }
    }

    /**
     * 改派事件
     *
     * @param cloudTriageEvent
     * @param reasonEnum
     * @throws Exception
     */
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public void reassign(BizCloudTriageEvent cloudTriageEvent, CollisionDetectionReassignReasonEnum reasonEnum)
            throws Exception {
        // 1. 新增碰撞检测异常事件
        BizCloudTriageEvent newEvent = addCloudSecurityEvent(cloudTriageEvent);

        // 2. 更新原事件状态
        if (Objects.nonNull(newEvent)) {
            // 填充改派详情
            RemarkDTO remarkDTO = JacksonUtils.from(cloudTriageEvent.getRemark(), RemarkDTO.class);
            if (Objects.isNull(remarkDTO)) {
                remarkDTO = new RemarkDTO();
            }
            remarkDTO.setRelatedEventId(newEvent.getEventId());
            remarkDTO.setReassignReason(reasonEnum.getDesc());
            // 更新事件详情
            CloudTriageEventUpdateRequest updateRequest = CloudTriageEventUpdateRequest.builder()
                    .eventId(cloudTriageEvent.getEventId())
                    .remark(JacksonUtils.to(remarkDTO))
                    .status(EventStatusEnum.CANCELED.getCode()).build();

            ResponseCodeEnum responseCodeEnum = nearbyRescueHandleService.updateCollisionDetectionEvent(
                    updateRequest);
            if (responseCodeEnum != ResponseCodeEnum.SUCCESS) {
                throw new RuntimeException("改派碰撞检测事件失败");
            }

        }

    }

    /**
     * 添加云安全处置异常事件
     *
     * @param event
     */
    private BizCloudTriageEvent addCloudSecurityEvent(BizCloudTriageEvent event) {
        if (event == null) {
            return null;
        }
        // 从原eventId中获取后缀
        String suffix = event.getEventId().substring(event.getEventId().indexOf('_'));
        // 生成新的eventId
        String newEventId = DateUtil.format(new Date(), DateUtil.YMD_HMS_SSS_UNSIGNED) + suffix;
        BizCloudTriageEvent bizCloudTriageEvent = new BizCloudTriageEvent();
        bizCloudTriageEvent.setEventId(newEventId);
        bizCloudTriageEvent.setEventTime(event.getEventTime());
        bizCloudTriageEvent.setVin(event.getVin());
        bizCloudTriageEvent.setEventType(event.getEventType());
        bizCloudTriageEvent.setVehicleId(event.getVehicleId());
        bizCloudTriageEvent.setRemark(event.getRemark());
        bizCloudTriageEvent.setLatitude(event.getLatitude());
        bizCloudTriageEvent.setLongitude(event.getLongitude());
        bizCloudTriageEvent.setPlace(event.getPlace());
        bizCloudTriageEvent.setPurpose(event.getPurpose());
        log.info("addCloudSecurityEvent,bizCloudTriageEvent = {} ", bizCloudTriageEvent);

        // todo: H24车辆在改派的时候也需要添加灰度逻辑，命中则改派给呼叫云辅助，否则给到云安全检核
        if (cockpitHandleService.isInGray(event.getEventType(), event.getPurpose())) {
            bizCloudTriageEvent.setOperatorType(EventOperatorTypeEnum.COCKPIT.getCode());
        }
        int result = cloudTriageEventService.save(bizCloudTriageEvent);
        if (result < 1) {
            return null;
        }
        return bizCloudTriageEvent;
    }

}
