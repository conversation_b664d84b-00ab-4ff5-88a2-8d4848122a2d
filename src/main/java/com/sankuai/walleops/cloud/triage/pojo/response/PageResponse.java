package com.sankuai.walleops.cloud.triage.pojo.response;

import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@Data
public class PageResponse extends CommonResponse{
    private Integer totalPage;
    private Integer totalSize;
    private Integer page;
    private Integer size;
    private Long maxId;

    public static PageResponse success() {
        return PageResponse.result(ResponseCodeEnum.SUCCESS);
    }

    public static PageResponse success(Object data) {
        PageResponse result = PageResponse.result(ResponseCodeEnum.SUCCESS);
        result.setData(data);
        return result;
    }

    public static PageResponse failed() {
        return PageResponse.result(ResponseCodeEnum.FAILED);
    }

    public static PageResponse failed(Object data) {
        PageResponse result = PageResponse.result(ResponseCodeEnum.FAILED);
        result.setData(data);
        return result;
    }

    public static PageResponse result(ResponseCodeEnum responseCodeEnum) {
        PageResponse pageResponse = new PageResponse();
        pageResponse.setRet(responseCodeEnum.getCode());
        pageResponse.setMsg(responseCodeEnum.getMsg());
        return pageResponse;
    }
}
