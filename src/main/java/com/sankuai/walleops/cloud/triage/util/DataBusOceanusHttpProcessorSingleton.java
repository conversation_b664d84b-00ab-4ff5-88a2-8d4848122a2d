package com.sankuai.walleops.cloud.triage.util;

import com.sankuai.oceanus.http.client.apache.OceanusHttpProcessor;

public class DataBusOceanusHttpProcessorSingleton {

    private static OceanusHttpProcessor instance;

    /**
     *  数据总线服务AppKey
     */
    private static final String  DATA_BUS_SERVICE_APPKEY = "com.sankuai.wallecmdb.monitor.online";

    private DataBusOceanusHttpProcessorSingleton() {
        // 私有构造函数，防止外部实例化
    }

    /**
     * 获取单例对象
     * @return OceanusHttpProcessor
     */
    public static synchronized OceanusHttpProcessor getInstance(){

        if(instance == null){
            instance = new OceanusHttpProcessor(DATA_BUS_SERVICE_APPKEY);
        }
        return instance;
    }
}
