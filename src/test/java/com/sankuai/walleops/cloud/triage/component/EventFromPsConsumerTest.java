package com.sankuai.walleops.cloud.triage.component;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.walleops.cloud.triage.BaseTest;
import com.sankuai.walleops.cloud.triage.TestConfiguration;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.mapper.BizCloudTriageEventMapper;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.service.CockpitHandleService;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * EventFromPsConsumer测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
@Import(TestConfiguration.class)
@Slf4j
public class EventFromPsConsumerTest extends BaseTest {

    @Resource
    private EventFromPsConsumer eventFromPsConsumer;

    /**
     * 测试事件过滤功能
     */
    @Test
    public void testEventFilter() {
        try {
            // 构建测试消息
            String message = buildTestMessage();
            System.out.println("测试消息: " + message);
            
            // 直接调用消费方法，参数2传null即可
            ConsumeStatus status = eventFromPsConsumer.consume(message, null);
            
            // 打印结果
            System.out.println("消费结果: " + status);
            System.out.println("测试完成!");
        } catch (Exception e) {
            System.out.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 构建测试消息 - PS格式
     */
    private String buildTestMessage() {
        Map<String, Object> event = new HashMap<>();
        event.put("event_id", "test_event_" + System.currentTimeMillis());
        event.put("event_type", 1003);  // 使用1003作为测试事件类型（对应PsEventEnum.TRAFFICCONGESTION_ALERT的code值）
        event.put("event_timestamp", System.currentTimeMillis());
        event.put("send_timestamp", System.currentTimeMillis());
        event.put("vin", "LMTZSV025NC010556");  // 真实VIN码
        event.put("vehicle_name", "H24");  // 使用H24车型来测试过滤逻辑
        event.put("vehicle_id", "京AG1234");

        // 内容可以是任意对象
        Map<String, Object> content = new HashMap<>();
        content.put("source", "单元测试");
        content.put("desc", "测试EventFromPsConsumer过滤功能");
        event.put("content", content);

        return JSON.toJSONString(event);
    }

    @Resource
    private CockpitHandleService cockpitHandleService;

    @Test
    public void testQueryByEventId() throws Exception {
        cockpitHandleService.requestCloudOperation("LA71AUB18S0508638", 23,
                CommonConstant.CANCEL_CLOUD_OPERATION_ACTION);
    }

    @Resource
    private BizCloudTriageEventMapper bizCloudTriageEventMapper;

    @Test
    public void testQueryByEventId2() throws Exception {
        BizCloudTriageEvent event = bizCloudTriageEventMapper.queryByUniqueKey(
                "20250625135927004_accident-detection_s20-173", 1653614L);
        System.out.println(event);
    }
} 