package com.sankuai.walleops.cloud.triage.mapper;

import com.sankuai.walleops.cloud.triage.pojo.dto.EventDetailQueryDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventGroupQueryDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventGroupResultDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.po.LastEventPO;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.Mapper;

/**
 * <AUTHOR>
 * @date 2022/8/24
 */
public interface BizCloudTriageEventMapper extends Mapper<BizCloudTriageEvent> {

    @Insert({
            "<script>",
            "INSERT INTO biz_cloud_triage_event",
            "<trim prefix='(' suffix=')' suffixOverrides=','>",
            "<if test='null != eventId and &apos;&apos; != eventId'>",
            "event_id,",
            "</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "information_id,",
            "</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>",
            "record_name,",
            "</if>",
            "<if test='null != vin and &apos;&apos; != vin'>",
            "vin,",
            "</if>",
            "<if test='null != vehicleId and &apos;&apos; != vehicleId'>",
            "vehicle_id,",
            "</if>",
            "<if test='null != eventTime'>",
            "event_time,",
            "</if>",
            "<if test='null != recoveryTime'>",
            "recovery_time,",
            "</if>",
            "<if test='null != eventType'>",
            "event_type,",
            "</if>",
            "<if test='null != longitude and &apos;&apos; != longitude'>",
            "longitude,",
            "</if>",
            "<if test='null != latitude and &apos;&apos; != latitude'>",
            "latitude,",
            "</if>",
            "<if test='null != status'>",
            "status,",
            "</if>",
            "<if test='null != operator and &apos;&apos; != operator'>",
            "operator,",
            "</if>",
            "<if test='null != reporter and &apos;&apos; != reporter'>",
            "reporter,",
            "</if>",
            "<if test='null != operateStartTime'>",
            "operate_start_time,",
            "</if>",
            "<if test='null != operateEndTime'>",
            "operate_end_time,",
            "</if>",
            "<if test='null != remark and &apos;&apos; != remark'>",
            "remark,",
            "</if>",
            "<if test='null != purpose and &apos;&apos; != purpose'>",
            "purpose,",
            "</if>",
            "<if test='null != place and &apos;&apos; != place'>",
            "place,",
            "</if>",
            "<if test='null != isDeleted'>",
            "is_deleted,",
            "</if>",
            "<if test='null != createTime'>",
            "create_time,",
            "</if>",
            "<if test='null != updateTime'>",
            "update_time,",
            "</if>",
            "<if test='null != operationType'>",
            "operation_type,",
            "</if>",
            "<if test='null != operatorType'>",
            "operator_type,",
            "</if>",
            "<if test='null != mrmCalled'>",
            "mrm_called,",
            "</if>",
            "</trim>",
            "<trim prefix='values (' suffix=')' suffixOverrides=','>",
            "<if test='null != eventId and &apos;&apos; != eventId'>",
            "#{eventId},",
            "</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "#{informationId},",
            "</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>",
            "#{recordName},",
            "</if>",
            "<if test='null != vin and &apos;&apos; != vin'>",
            "#{vin},",
            "</if>",
            "<if test='null != vehicleId and &apos;&apos; != vehicleId'>",
            "#{vehicleId},",
            "</if>",
            "<if test='null != eventTime'>",
            "#{eventTime},",
            "</if>",
            "<if test='null != recoveryTime'>",
            "#{recoveryTime},",
            "</if>",
            "<if test='null != eventType'>",
            "#{eventType},",
            "</if>",
            "<if test='null != longitude and &apos;&apos; != longitude'>",
            "#{longitude},",
            "</if>",
            "<if test='null != latitude and &apos;&apos; != latitude'>",
            "#{latitude},",
            "</if>",
            "<if test='null != status'>",
            "#{status},",
            "</if>",
            "<if test='null != operator and &apos;&apos; != operator'>",
            "#{operator},",
            "</if>",
            "<if test='null != reporter and &apos;&apos; != reporter'>",
            "#{reporter},",
            "</if>",
            "<if test='null != operateStartTime'>",
            "#{operateStartTime},",
            "</if>",
            "<if test='null != operateEndTime'>",
            "#{operateEndTime},",
            "</if>",
            "<if test='null != remark and &apos;&apos; != remark'>",
            "#{remark},",
            "</if>",
            "<if test='null != purpose and &apos;&apos; != purpose'>",
            "#{purpose},",
            "</if>",
            "<if test='null != place and &apos;&apos; != place'>",
            "#{place},",
            "</if>",
            "<if test='null != isDeleted'>",
            "#{isDeleted},",
            "</if>",
            "<if test='null != createTime'>",
            "#{createTime},",
            "</if>",
            "<if test='null != updateTime'>",
            "#{updateTime},",
            "</if>",
            "<if test='null != operationType'>",
            "#{operationType},",
            "</if>",
            "<if test='null != operatorType'>",
            "#{operatorType},",
            "</if>",
            "<if test='null != mrmCalled'>",
            "#{mrmCalled},",
            "</if>",
            "</trim>",
            "</script>"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertOne(BizCloudTriageEvent cloudTriageEvent);

    @Insert({
            "<script>",
            "INSERT INTO biz_cloud_triage_event",
            "<trim prefix='(' suffix=')' suffixOverrides=','>",
            "<if test='null != eventId and &apos;&apos; != eventId'>",
            "event_id,",
            "</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "information_id,",
            "</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>",
            "record_name,",
            "</if>",
            "<if test='null != vin and &apos;&apos; != vin'>",
            "vin,",
            "</if>",
            "<if test='null != vehicleId and &apos;&apos; != vehicleId'>",
            "vehicle_id,",
            "</if>",
            "<if test='null != eventTime'>",
            "event_time,",
            "</if>",
            "<if test='null != recoveryTime'>",
            "recovery_time,",
            "</if>",
            "<if test='null != eventType'>",
            "event_type,",
            "</if>",
            "<if test='null != longitude and &apos;&apos; != longitude'>",
            "longitude,",
            "</if>",
            "<if test='null != latitude and &apos;&apos; != latitude'>",
            "latitude,",
            "</if>",
            "<if test='null != status'>",
            "status,",
            "</if>",
            "<if test='null != operator and &apos;&apos; != operator'>",
            "operator,",
            "</if>",
            "<if test='null != operateStartTime'>",
            "operate_start_time,",
            "</if>",
            "<if test='null != operateEndTime'>",
            "operate_end_time,",
            "</if>",
            "<if test='null != remark and &apos;&apos; != remark'>",
            "remark,",
            "</if>",
            "<if test='null != purpose and &apos;&apos; != purpose'>",
            "purpose,",
            "</if>",
            "<if test='null != place and &apos;&apos; != place'>",
            "place,",
            "</if>",
            "<if test='null != isDeleted'>",
            "is_deleted,",
            "</if>",
            "<if test='null != createTime'>",
            "create_time,",
            "</if>",
            "<if test='null != updateTime'>",
            "update_time",
            "</if>",
            "<if test='null != operationType'>",
            "operation_type",
            "</if>",
            "</trim>",
            "<trim prefix='values (' suffix=')' suffixOverrides=','>",
            "<if test='null != eventId and &apos;&apos; != eventId'>",
            "#{eventId},",
            "</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "#{informationId},",
            "</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>",
            "#{recordName},",
            "</if>",
            "<if test='null != vin and &apos;&apos; != vin'>",
            "#{vin},",
            "</if>",
            "<if test='null != vehicleId and &apos;&apos; != vehicleId'>",
            "#{vehicleId},",
            "</if>",
            "<if test='null != eventTime'>",
            "#{eventTime},",
            "</if>",
            "<if test='null != recoveryTime'>",
            "#{recoveryTime},",
            "</if>",
            "<if test='null != eventType'>",
            "#{eventType},",
            "</if>",
            "<if test='null != longitude and &apos;&apos; != longitude'>",
            "#{longitude},",
            "</if>",
            "<if test='null != latitude and &apos;&apos; != latitude'>",
            "#{latitude},",
            "</if>",
            "<if test='null != status'>",
            "#{status},",
            "</if>",
            "<if test='null != operator and &apos;&apos; != operator'>",
            "#{operator},",
            "</if>",
            "<if test='null != operateStartTime'>",
            "#{operateStartTime},",
            "</if>",
            "<if test='null != operateEndTime'>",
            "#{operateEndTime},",
            "</if>",
            "<if test='null != remark and &apos;&apos; != remark'>",
            "#{remark},",
            "</if>",
            "<if test='null != purpose and &apos;&apos; != purpose'>",
            "#{purpose},",
            "</if>",
            "<if test='null != place and &apos;&apos; != place'>",
            "#{place},",
            "</if>",
            "<if test='null != isDeleted'>",
            "#{isDeleted},",
            "</if>",
            "<if test='null != createTime'>",
            "#{createTime},",
            "</if>",
            "<if test='null != updateTime'>",
            "#{updateTime}",
            "</if>",
            "<if test='null != operationType'>",
            "#{operationType}",
            "</if>",
            "</trim>",
            " ON DUPLICATE KEY UPDATE ",
            "<trim suffixOverrides=','>",
            "<if test='null != eventId and &apos;&apos; != eventId'>event_id = #{eventId},</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "information_id = #{informationId},</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>record_name = #{recordName},</if>",
            "<if test='null != vin and &apos;&apos; != vin'>vin = #{vin},</if>",
            "<if test='null != vehicleId and &apos;&apos; != vehicleId'>vehicle_id = #{vehicleId},</if>",
            "<if test='null != eventTime'>event_time = #{eventTime},</if>",
            "<if test='null != recoveryTime'>recovery_time = #{recoveryTime},</if>",
            "<if test='null != eventType'>event_type = #{eventType},</if>",
            "<if test='null != longitude and &apos;&apos; != longitude'>longitude = #{longitude},</if>",
            "<if test='null != latitude and &apos;&apos; != latitude'>latitude = #{latitude},</if>",
            "<if test='null != status'>status = #{status},</if>",
            "<if test='null != operator and &apos;&apos; != operator'>operator = #{operator},</if>",
            "<if test='null != operateStartTime'>operate_start_time = #{operateStartTime},</if>",
            "<if test='null != operateEndTime'>operate_end_time = #{operateEndTime},</if>",
            "<if test='null != remark and &apos;&apos; != remark'>remark = #{remark},</if>",
            "<if test='null != purpose and &apos;&apos; != purpose'>purpose = #{purpose},</if>",
            "<if test='null != place and &apos;&apos; != place'>place = #{place},</if>",
            "<if test='null != isDeleted'>is_deleted = #{isDeleted},</if>",
            "</trim>",
            "</script>"
    })
    int upsertAccident(BizCloudTriageEvent cloudTriageEvent);

    @Update({
            "<script>",
            "UPDATE biz_cloud_triage_event",
            "<set>",
            "<if test='null != eventId and &apos;&apos; != eventId'>event_id = #{eventId},</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "information_id = #{informationId},</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>record_name = #{recordName},</if>",
            "<if test='null != vin and &apos;&apos; != vin'>vin = #{vin},</if>",
            "<if test='null != vehicleId and &apos;&apos; != vehicleId'>vehicle_id = #{vehicleId},</if>",
            "<if test='null != eventTime'>event_time = #{eventTime},</if>",
            "<if test='null != recoveryTime'>recovery_time = #{recoveryTime},</if>",
            "<if test='null != eventType'>event_type = #{eventType},</if>",
            "<if test='null != longitude and &apos;&apos; != longitude'>longitude = #{longitude},</if>",
            "<if test='null != latitude and &apos;&apos; != latitude'>latitude = #{latitude},</if>",
            "<if test='null != status'>status = #{status},</if>",
            "<if test='null != operator and &apos;&apos; != operator'>operator = #{operator},</if>",
            "<if test='null != operateStartTime'>operate_start_time = #{operateStartTime},</if>",
            "<if test='null != operateEndTime'>operate_end_time = #{operateEndTime},</if>",
            "<if test='null != remark and &apos;&apos; != remark'>remark = #{remark},</if>",
            "<if test='null != purpose and &apos;&apos; != purpose'>purpose = #{purpose},</if>",
            "<if test='null != place and &apos;&apos; != place'>place = #{place},</if>",
            "<if test='null != isDeleted'>is_deleted = #{isDeleted},</if>",
            "<if test='null != operationType'>operation_type = #{operationType},</if>",
            "<if test='null != operatorType'>operator_type = #{operatorType},</if>",
            "<if test='null != mrmCalled'>mrm_called = #{mrmCalled},</if>",
            "</set>",
            "WHERE id = #{id} or event_id = #{eventId}",
            "</script>"
    })
    int updateOne(BizCloudTriageEvent cloudTriageEvent);

    @Update({
            "<script>",
            "<foreach item='item' collection='list' separator=';'>",
            "UPDATE biz_cloud_triage_event",
            "<set>",
            "<if test='null != item.eventId and &apos;&apos; != item.eventId'>event_id = #{item.eventId},</if>",
            "<if test='null != item.informationId and &apos;&apos; != item.informationId'>",
            "information_id = #{informationId},</if>",
            "<if test='null != item.recordName and &apos;&apos; != item.recordName'>",
            "record_name = #{item.recordName},</if>",
            "<if test='null != item.vin and &apos;&apos; != item.vin'>vin = #{item.vin},</if>",
            "<if test='null != item.vehicleId and &apos;&apos; != item.vehicleId'>vehicle_id = #{item.vehicleId},</if>",
            "<if test='null != item.eventTime'>event_time = #{item.eventTime},</if>",
            "<if test='null != item.recoveryTime'>recovery_time = #{item.recoveryTime},</if>",
            "<if test='null != item.eventType'>event_type = #{item.eventType},</if>",
            "<if test='null != item.longitude and &apos;&apos; != item.longitude'>longitude = #{item.longitude},</if>",
            "<if test='null != item.latitude and &apos;&apos; != item.latitude'>latitude = #{item.latitude},</if>",
            "<if test='null != item.status and &apos;&apos; != item.status'>status = #{item.status},</if>",
            "<if test='null != item.operator and &apos;&apos; != item.operator'>operator = #{item.operator},</if>",
            "<if test='null != item.operateStartTime'>operate_start_time = #{item.operateStartTime},</if>",
            "<if test='null != item.operateEndTime'>operate_end_time = #{item.operateEndTime},</if>",
            "<if test='null != item.remark and &apos;&apos; != item.remark'>remark = #{item.remark},</if>",
            "<if test='null != item.purpose and &apos;&apos; != item.purpose'>purpose = #{item.purpose},</if>",
            "<if test='null != item.place and &apos;&apos; != item.place'>place = #{item.place},</if>",
            "<if test='null != item.isDeleted and &apos;&apos; != item.isDeleted'>is_deleted = #{item.isDeleted},</if>",
            "</set>",
            "WHERE id = #{id}",
            "</foreach>",
            "</script>"
    })
    int updateBatch(List<BizCloudTriageEvent> cloudTriageEvent);


    @Select({
            "<script>",
            "select * from biz_cloud_triage_event where is_deleted = 0",
            "<if test='null != id'>and id = #{id}</if>",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "and information_id = #{informationId}</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != vinList and vinList.size != 0'>",
            "and vin in",
            "<foreach item='item' collection='vinList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != vehicleIdList and vehicleIdList.size != 0'>",
            "and vehicle_id in",
            "<foreach item='item' collection='vehicleIdList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != eventType and eventType.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='eventType' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != status'>and status = #{status}</if>",
            "<if test='null != operator and &apos;&apos; != operator'>and operator = #{operator}</if>",
            "<if test='null != purpose and purpose.size != 0'>",
            "and purpose in",
            "<foreach item='item' collection='purpose' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != eventTime and &apos;&apos; != eventTime'>and event_time = #{eventTime}</if>",
            "<if test='null != startTime and &apos;&apos; != startTime'>and event_id &gt;= #{startTime}</if>",
            "<if test='null != endTime and &apos;&apos; != endTime'>and event_id &lt;= #{endTime}</if>",
            "<if test='null != remark and &apos;&apos; != remark'>and remark like concat('%', #{remark}, '%')</if>",
            "<if test='null != operationType'>and operation_type = #{operationType}</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "<if test='null != mrmCalled'>and mrm_called = #{mrmCalled}</if>",
            "<if test='null != minUpdateTime'>and update_time &gt;= #{minUpdateTime}</if>",
            "<if test='null != maxUpdateTime'>and update_time &lt;= #{maxUpdateTime}</if>",
            "<if test='null != inStatusList and inStatusList.size != 0'>",
            "and status in",
            "<foreach item='item' collection='inStatusList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != reporter'>and reporter = #{reporter}</if>",
            "and vehicle_id != 'E10-01'",
            "order by event_id asc",
            "<if test='null != page and null != size'>",
            "limit #{start}, #{size}",
            "</if>",
            "</script>"
    })
    List<BizCloudTriageEvent> pageQuery(CloudTriageEventQueryPageRequest request);

    @Select({
            "<script>",
            "select * from biz_cloud_triage_event where is_deleted = 0",
            "<if test='null != id'>and id = #{id}</if>",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "and information_id = #{informationId}</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != vinList and vinList.size != 0'>",
            "and vin in",
            "<foreach item='item' collection='vinList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != operatorTypeList and operatorTypeList.size != 0'>",
            "and operator_type in",
            "<foreach item='item' collection='operatorTypeList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != vehicleIdList and vehicleIdList.size != 0'>",
            "and vehicle_id in",
            "<foreach item='item' collection='vehicleIdList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != eventType and eventType.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='eventType' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != status'>and status = #{status}</if>",
            "<if test='null != operator and &apos;&apos; != operator'>and operator = #{operator}</if>",
            "<if test='null != purpose and purpose.size != 0'>",
            "and purpose in",
            "<foreach item='item' collection='purpose' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != place and place.size != 0'>",
            "and place in",
            "<foreach item='item' collection='place' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != eventTime and &apos;&apos; != eventTime'>and event_time = #{eventTime}</if>",
            "<if test='null != startTime and &apos;&apos; != startTime'>and create_time &gt;= #{startTime}</if>",
            "<if test='null != endTime and &apos;&apos; != endTime'>and create_time &lt;= #{endTime}</if>",
            "<if test='null != remark and &apos;&apos; != remark'>and remark like concat('%', #{remark}, '%')</if>",
            "<if test='null != operationType'>and operation_type = #{operationType}</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "<if test='null != mrmCalled'>and mrm_called = #{mrmCalled}</if>",
            "<if test='null != minUpdateTime'>and update_time &gt;= #{minUpdateTime}</if>",
            "<if test='null != maxUpdateTime'>and update_time &lt;= #{maxUpdateTime}</if>",
            "<if test='null != inStatusList and inStatusList.size != 0'>",
            "and status in",
            "<foreach item='item' collection='inStatusList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != reporter'>and reporter = #{reporter}</if>",
            "and vehicle_id != 'E10-01'",
            "order by event_id asc",
            "<if test='null != page and null != size'>",
            "limit #{start}, #{size}",
            "</if>",
            "</script>"
    })
    List<BizCloudTriageEvent> commonQuery(CloudTriageEventQueryPageRequest request);

    @Select({
            "<script>",
            "select * from biz_cloud_triage_event where ( is_deleted = 0",
            "<if test='null != id'>and id = #{id}</if>",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "and information_id = #{informationId}</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != vinList and vinList.size != 0'>",
            "and vin in",
            "<foreach item='item' collection='vinList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != vehicleIdList and vehicleIdList.size != 0'>",
            "and vehicle_id in",
            "<foreach item='item' collection='vehicleIdList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != eventType and eventType.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='eventType' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != status'>and status = #{status}</if>",
            "<if test='null != operator and &apos;&apos; != operator'>and operator = #{operator}</if>",
            "<if test='null != purpose and purpose.size != 0'>",
            "and purpose in",
            "<foreach item='item' collection='purpose' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != place and place.size != 0'>",
            "and place in",
            "<foreach item='item' collection='place' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != startTime and &apos;&apos; != startTime'>and event_id &gt;= #{startTime}</if>",
            "<if test='null != endTime and &apos;&apos; != endTime'>and event_id &lt;= #{endTime}</if>",
            "<if test='null != remark and &apos;&apos; != remark'>and remark like concat('%', #{remark}, '%')</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "<if test='null != mrmCalled'>and mrm_called = #{mrmCalled}</if>",
            "and vehicle_id != 'E10-01'",
            " ) OR ( is_deleted = 0",
            "<if test='null != id'>and id = #{id}</if>",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "and information_id = #{informationId}</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != vinList and vinList.size != 0'>",
            "and vin in",
            "<foreach item='item' collection='vinList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != vehicleIdList and vehicleIdList.size != 0'>",
            "and vehicle_id in",
            "<foreach item='item' collection='vehicleIdList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != groupEventType and groupEventType.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='groupEventType' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != status'>and status = #{status}</if>",
            "<if test='null != operator and &apos;&apos; != operator'>and operator = #{operator}</if>",
            "<if test='null != groupPurpose and groupPurpose.size != 0'>",
            "and purpose in",
            "<foreach item='item' collection='groupPurpose' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != place and place.size != 0'>",
            "and place in",
            "<foreach item='item' collection='place' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != startTime and &apos;&apos; != startTime'>and event_id &gt;= #{startTime}</if>",
            "<if test='null != endTime and &apos;&apos; != endTime'>and event_id &lt;= #{endTime}</if>",
            "<if test='null != remark and &apos;&apos; != remark'>and remark like concat('%', #{remark}, '%')</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "<if test='null != mrmCalled'>and mrm_called = #{mrmCalled}</if>",
            "and vehicle_id != 'E10-01' )",
            "order by event_id asc ",
            "<if test='null != page and null != size'>",
            "limit #{start}, #{size}",
            "</if>",
            "</script>"
    })
    List<BizCloudTriageEvent> pageQueryGroup(CloudTriageEventQueryPageRequest request);

    @Select({
            "<script>",
            "select count(1) from biz_cloud_triage_event where is_deleted = 0",
            "<if test='null != id'>and id = #{id}</if>",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "and information_id = #{informationId}</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != vinList and vinList.size != 0'>",
            "and vin in",
            "<foreach item='item' collection='vinList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != vehicleIdList and vehicleIdList.size != 0'>",
            "and vehicle_id in",
            "<foreach item='item' collection='vehicleIdList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != eventType and eventType.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='eventType' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != status'>and status = #{status}</if>",
            "<if test='null != operator and &apos;&apos; != operator'>and operator = #{operator}</if>",
            "<if test='null != purpose and purpose.size != 0'>",
            "and purpose in",
            "<foreach item='item' collection='purpose' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != place and place.size != 0'>",
            "and place in",
            "<foreach item='item' collection='place' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != startTime and &apos;&apos; != startTime'>and event_id &gt;= #{startTime}</if>",
            "<if test='null != endTime and &apos;&apos; != endTime'>and event_id &lt;= #{endTime}</if>",
            "<if test='null != remark and &apos;&apos; != remark'>and remark like concat('%', #{remark}, '%')</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "<if test='null != mrmCalled'>and mrm_called = #{mrmCalled}</if>",
            "and vehicle_id != 'E10-01'",
            "</script>"
    })
    int count(CloudTriageEventQueryPageRequest request);

    @Select({
            "<script>",
            "select count(1) from biz_cloud_triage_event where ( is_deleted = 0",
            "<if test='null != id'>and id = #{id}</if>",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "and information_id = #{informationId}</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != vinList and vinList.size != 0'>",
            "and vin in",
            "<foreach item='item' collection='vinList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != vehicleIdList and vehicleIdList.size != 0'>",
            "and vehicle_id in",
            "<foreach item='item' collection='vehicleIdList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != eventType and eventType.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='eventType' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != status'>and status = #{status}</if>",
            "<if test='null != operator and &apos;&apos; != operator'>and operator = #{operator}</if>",
            "<if test='null != purpose and purpose.size != 0'>",
            "and purpose in",
            "<foreach item='item' collection='purpose' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != place and place.size != 0'>",
            "and place in",
            "<foreach item='item' collection='place' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != startTime and &apos;&apos; != startTime'>and event_id &gt;= #{startTime}</if>",
            "<if test='null != endTime and &apos;&apos; != endTime'>and event_id &lt;= #{endTime}</if>",
            "<if test='null != remark and &apos;&apos; != remark'>and remark like concat('%', #{remark}, '%')</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "<if test='null != mrmCalled'>and mrm_called = #{mrmCalled}</if>",
            "and vehicle_id != 'E10-01'",
            " ) OR ( is_deleted = 0",
            "<if test='null != id'>and id = #{id}</if>",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "and information_id = #{informationId}</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != vinList and vinList.size != 0'>",
            "and vin in",
            "<foreach item='item' collection='vinList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != vehicleIdList and vehicleIdList.size != 0'>",
            "and vehicle_id in",
            "<foreach item='item' collection='vehicleIdList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != groupEventType and groupEventType.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='groupEventType' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != status'>and status = #{status}</if>",
            "<if test='null != operator and &apos;&apos; != operator'>and operator = #{operator}</if>",
            "<if test='null != groupPurpose and groupPurpose.size != 0'>",
            "and purpose in",
            "<foreach item='item' collection='groupPurpose' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != place and place.size != 0'>",
            "and place in",
            "<foreach item='item' collection='place' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != startTime and &apos;&apos; != startTime'>and event_id &gt;= #{startTime}</if>",
            "<if test='null != endTime and &apos;&apos; != endTime'>and event_id &lt;= #{endTime}</if>",
            "<if test='null != remark and &apos;&apos; != remark'>and remark like concat('%', #{remark}, '%')</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "<if test='null != mrmCalled'>and mrm_called = #{mrmCalled}</if>",
            "and vehicle_id != 'E10-01' )",
            "</script>"
    })
    int countGroup(CloudTriageEventQueryPageRequest request);

    @Select({
            "<script>",
            "select MAX(id) from biz_cloud_triage_event where is_deleted = 0",
            "<if test='null != id'>and id = #{id}</if>",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "and information_id = #{informationId}</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != vinList and vinList.size != 0'>",
            "and vin in",
            "<foreach item='item' collection='vinList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != vehicleIdList and vehicleIdList.size != 0'>",
            "and vehicle_id in",
            "<foreach item='item' collection='vehicleIdList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != eventType and eventType.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='eventType' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != status'>and status = #{status}</if>",
            "<if test='null != operator and &apos;&apos; != operator'>and operator = #{operator}</if>",
            "<if test='null != purpose and purpose.size != 0'>",
            "and purpose in",
            "<foreach item='item' collection='purpose' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != place and place.size != 0'>",
            "and place in",
            "<foreach item='item' collection='place' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != startTime and &apos;&apos; != startTime'>and event_id &gt;= #{startTime}</if>",
            "<if test='null != endTime and &apos;&apos; != endTime'>and event_id &lt;= #{endTime}</if>",
            "<if test='null != remark and &apos;&apos; != remark'>and remark like concat('%', #{remark}, '%')</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "<if test='null != mrmCalled'>and mrm_called = #{mrmCalled}</if>",
            "and vehicle_id != 'E10-01'",
            "</script>"
    })
    Long getMaxId(CloudTriageEventQueryPageRequest request);

    @Select({
            "<script>",
            "select MAX(id) from biz_cloud_triage_event where ( is_deleted = 0",
            "<if test='null != id'>and id = #{id}</if>",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "and information_id = #{informationId}</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != vinList and vinList.size != 0'>",
            "and vin in",
            "<foreach item='item' collection='vinList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != vehicleIdList and vehicleIdList.size != 0'>",
            "and vehicle_id in",
            "<foreach item='item' collection='vehicleIdList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != eventType and eventType.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='eventType' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != status'>and status = #{status}</if>",
            "<if test='null != operator and &apos;&apos; != operator'>and operator = #{operator}</if>",
            "<if test='null != purpose and purpose.size != 0'>",
            "and purpose in",
            "<foreach item='item' collection='purpose' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != place and place.size != 0'>",
            "and place in",
            "<foreach item='item' collection='place' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != startTime and &apos;&apos; != startTime'>and event_id &gt;= #{startTime}</if>",
            "<if test='null != endTime and &apos;&apos; != endTime'>and event_id &lt;= #{endTime}</if>",
            "<if test='null != remark and &apos;&apos; != remark'>and remark like concat('%', #{remark}, '%')</if>",
            "and vehicle_id != 'E10-01'",
            " ) OR ( is_deleted = 0",
            "<if test='null != id'>and id = #{id}</if>",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "and information_id = #{informationId}</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != vinList and vinList.size != 0'>",
            "and vin in",
            "<foreach item='item' collection='vinList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != vehicleIdList and vehicleIdList.size != 0'>",
            "and vehicle_id in",
            "<foreach item='item' collection='vehicleIdList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != groupEventType and groupEventType.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='groupEventType' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != status'>and status = #{status}</if>",
            "<if test='null != operator and &apos;&apos; != operator'>and operator = #{operator}</if>",
            "<if test='null != groupPurpose and groupPurpose.size != 0'>",
            "and purpose in",
            "<foreach item='item' collection='groupPurpose' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != place and place.size != 0'>",
            "and place in",
            "<foreach item='item' collection='place' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != startTime and &apos;&apos; != startTime'>and event_id &gt;= #{startTime}</if>",
            "<if test='null != endTime and &apos;&apos; != endTime'>and event_id &lt;= #{endTime}</if>",
            "<if test='null != remark and &apos;&apos; != remark'>and remark like concat('%', #{remark}, '%')</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "<if test='null != mrmCalled'>and mrm_called = #{mrmCalled}</if>",
            "and vehicle_id != 'E10-01' )",
            "</script>"
    })
    Long getMaxIdGroup(CloudTriageEventQueryPageRequest request);


    @Select({
            "<script>",
            "select vin, count(1) event_count, max(id) latest_id from biz_cloud_triage_event",
            "where is_deleted = 0 and status in (0, 1)",
            "<if test='null != id'>and id = #{id}</if>",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != informationId and &apos;&apos; != informationId'>",
            "and information_id = #{informationId}</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != vinList and vinList.size != 0'>",
            "and vin in",
            "<foreach item='item' collection='vinList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != vehicleIdList and vehicleIdList.size != 0'>",
            "and vehicle_id in",
            "<foreach item='item' collection='vehicleIdList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != eventType'>and event_type = #{eventType}</if>",
            "<if test='null != operator and &apos;&apos; != operator'>and operator = #{operator}</if>",
            "<if test='null != purpose and &apos;&apos; != purpose'>and purpose = #{purpose}</if>",
            "<if test='null != place and &apos;&apos; != place'>and place = #{place}</if>",
            "<if test='null != startTime and &apos;&apos; != startTime'>and event_id &gt;= #{startTime}</if>",
            "<if test='null != endTime and &apos;&apos; != endTime'>and event_id &lt;= #{endTime}</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "group by vin",
            "</script>"
    })
    @Results(id = "vinEventTypeCount", value = {
            @Result(property = "vin", column = "vin"),
            @Result(property = "eventCount", column = "event_count"),
            @Result(property = "latestId", column = "latest_id"),
    })
    List<EventGroupResultDTO> groupUnprocessedEvent(EventGroupQueryDTO request);

    @Select({
            "<script>",
            "SELECT *",
            "FROM biz_cloud_triage_event",
            "WHERE is_deleted = 0",
            "<if test='null != startTime'>",
            "AND create_time &gt;= #{startTime}",
            "</if>",
            "<if test='null != endTime'>",
            "AND create_time &lt;= #{endTime}",
            "</if>",
            "<if test='null != eventTypeList and eventTypeList.size != 0'>",
            "AND event_type IN",
            "<foreach item='item' collection='eventTypeList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "ORDER BY create_time ASC",
            "</script>"
    })
    List<BizCloudTriageEvent> queryByTimeRange(
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("eventTypeList") List<Integer> eventTypeList,
            @Param("operatorType") Integer operatorType);

    @Select({
            "<script>",
            "select *",
            "from biz_cloud_triage_event where 1 = 1",
            "<if test='null != eventId'>",
            "and event_id = #{eventId}",
            "</if>",
            "<if test='null != id'>",
            "and id = #{id}",
            "</if>",
            "</script>"
    })
    BizCloudTriageEvent queryAllByUniqueKey(@Param("eventId") String eventId, @Param("id") Long id);

    @Select({
            "<script>",
            "select vin, event_type from biz_cloud_triage_event where id in",
            "<foreach item='item' collection='list' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<BizCloudTriageEvent> queryEventTypeByIds(List<Long> ids);

    @Select({
            "<script>",
            "select id, event_id, vehicle_id, vin, status, event_time, operate_start_time, operate_end_time, mrm_called, event_type",
            "from biz_cloud_triage_event where 1 = 1",
            "<if test='null != eventId'>",
            "and event_id = #{eventId}",
            "</if>",
            "<if test='null != id'>",
            "and id = #{id}",
            "</if>",
            "</script>"
    })
    BizCloudTriageEvent queryByUniqueKey(@Param("eventId") String eventId, @Param("id") Long id);


    @Select({
            "<script>",
            "select id, event_id, vehicle_id, vin, status, event_time, operate_start_time, operate_end_time",
            "from biz_cloud_triage_event where 1 = 1",
            "<if test='null != informationId'>",
            "and information_id = #{informationId}",
            "</if>",
            "order by id desc limit 1",
            "</script>"
    })
    BizCloudTriageEvent queryByInformationId(@Param("informationId") String informationId);

    @Select("select * from biz_cloud_triage_event where id = #{id}")
    BizCloudTriageEvent queryEventDetailById(@Param("id") Long id);

    @Select({
            "<script>",
            "select event_id as eventId, event_type as eventType, event_time as eventTime, vin, status",
            " from biz_cloud_triage_event",
            " where event_type=#{event_type}",
            " and vin in ",
            "<foreach item='item' collection='vin_list' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            " and event_id &gt;= #{start_time}",
            " and event_id &lt;= #{end_time}",
            "<if test='null != operator_type'>and operator_type = #{operator_type}</if>",
            "</script>"
    })
    List<LastEventPO> queryLastEvent(@Param("vin_list") List<String> vinList,
            @Param("start_time") String startTime,
            @Param("end_time") String entTime,
            @Param("event_type") Integer eventType,
            @Param("operator_type") Integer operatorType);

    @Select({
            "<script>",
            "select * from biz_cloud_triage_event",
            " where event_id like concat('%', #{eventId}, '%')",
            " and create_time >= #{minCreateTime}",  // 添加创建时间条件
            "ORDER BY id DESC",
            "LIMIT 1",
            "</script>"
    })
    BizCloudTriageEvent queryLatestEventDetailByUniqueKey(@Param("eventId") String eventId,
            @Param("minCreateTime") Date minCreateTime);

    @Select({
            "<script>",
            "select * from biz_cloud_triage_event",
            " where event_id like concat('%', #{eventId}, '%')",
            " and create_time >= #{minCreateTime}",  // 添加创建时间条件
            "ORDER BY id DESC",
            "</script>"
    })
    List<BizCloudTriageEvent> queryAllEventDetailByUniqueKey(@Param("eventId") String eventId,
            @Param("minCreateTime") Date minCreateTime);


    @Select({
            "<script>",
            "select * from biz_cloud_triage_event",
            "where is_deleted = 0",
            "<if test='null != operationType and &apos;&apos; != operationType'>",
            "and operation_type = #{operationType}</if>",
            "<if test='null != statusList and statusList.size != 0'>",
            "and status in",
            "<foreach item='item' collection='statusList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != maxCreateTime'>and create_time &lt;= #{maxCreateTime}</if>",
            "<if test='null != minCreateTime'>and create_time &gt;= #{minCreateTime}</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "</script>"
    })
    List<BizCloudTriageEvent> queryByCreateTimeRangeAndOperationType(
            @Param("operationType") Integer operationType,
            @Param("statusList") List<Integer> statusList,
            @Param("maxCreateTime") Date maxCreateTime,
            @Param("minCreateTime") Date minCreateTime,
            @Param("operatorType") Integer operatorType);

    @Select({
            "<script>",
            "select * from biz_cloud_triage_event",
            "where is_deleted = 0",
            "<if test='null != operationType and &apos;&apos; != operationType'>",
            "and operation_type = #{operationType}</if>",
            "<if test='null != status and &apos;&apos; != status'>",
            "and status = #{status}</if>",
            "<if test='null != maxOperateStartTime'>and operate_start_time &lt;= #{maxOperateStartTime}</if>",
            "<if test='null != minOperateStartTime'>and operate_start_time &gt;= #{minOperateStartTime}</if>",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "</script>"
    })
    List<BizCloudTriageEvent> queryByOperateStartTimeRangeAndOperationType(
            @Param("operationType") Integer operationType,
            @Param("status") Integer status,
            @Param("maxOperateStartTime") Date maxOperateStartTime,
            @Param("minOperateStartTime") Date minOperateStartTime,
            @Param("operatorType") Integer operatorType);


    @Select({
            "<script>",
            "select * from biz_cloud_triage_event",
            "where is_deleted = 0",
            "<if test='null != vin'>and vin = #{vin}</if>",
            "<if test='null != eventTypeList and eventTypeList.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='eventTypeList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != statusList and statusList.size != 0'>",
            "and status in",
            "<foreach item='item' collection='statusList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            " and create_time &gt;= #{startTime}",
            " and create_time &lt;= #{endTime}",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            "</script>"
    })
    List<BizCloudTriageEvent> queryEventsByTimeVinStatus(
            @Param("vin") String vin,
            @Param("eventTypeList") List<Integer> eventTypeList,
            @Param("statusList") List<Integer> statusList,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("operatorType") Integer operatorType);

    /**
     * 根据eventDetailQueryDTO查询异常事件，支持指定检索输出顺序（默认为升序）
     * @param eventDetailQueryDTO
     * @return
     */
    @Select({
            "<script>",
            "select * from biz_cloud_triage_event",
            "where is_deleted = 0",
            "<if test='null != vin'>and vin = #{vin}</if>",
            "<if test='null != eventTime and &apos;&apos; != eventTime'>and event_time = #{eventTime}</if>",
            "<if test='null != eventTypeList and eventTypeList.size != 0'>",
            "and event_type in",
            "<foreach item='item' collection='eventTypeList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='null != statusList and statusList.size != 0'>",
            "and status in",
            "<foreach item='item' collection='statusList' open='(' close=')' separator=','>",
            "#{item}",
            "</foreach>",
            "</if>",
            " and create_time &gt;= #{startTime}",
            " and create_time &lt;= #{endTime}",
            "<if test='null != operatorType'>and operator_type = #{operatorType}</if>",
            " ORDER BY id DESC",
            "</script>"
    })
    List<BizCloudTriageEvent> queryEvents(EventDetailQueryDTO eventDetailQueryDTO);



}