package com.sankuai.walleops.cloud.triage.component;

import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperateHistoryPageDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleHistoryStatusDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class InformationApiServiceTest {

    @InjectMocks
    private final InformationApiService apiService = new InformationApiService();

    private static String statusUrl = "http://walledata.mad.test.sankuai.com/iiirc/openapi/vehicleStatusHistory";
    private static String statusResponse = "{\"ret\":0, \"msg\":\"success\", \"data\":" +
            "[{\"timestamp\":1,\"speed\":1,\"gear\":1,\"gearDesc\":\"1\",\"driveMode\":1,\"driveModeDesc\":\"自动驾驶\"," +
            "\"cockpitStatus\":1,\"cockpitStatusDesc\":\"1\",\"cockpitMisID\":\"cockpitMisID\",\"longitude\":1,\"latitude\":1}," +
            "{\"timestamp\":2,\"speed\":2,\"gear\":2,\"gearDesc\":\"2\",\"driveMode\":1,\"driveModeDesc\":\"自动驾驶\"," +
            "\"cockpitStatus\":2,\"cockpitStatusDesc\":\"2\",\"cockpitMisID\":\"cockpitMisID\",\"longitude\":2,\"latitude\":2}]}";

    private static String operateUrl = "http://walledata.mad.test.sankuai.com/iiirc/openapi/iiirc/openapi/madLogger/common";
    String responseOperate = "{\"ret\":0, \"msg\":\"success\", \"data\":" +
            "{\"page\":1,\"size\":10,\"content\":" +
            "[{\"logSource\":\"11\",\"logCategory\":\"11\",\"logType\":2,\"logName\":\"11\",\"logTag\":\"11\"," +
            "\"bizUniqueKey\":\"11\",\"vin\":\"11\",\"content\":\"11\",\"logTimestamp\":111,\"reportTimestamp\":11}," +
            "{\"logSource\":\"22\",\"logCategory\":\"22\",\"logType\":2,\"logName\":\"22\",\"logTag\":\"22\"," +
            "\"bizUniqueKey\":\"22\",\"vin\":\"22\",\"content\":\"22\",\"logTimestamp\":222,\"reportTimestamp\":22}]}}";

    @Test
    public void queryVehicleHistoryStatusTest() {
        Map<String, Object> param = new HashMap<>();
        param.put("vin", "11");
        param.put("startTimestamp", 100000);
        param.put("endTimestamp", 200000);

        List<VehicleHistoryStatusDTO> statusDTOList = apiService.queryVehicleHistoryStatus(param);

        assertNotNull(statusDTOList);
    }

    @Test
    public void queryOperateHistoryListTest() {
        Map<String, Object> param = new HashMap<>();
        param.put("eventId", "111");
        param.put("vin", "111");
        param.put("page", 1);
        param.put("size", 10);
        param.put("logSource", CommonConstant.EVENT_SOURCE_FE);

       // OperateHistoryPageDTO pageDTO = apiService.queryOperateHistoryList(param);

       // assertNotNull(pageDTO);
    }
}
