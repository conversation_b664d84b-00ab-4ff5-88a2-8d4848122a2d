package com.sankuai.walleops.cloud.triage.constant;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/8/28
 */
@AllArgsConstructor
@Getter
public enum EventStatusEnum {
    INITED(0, "未处理"),
    HANDLING(1, "处理中"),
    COMPLETED(2, "已完成"),
    CANCELED(3, "已取消"),
    REASSIGN(4, "已改派");

    private final Integer code;
    private final String msg;

    public static String getMsgByCode(Integer code) {
        for (EventStatusEnum status : EventStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getMsg();
            }
        }
        return null;
    }

    /**
     * 返回未处理状态列表
     */
    public static List<Integer> getUnCompletedStatus() {
        return Lists.newArrayList(INITED.getCode(), HANDLING.getCode());
    }

    /**
     * 根据状态码获取对应的事件状态枚举
     * @param code 状态码
     * @return 对应的事件状态枚举，如果不存在则返回null
     */
    public static EventStatusEnum getByCode(Integer code){
        if (Objects.isNull(code)) {
            return null;
        }
        for (EventStatusEnum status : EventStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断事件是否已完成
     *
     * @param code
     * @return
     */
    public static boolean isCompleted(Integer code) {
        return code > HANDLING.code;
    }

}
