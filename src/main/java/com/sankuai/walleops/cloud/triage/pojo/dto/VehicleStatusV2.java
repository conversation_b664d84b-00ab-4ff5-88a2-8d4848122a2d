package com.sankuai.walleops.cloud.triage.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆状态 v2 接口数据结构
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VehicleStatusV2 {

    /**
     * 车辆VIN码
     */
    private String vin;

    /**
     * 车辆名称
     */
    private String name;

    /**
     * 车牌号
     */
    private String licenseNo;

    /**
     * 在线状态
     */
    private IntegerItem online;

    /**
     * 座舱连接状态
     * 0: 坐席未连接
     * 1: 坐席未连接
     * 2: 坐席连接中(云代驾)
     * 3: 请求连接中
     * 4: 坐席连接中(云辅助)
     * 5: 坐席降级投车中(云辅助)
     */
    private IntegerItem cockpitStatus;

    /**
     * 纬度
     */
    private IntegerItem latitude;

    /**
     * 经度
     */
    private IntegerItem longitude;

    /**
     * 里程
     */
    private IntegerItem mileage;

}
