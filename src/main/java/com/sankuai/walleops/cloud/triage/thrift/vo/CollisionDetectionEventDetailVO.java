package com.sankuai.walleops.cloud.triage.thrift.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "碰撞检测事件详情查询详情")
public class CollisionDetectionEventDetailVO {

    @ThriftField(1)
    @FieldDoc(description = "事件ID")
    private String eventId;

    @ThriftField(2)
    @FieldDoc(description = "车架号")
    private String vin;

    @ThriftField(3)
    @FieldDoc(description = "车辆名称")
    private String vehicleName;

    @ThriftField(4)
    @FieldDoc(description = "车牌号")
    private String vehicleId;

    @ThriftField(5)
    @FieldDoc(description = "异常事件发生时间")
    private String eventTime;

    @ThriftField(6)
    @FieldDoc(description = "事件判定结果")
    private Integer checkResult;

    @ThriftField(7)
    @FieldDoc(description = "视频链接列表")
    private List<String> videoLinkList;
}
