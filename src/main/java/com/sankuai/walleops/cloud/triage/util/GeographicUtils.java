package com.sankuai.walleops.cloud.triage.util;


import com.sankuai.walleops.cloud.triage.pojo.dto.GeographicPoint;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Created by leihui on 2020/9/23.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GeographicUtils {

    private static double PI_VALUE = Math.PI;
    private static final int NOR_SOUTH_EAST_ZONE = 50;

    /* Ellipsoid model constants (actual values here are for WGS84) */
    private static double sm_a = 6378137.0;
    private static double sm_b = 6356752.314;

    private static double UTMScaleFactor = 0.9996;

    /**
     * DegToRad
     * <p>
     * Converts degrees to radians.
     */
    public static double degToRad(double deg) {
        return (deg / 180.0 * PI_VALUE);
    }

    /**
     * RadToDeg
     * <p>
     * Converts radians to degrees.
     */
    public static double radToDeg(double rad) {
        return (rad / PI_VALUE * 180.0);
    }

    /**
     * ArcLengthOfMeridian
     * <p>
     * Computes the ellipsoidal distance from the equator to a point at a given
     * latitude.
     * <p>
     * Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
     * GPS: Theory and Practice, 3rd ed. New York: Springer-Verlag Wien, 1994.
     * <p>
     * Inputs: phi - Latitude of the point, in radians.
     * <p>
     * Globals: sm_a - Ellipsoid model major axis. sm_b - Ellipsoid model minor
     * axis.
     * <p>
     * Returns: The ellipsoidal distance of the point from the equator, in
     * meters.
     */
    private static double arcLengthOfMeridian(double phi) {
        double alpha, beta, gamma, delta, epsilon, n;
        double result;

        /* Precalculate n */
        n = (sm_a - sm_b) / (sm_a + sm_b);

        /* Precalculate alpha */
        alpha = ((sm_a + sm_b) / 2.0) * (1.0 + (Math.pow(n, 2.0) / 4.0) + (Math.pow(n, 4.0) / 64.0));

        /* Precalculate beta */
        beta = (-3.0 * n / 2.0) + (9.0 * Math.pow(n, 3.0) / 16.0) + (-3.0 * Math.pow(n, 5.0) / 32.0);

        /* Precalculate gamma */
        gamma = (15.0 * Math.pow(n, 2.0) / 16.0) + (-15.0 * Math.pow(n, 4.0) / 32.0);

        /* Precalculate delta */
        delta = (-35.0 * Math.pow(n, 3.0) / 48.0) + (105.0 * Math.pow(n, 5.0) / 256.0);

        /* Precalculate epsilon */
        epsilon = (315.0 * Math.pow(n, 4.0) / 512.0);

        /* Now calculate the sum of the series and return */
        result = alpha
                * (phi + (beta * Math.sin(2.0 * phi)) + (gamma * Math.sin(4.0 * phi))
                + (delta * Math.sin(6.0 * phi)) + (epsilon * Math.sin(8.0 * phi)));

        return result;
    }

    /**
     * UTMCentralMeridian
     * <p>
     * Determines the central meridian for the given UTM zone.
     * <p>
     * Inputs: zone - An integer value designating the UTM zone, range [1,60].
     * <p>
     * Returns: The central meridian for the given UTM zone, in radians, or zero
     * if the UTM zone parameter is outside the range [1,60]. Range of the
     * central meridian is the radian equivalent of [-177,+177].
     */
    private static double utmCentralMeridian(int zone) {
        zone = zone < 0 ? NOR_SOUTH_EAST_ZONE : zone;
        double cmeridian;

        cmeridian = degToRad(-183.0 + (zone * 6.0));

        return cmeridian;
    }

    /**
     * FootpointLatitude
     * <p>
     * Computes the footpoint latitude for use in converting transverse Mercator
     * coordinates to ellipsoidal coordinates.
     * <p>
     * Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
     * GPS: Theory and Practice, 3rd ed. New York: Springer-Verlag Wien, 1994.
     * <p>
     * Inputs: y - The UTM northing coordinate, in meters.
     * <p>
     * Returns: The footpoint latitude, in radians.
     */
    private static double footPointLatitude(double y) {
        double yRadians, alpha, beta, gamma, delta, epsilon, n;
        double result;

        /* Precalculate n (Eq. 10.18) */
        n = (sm_a - sm_b) / (sm_a + sm_b);

        /* Precalculate alpha (Eq. 10.22) */
        /* (Same as alpha in Eq. 10.17) */
        alpha = ((sm_a + sm_b) / 2.0) * (1 + (Math.pow(n, 2.0) / 4) + (Math.pow(n, 4.0) / 64));

        /* Precalculate yRadians (Eq. 10.23) */
        yRadians = y / alpha;

        /* Precalculate beta (Eq. 10.22) */
        beta = (3.0 * n / 2.0) + (-27.0 * Math.pow(n, 3.0) / 32.0) + (269.0 * Math.pow(n, 5.0) / 512.0);

        /* Precalculate gamma (Eq. 10.22) */
        gamma = (21.0 * Math.pow(n, 2.0) / 16.0) + (-55.0 * Math.pow(n, 4.0) / 32.0);

        /* Precalculate delta (Eq. 10.22) */
        delta = (151.0 * Math.pow(n, 3.0) / 96.0) + (-417.0 * Math.pow(n, 5.0) / 128.0);

        /* Precalculate epsilon (Eq. 10.22) */
        epsilon = (1097.0 * Math.pow(n, 4.0) / 512.0);

        /* Now calculate the sum of the series (Eq. 10.21) */
        result = yRadians + (beta * Math.sin(2.0 * yRadians)) + (gamma * Math.sin(4.0 * yRadians))
                + (delta * Math.sin(6.0 * yRadians)) + (epsilon * Math.sin(8.0 * yRadians));

        return result;
    }

    /**
     * MapLatLonToXY
     * <p>
     * Converts a latitude/longitude pair to x and y coordinates in the
     * Transverse Mercator projection. Note that Transverse Mercator is not the
     * same as UTM; a scale factor is required to convert between them.
     * <p>
     * Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
     * GPS: Theory and Practice, 3rd ed. New York: Springer-Verlag Wien, 1994.
     * <p>
     * Inputs: phi - Latitude of the point, in radians. lambda - Longitude of
     * the point, in radians. lambda0 - Longitude of the central meridian to be
     * used, in radians.
     * <p>
     * Outputs: xy - A 2-element array containing the x and y coordinates of the
     * computed point.
     * <p>
     * Returns: The function does not return a value.
     */
    private static GeographicPoint mapLatLonToXy(double phi, double lambda, double lambda0) {
        double[] xy = new double[2];
        double n, nu2, ep2, t, t2, l;
        double l3coef, l4coef, l5coef, l6coef, l7coef, l8coef;
        double tmp;

        /* Precalculate ep2 */
        ep2 = (Math.pow(sm_a, 2.0) - Math.pow(sm_b, 2.0)) / Math.pow(sm_b, 2.0);

        /* Precalculate nu2 */
        nu2 = ep2 * Math.pow(Math.cos(phi), 2.0);

        /* Precalculate n */
        n = Math.pow(sm_a, 2.0) / (sm_b * Math.sqrt(1 + nu2));

        /* Precalculate t */
        t = Math.tan(phi);
        t2 = t * t;
        tmp = (t2 * t2 * t2) - Math.pow(t, 6.0);

        /* Precalculate l */
        l = lambda - lambda0;

        /*
         * Precalculate coefficients for l**n in the equations below so a normal
         * human being can read the expressions for easting and northing -- l**1
         * and l**2 have coefficients of 1.0
         */
        l3coef = 1.0 - t2 + nu2;

        l4coef = 5.0 - t2 + 9 * nu2 + 4.0 * (nu2 * nu2);

        l5coef = 5.0 - 18.0 * t2 + (t2 * t2) + 14.0 * nu2 - 58.0 * t2 * nu2;

        l6coef = 61.0 - 58.0 * t2 + (t2 * t2) + 270.0 * nu2 - 330.0 * t2 * nu2;

        l7coef = 61.0 - 479.0 * t2 + 179.0 * (t2 * t2) - (t2 * t2 * t2);

        l8coef = 1385.0 - 3111.0 * t2 + 543.0 * (t2 * t2) - (t2 * t2 * t2);

        /* Calculate easting (x) */
        xy[0] = n * Math.cos(phi) * l + (n / 6.0 * Math.pow(Math.cos(phi), 3.0) * l3coef * Math.pow(l, 3.0))
                + (n / 120.0 * Math.pow(Math.cos(phi), 5.0) * l5coef * Math.pow(l, 5.0))
                + (n / 5040.0 * Math.pow(Math.cos(phi), 7.0) * l7coef * Math.pow(l, 7.0));

        /* Calculate northing (y) */
        xy[1] = arcLengthOfMeridian(phi) + (t / 2.0 * n * Math.pow(Math.cos(phi), 2.0) * Math.pow(l, 2.0))
                + (t / 24.0 * n * Math.pow(Math.cos(phi), 4.0) * l4coef * Math.pow(l, 4.0))
                + (t / 720.0 * n * Math.pow(Math.cos(phi), 6.0) * l6coef * Math.pow(l, 6.0))
                + (t / 40320.0 * n * Math.pow(Math.cos(phi), 8.0) * l8coef * Math.pow(l, 8.0));

        return new GeographicPoint(xy[0], xy[1]);
    }

    /**
     * MapXYToLatLon
     * <p>
     * Converts x and y coordinates in the Transverse Mercator projection to a
     * latitude/longitude pair. Note that Transverse Mercator is not the same as
     * UTM; a scale factor is required to convert between them.
     * <p>
     * Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
     * GPS: Theory and Practice, 3rd ed. New York: Springer-Verlag Wien, 1994.
     * <p>
     * Inputs: x - The easting of the point, in meters. y - The northing of the
     * point, in meters. lambda0 - Longitude of the central meridian to be used,
     * in radians.
     * <p>
     * Outputs: philambda - A 2-element containing the latitude and longitude in
     * radians.
     * <p>
     * Returns: The function does not return a value.
     * <p>
     * Remarks: The local variables Nf, nuf2, tf, and tf2 serve the same purpose
     * as N, nu2, t, and t2 in MapLatLonToXY, but they are computed with respect
     * to the footpoint latitude phif.
     * <p>
     * x1frac, x2frac, x2poly, x3poly, etc. are to enhance readability and to
     * optimize computations.
     */
    public static GeographicPoint mapXyToLatLon(double x, double y, double lambda0) {
        double phif, nf, nfpow, nuf2, ep2, tf, tf2, tf4, cf;
        double x1frac, x2frac, x3frac, x4frac, x5frac, x6frac, x7frac, x8frac;
        double x2poly, x3poly, x4poly, x5poly, x6poly, x7poly, x8poly;
        double[] philambda = new double[2];

        /* Get the value of phif, the footpoint latitude. */
        phif = footPointLatitude(y);

        /* Precalculate ep2 */
        ep2 = (Math.pow(sm_a, 2.0) - Math.pow(sm_b, 2.0)) / Math.pow(sm_b, 2.0);

        /* Precalculate cos (phif) */
        cf = Math.cos(phif);

        /* Precalculate nuf2 */
        nuf2 = ep2 * Math.pow(cf, 2.0);

        /* Precalculate nf and initialize nfpow */
        nf = Math.pow(sm_a, 2.0) / (sm_b * Math.sqrt(1 + nuf2));
        nfpow = nf;

        /* Precalculate tf */
        tf = Math.tan(phif);
        tf2 = tf * tf;
        tf4 = tf2 * tf2;

        /*
         * Precalculate fractional coefficients for x**n in the equations below
         * to simplify the expressions for latitude and longitude.
         */
        x1frac = 1.0 / (nfpow * cf);

        nfpow *= nf; /* now equals nf**2) */
        x2frac = tf / (2.0 * nfpow);

        nfpow *= nf; /* now equals nf**3) */
        x3frac = 1.0 / (6.0 * nfpow * cf);

        nfpow *= nf; /* now equals nf**4) */
        x4frac = tf / (24.0 * nfpow);

        nfpow *= nf; /* now equals nf**5) */
        x5frac = 1.0 / (120.0 * nfpow * cf);

        nfpow *= nf; /* now equals nf**6) */
        x6frac = tf / (720.0 * nfpow);

        nfpow *= nf; /* now equals nf**7) */
        x7frac = 1.0 / (5040.0 * nfpow * cf);

        nfpow *= nf; /* now equals nf**8) */
        x8frac = tf / (40320.0 * nfpow);

        /*
         * Precalculate polynomial coefficients for x**n. -- x**1 does not have
         * a polynomial coefficient.
         */
        x2poly = -1.0 - nuf2;

        x3poly = -1.0 - 2 * tf2 - nuf2;

        x4poly = 5.0 + 3.0 * tf2 + 6.0 * nuf2 - 6.0 * tf2 * nuf2 - 3.0 * (nuf2 * nuf2) - 9.0 * tf2 * (nuf2 * nuf2);

        x5poly = 5.0 + 28.0 * tf2 + 24.0 * tf4 + 6.0 * nuf2 + 8.0 * tf2 * nuf2;

        x6poly = -61.0 - 90.0 * tf2 - 45.0 * tf4 - 107.0 * nuf2 + 162.0 * tf2 * nuf2;

        x7poly = -61.0 - 662.0 * tf2 - 1320.0 * tf4 - 720.0 * (tf4 * tf2);

        x8poly = 1385.0 + 3633.0 * tf2 + 4095.0 * tf4 + 1575 * (tf4 * tf2);

        /* Calculate latitude */
        philambda[0] = phif + x2frac * x2poly * (x * x) + x4frac * x4poly * Math.pow(x, 4.0) + x6frac * x6poly
                * Math.pow(x, 6.0) + x8frac * x8poly * Math.pow(x, 8.0);

        /* Calculate longitude */
        philambda[1] = lambda0 + x1frac * x + x3frac * x3poly * Math.pow(x, 3.0) + x5frac * x5poly
                * Math.pow(x, 5.0) + x7frac * x7poly * Math.pow(x, 7.0);

        philambda[0] = radToDeg(philambda[0]);
        philambda[1] = radToDeg(philambda[1]);

        return new GeographicPoint(philambda[0], philambda[1]);
    }

    /**
     * LatLonToUTMXY
     * <p>
     * Converts a latitude/longitude pair to x and y coordinates in the
     * Universal Transverse Mercator projection.
     * <p>
     * Inputs: lat - Latitude of the point, in radians. lon - Longitude of the
     * point, in radians. zone - UTM zone to be used for calculating values for
     * x and y. If zone is less than 1 or greater than 60, the routine will
     * determine the appropriate zone from the value of lon.
     * <p>
     * Outputs: xy - A 2-element array where the UTM x and y values will be
     * stored.
     * <p>
     * Returns: The UTM zone used for calculating the values of x and y.
     */
    public static GeographicPoint latLonToUtmXy(double lat, double lon, int zone) {
        double[] xy = new double[2];
        lat = degToRad(lat);
        lon = degToRad(lon);
        GeographicPoint point = mapLatLonToXy(lat, lon, utmCentralMeridian(zone));

        /* Adjust easting and northing for UTM system. */
        point.setLatitude(point.getLatitude() * UTMScaleFactor + 500000.0);
        point.setLongitude(point.getLongitude() * UTMScaleFactor);
        if (xy[1] < 0.0) {
            xy[1] = xy[1] + 10000000.0;
        }
        return point;
    }

    public static GeographicPoint utmXyToLatLon(double x, double y) {
        return utmXyToLatLon(x, y, NOR_SOUTH_EAST_ZONE, false);
    }

    /**
     * UTMXYToLatLon
     * <p>
     * Converts x and y coordinates in the Universal Transverse Mercator
     * projection to a latitude/longitude pair.
     * <p>
     * Inputs: x - The easting of the point, in meters. y - The northing of the
     * point, in meters. zone - The UTM zone in which the point lies. southhemi
     * - True if the point is in the southern hemisphere; false otherwise.
     * <p>
     * Outputs: latlon - A 2-element array containing the latitude and longitude
     * of the point, in radians.
     * <p>
     * Returns: The function does not return a value.
     */
    public static GeographicPoint utmXyToLatLon(double x, double y, int zone, boolean isOnSouthernHemisphere) {
        double cmeridian;

        x -= 500000.0;
        x /= UTMScaleFactor;

        /* If in southern hemisphere, adjust y accordingly. */
        if (isOnSouthernHemisphere)
            y -= 10000000.0;

        y /= UTMScaleFactor;

        cmeridian = utmCentralMeridian(zone);

        return mapXyToLatLon(x, y, cmeridian);
    }

    public static double get2PointsDistance(double longitude1, double latitude1,
                                            double longitude2, double latitude2) {
        // 纬度
        double lat1 = rad(latitude1);
        double lat2 = rad(latitude2);
        // 两点纬度之差
        double latDiff = lat1 - lat2;
        // 经度之差
        double longDiff = rad(longitude1) - rad(longitude2);
        // 计算两点距离的公式
        double s = 2 * Math.asin(Math.sqrt(Math.pow(
                Math.sin(latDiff / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(longDiff / 2), 2))
        );
        // 弧长乘地球半径（半径为米）
        s = s * 6378137.0;
        // 精确距离的数值
        s = Math.round(s * 10000d) / 10000d;
        return s;
    }

    public static double rad(double d) {
        // 角度转换成弧度
        return d * Math.PI / 180.00;
    }
}
