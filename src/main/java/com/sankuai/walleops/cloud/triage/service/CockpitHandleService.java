package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import java.util.List;

public interface CockpitHandleService {

    /**
     * 查询未处理的碰撞检测事件列表
     *
     * @param vin
     * @return
     */
    List<BizCloudTriageEvent> queryUnCheckCollisionDetectionEventList(String vin);

    /**
     * 判断是否灰度
     *
     * @param eventType
     * @param purpose
     * @return
     */
    Boolean isInGray(Integer eventType, String purpose);

    /**
     * 取消呼叫云控操作
     *
     * @param vin
     * @param type
     */
    void requestCloudOperation(String vin, Integer type, String action) throws Exception;

    /**
     * 处理碰撞检测事件
     *
     * @param event
     */
    void handleCollisionDetectionEvent(BizCloudTriageEvent event);

}
