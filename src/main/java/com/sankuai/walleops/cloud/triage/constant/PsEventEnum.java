package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PsEventEnum {
    /*
           该枚举值用于保障系统传递的消息类型，目前只从保障系统接收 堵路告警 事件（Ps 表示保障系统）
           该枚举类中只有1个值 ，那么当调用 getTypeByCode 函数获取 type时，
           如果是其他类型的事故则会自动过滤掉
     */

    //COLLISION_DETECTION(1001, "事故检测",1);
    TRAFFICCONGESTION_ALERT(1003,"堵路告警", 23),
    TRAFFICCONGESTION_ALERT_END(1004,"堵路告警结束", 25);

    private final Integer code;
    private final String msg;
    private final Integer type;

    public static Integer getTypeByCode(Integer code) {
        for (PsEventEnum event : PsEventEnum.values()) {
            if (event.getCode().equals(code)) {
                return event.getType();
            }
        }
        return null; // 如果找不到对应的 code 值，则返回 null
    }
}
