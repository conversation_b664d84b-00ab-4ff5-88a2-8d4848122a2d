package com.sankuai.walleops.cloud.triage.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.service.ExternalInterfaceService;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpGet;
import org.springframework.stereotype.Service;
@Service
@Slf4j
public class ExternalInterfaceServiceImpl implements ExternalInterfaceService {

    @MdpConfig("information.center.url.prefix")
    String hostName;

    @MdpConfig("evaluation.alerts.interface.ba.clientId")
    private String clientId;

    @MdpConfig("evaluation.alerts.interface.ba.clientSecret")
    private  String clientSecret;

    @MdpConfig("get.location.name.api.key")
    private String getLocationNameApiKey;

    @MdpConfig("daXiangServiceHostname")
    private String queryRescueOrderStatusHostName;

    private final String AlertReflectUrlPath = "/scene-ranking/v1/evaluation-alerts";

    @Override
    public Map<String, Map<String, String>> getEvaluationAlertsReflect() {

        String request = new StringBuilder(hostName).append(AlertReflectUrlPath).toString();
        try {
            Map<String, String> headers = new HashMap<>();
            String authorUrl= "/v1/evaluation-alerts";
            String authorization = CommonUtil.getAuthorization(authorUrl, new HttpGet().getMethod(), CommonUtil.getDate(), clientId, clientSecret);
            headers.put("Date", CommonUtil.getDate());
            headers.put("Authorization", authorization);
            String response = CommonUtil.doGet(request, null, headers );
            log.info("response = {}", response);
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Map<String, String>> result = objectMapper.convertValue( objectMapper.readTree(response).get("data"), new TypeReference<Map<String, Map<String, String>>>() {});
            if (result != null) {
                return result;
            }
        }
        catch (Exception e){
            log.error("getEvaluationAlertsReflect is failed", e);
        }

        return new HashMap<>();
    }

    @Override
    public String getLocationNameByGps(String locationGps) {
        if(StringUtils.isBlank(locationGps)){
            return "未知";
        }
        String queryLocationNamePath = "https://lbsapi.sankuai.com/v1/location/regeo";
        Map<String,Object> params = new HashMap<>();
        params.put("location", locationGps);
        params.put("key", getLocationNameApiKey);
        try {
            String response = CommonUtil.doGet(queryLocationNamePath, params);
            log.info("getLocationNameByGps，params = {},response = {}",params, response);
            if(StringUtils.isNotBlank(response)){
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.readTree(response);
                int status = jsonNode.get("status").asInt();
                if(status == 200){
                    return jsonNode.get("regeocode").get(0).get("formatted_address").asText();
                }
            }
        } catch (Exception e) {
            log.error("getLocationNameByGps is failed, locationGps = {}", locationGps, e);
        }
        return "未知";
    }

    /**
     * 获取救援订单状态
     *
     * @param vin
     * @return
     */
    @Override
    public Boolean getRescueOrderStatus(String vin) {
        String path = String.format("/eve/output/rest/rescue/get-order-status?vin=%s", vin);
        String request = new StringBuilder(queryRescueOrderStatusHostName).append(path).toString();
        try {
            String response = CommonUtil.doGet(request, null);
            log.info("getRescueOrderStatus, request =[{}], response = {}", request, response);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(response);
            int status = jsonNode.get("code").asInt();
            if(status == 200){
                //返回true则表示该车辆处于救援状态，需要过滤
                return jsonNode.get("data").get("hasRescue").asBoolean();
            }
        } catch (Exception e){
            log.error("getRescueOrderStatus is failed, request = [{}]",request, e);
        }
        return false;
    }
}
