package com.sankuai.walleops.cloud.triage.constant;

import java.util.Arrays;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@AllArgsConstructor
@Getter
public enum EventTypeEnum {
    UNKNOWN(-1, "unknown", "未知"),
    VEHICLE_STANDSTILL(0, "vehicle-standstill", "车辆停滞不前"),
    COLLISION_DETECTION(1, "collision-detection", "L2H事故检测"),
    DEPARTURE_FAILED(2, "departure-failed", "信号不佳，移至门口发车"),
    DEPARTURE_TIMEOUT(3, "departure-timeout", "发车超时"),
    ARRIVE_TIMEOUT(4, "arrive-timeout", "到达超时"),
    DEPARTURE_HOME(5, "departure-home", "发车-HOME"),
    ACCIDENT(6, "accident", "事故"),
    DEPARTURE_STATION(7, "departure-station", "发车-停靠点"),
    COREDUMP(8, "coredump", "恢复行驶"),
    ROUTING_SEND_FAILED(9, "routing-send-failed", "路由下发失败"),
    ROUTING_GENERATE_FAILED(10, "routing-generate-failed", "路由生成失败"),
    START_AUTO_FAILED(11, "start-auto-failed", "启动自动驾驶失败"),
    DOCKING_TIMEOUT(12, "docking-timeout", "停靠超时"),
    AUTODRIVE_ABNORMAL_EXIT(13, "autodrive-abnormal-exit", "自动驾驶异常退出"),
    POWER_WARNING(14, "power-warning", "电量告警"),
    AUTODRIVE_PROCESS_ABNORMAL_EXIT(15, "autodrive-process-abnormal-exit", "自动驾驶程序异常退出"),
    BATTERY_REPLACE_STOP(16, "battery-replace-stop", "电池需换电请停车"),
    ROADBLOCKAGE_MOVEVEHICLE(17, "roadBlockage-MoveVehicle", "堵路需挪车"),
    AUTOPARKING_NOT_MEETEXPECTATION(18, "autoParking-MeetExpectation", "自动泊车不符合预期"),
    PARKINGPOSITIONNOTREASONABLE_MOVEVEHICLE(19, "parkingPosition-MoveVehicle", "车辆停靠位置不合理需挪车"),
    RIDERHOLDGOODS_PARKING(20, "riderHoldGoods-Parking", "骑手未取完货需停车"),
    OTHER(21, "other", "其他"),
    ROUTING_ISSUE(22, "routing-issue", "路由问题"),
    TRAFFICCONGESTION_ALERT(23, "trafficCongestion-alert", "堵路告警"),
    COLLISION_DETECTION_LOW(24, "collision-detection-low", "L2L事故检测"),
    TRAFFICCONGESTION_ALERT_END(25, "trafficCongestion-alert-end", "堵路告警结束"),
    COLLISION_DETECTION_HIGH(26, "collision-detection-high", "L1事故检测"),
    ORDER_COMPLETED_RETURN(27, "order-completed-return", "订单完成可以返回"),
    VEHICLE_NO_PARK_LONG_TIME(28, "vehicle-not-parked-long-time", "车辆长时间没有驻车"),
    CLOSE_CLEANING_SENSOR(29, "close-cleaning-sensor", "关闭清洁传感器"),
    VIRTUAL_ORDER_DEPARTURE(30, "virtual-order-departure", "虚拟单发车"),
    ROAD_TEST_DEPARTURE(31, "road-test-departure", "路测发车"),
    PUBLIC_REMOVAL(32, "public-removal", "公众挪车"),
    AUXILIARY_ROAD_TO_MAIN_ROAD(33, "auxiliary-road-to-main-road", "辅路车借道主路"),
    MAIN_ROAD_TO_AUXILIARY_ROAD_RETURN(34, "main-road-to-auxiliary-road-return", "主路车误入辅路后返回"),
    RESUME_ROUTE_AFTER_RETURN(35, "resume-route-after-return", "掉头后恢复路由"),
    PULL_OVER_AND_EXIT_CONTROL(36, "pull-over-and-exit-control", "靠边停车退控"),
    SCAN_CODE_TO_LIFT_BARRIER(37, "scan-code-to-lift-barrier", "扫码抬杆"),

    RETROGRADE(40, "retrograde", "占用逆向车道停滞"),
    SPECIAL_AREA_STRANDING(41, "special_area_stranding", "路口施工区域停滞"),
    DRIVE_ON_TRAFFIC_LINE(42, "drive_on_traffic_line", "非法压线停滞");

    private final Integer code;
    private final String eventName;
    private final String msg;

    public static EventTypeEnum getByCode(int code) {
        for (EventTypeEnum eventTypeEnum : EventTypeEnum.values()) {
            if (eventTypeEnum.getCode().equals(code)) {
                return eventTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public static Integer getCodeByEventName(String eventName) {
        for (EventTypeEnum eventTypeEnum : EventTypeEnum.values()) {
            if (eventTypeEnum.getEventName().equals(eventName)) {
                return eventTypeEnum.getCode();
            }
        }
        return -1;
    }

    /**
     * 根据事件代码获取事件名称
     *
     * @param code
     * @return
     */
    public static String getMsgByCode(Integer code) {
        for (EventTypeEnum eventTypeEnum : EventTypeEnum.values()) {
            if (eventTypeEnum.getCode().equals(code)) {
                return eventTypeEnum.getMsg();
            }
        }
        return null;
    }

    public static Integer getCodeByMsg(String msg) {
        for (EventTypeEnum eventType : EventTypeEnum.values()) {
            if (eventType.getMsg().equals(msg)) {
                return eventType.getCode();
            }
        }
        return -1; // 如果找不到对应的msg，则返回-1或其他适当的默认值
    }

    /**
     * 获取所有碰撞检测类型的事件代码列表
     *
     * @return 包含所有碰撞检测类型事件代码的列表
     */
    public static List<Integer> getCollisionDetectionTypeList() {
        // 创建一个包含所有碰撞检测类型事件代码的列表
        return Arrays.asList(EventTypeEnum.COLLISION_DETECTION_LOW.code, // 低级别碰撞检测事件代码
                EventTypeEnum.COLLISION_DETECTION_HIGH.code, // 高级别碰撞检测事件代码
                EventTypeEnum.COLLISION_DETECTION.code); // 一般碰撞检测事件代码
    }
}
