package com.sankuai.walleops.cloud.triage.pojo.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/05
 */
@Data
public class AccidentDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 车架号
     */
    private String vin;

    private String vehicleId;

    private String vehicleName;

    /**
     * record名称
     */
    private String recordName;

    private String title;

    /**
     * 事故发生的时间，这个事故时间，有可能是经过推理，比如最近一次接管的时间
     */
    private Long accidentTimestamp;

    /**
     * 安全员上报的事故时间
     */
    private Long reportAccidentTimestamp;

    private Long siteDisposeTimestamp;

    /**
     * 事故发生的位置地名
     */
    private String locationName;

    /**
     * 事故发生的位置经纬度坐标
     */
    private String locationGps;

    /**
     * 事故上报人的mis号
     */
    private String reporter;

    /**
     * 近场安全员的mis号
     */
    private String siteMisId;

    /**
     * 远遥安全员的mis号
     */
    private String remoteMisId;

    /**
     * 快速上传任务的id
     */
    private String uploadTaskId;

    /**
     * 事故描述
     */
    private String accidentDesc;

    /**
     * 事故等级
     */
    private Integer accidentLevel;

    private String accidentLevelDesc;

    /**
     * 我方责任类型
     */
    private Integer responsibilityType;

    private String responsibilityTypeDesc;

    /**
     * 事故类型
     */
    private Integer accidentType;

    private String accidentTypeDec;

    /**
     * 自动驾驶责任模块
     */
    private String responsibilityModule;


    /**
     * 事故责任类型
     */
    private String accidentResponsibility;

    /**
     * 事故责任描述
     */
    private String accidentResponsibilityDesc;

    /**
     * 事故状态：10新建|11处理中|12处理完成
     */
    private Integer status;

    private String statusDesc;

    private Integer reportSource;

    private String reportSourceDesc;

    private String content;

    public String getExtraInfo() {
        SimplePropertyPreFilter filter = new SimplePropertyPreFilter(this.getClass(), "id",
                "title", "locationName", "reporter", "status", "reportSourceDesc");
        return JSON.toJSONString(this, filter);
    }
}