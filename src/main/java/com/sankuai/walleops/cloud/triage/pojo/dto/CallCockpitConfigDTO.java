package com.sankuai.walleops.cloud.triage.pojo.dto;

import static com.sankuai.walleops.cloud.triage.constant.CommonConstant.ALL_STRING;

import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CallCockpitConfigDTO {

    /**
     * 呼叫云控原因
     */
    private Integer reason;

    /**
     * 灰度用车目的列表
     */
    private Set<String> grayPurposeList;

    /**
     * 判断是否处于灰度范围内
     *
     * @param purpose
     * @return
     */
    public boolean isInGrayScope(String purpose) {
        return grayPurposeList.contains(ALL_STRING) || grayPurposeList.contains(purpose);
    }
}
