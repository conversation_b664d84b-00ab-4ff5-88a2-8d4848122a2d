package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum VhrDescEnum {
    VHR_EQUAL_ONE(0, "VRH=1"),
    VHR_GREATER_ONE(1, "VRH>1");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code code值
     * @return VhrDescEnum
     */
    public static VhrDescEnum getByCode(Integer code) {
        for (VhrDescEnum vhrDescEnum : VhrDescEnum.values()) {
            if (vhrDescEnum.getCode().equals(code)) {
                return vhrDescEnum;
            }
        }
        return null;
    }
}
