package com.sankuai.walleops.cloud.triage.controller;

import com.sankuai.walleeve.dto.TokenCheckDTO;
import com.sankuai.walleops.cloud.triage.component.VenusService;
import com.sankuai.walleops.cloud.triage.component.WechatAuthAdminService;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.pojo.response.ExternalSystemResponse;
import com.sankuai.walleops.cloud.triage.service.S3Service;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import com.sankuai.walleops.cloud.triage.util.exception.ParamInputErrorException;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/output/api")
@Slf4j
public class S3Controller {

    @Resource
    private S3Service s3Service;

    @Resource
    private WechatAuthAdminService authAdminService;

    @Resource
    private VenusService venusService;

    /**
     * 获取普通上传签名
     * @param token 用户登录态token
     * @param fileName 文件名
     * @return 通用响应对象
     */
    @GetMapping("/s3/getNormalUploadSign")
    public CommonResponse getNormalUploadSign(@RequestHeader("token") String token, String fileName) {
        log.info("getNormalUploadSign, fileName = {}", fileName);
        try {

            // 1 登陆态校验
            InputCheckUtil.isNotBlank(token, "用户登陆态不可为空");
            TokenCheckDTO tokenCheckDTO = authAdminService.checkToken(token);
            if (Objects.isNull(tokenCheckDTO) || !tokenCheckDTO.getIsValid()) {
                return CommonResponse.builder().ret(HttpStatus.UNAUTHORIZED.value()).msg("登陆态过期，请重新登陆")
                        .build();
            }
            // 2 入参校验 车牌号
            InputCheckUtil.isNotBlank(fileName, "文件名不可为空");
            return CommonResponse.success(s3Service.getNormalUploadSign(fileName));
        } catch (ParamInputErrorException e) {
            return CommonResponse.failed(e.getMessage());
        } catch (Exception e) {
            log.error("getNormalUploadSign error", e);
            return CommonResponse.failed("system error");
        }

    }

    /**
     * 获取Venus系统的访问令牌
     * @param token 用户登录态token
     * @return 外部系统响应对象
     */
    @PostMapping("/venus/getToken")
    public ExternalSystemResponse getVenusToken(@RequestHeader("token") String token) {
        log.info("getVenusToken, token = {}", token);
        try {
            // 1 登陆态校验
            InputCheckUtil.isNotBlank(token, "用户登陆态不可为空");
            TokenCheckDTO tokenCheckDTO = authAdminService.checkToken(token);
            if (Objects.isNull(tokenCheckDTO) || !tokenCheckDTO.getIsValid()) {
                return ExternalSystemResponse.builder().code(HttpStatus.UNAUTHORIZED.value()).msg("登陆态过期，请重新登陆")
                        .build();
            }
            return ExternalSystemResponse.success(venusService.getToken());
        } catch (Exception e) {
            log.error("getVenusToken error", e);
            return ExternalSystemResponse.failed("system error");
        }
    }


}
