package com.sankuai.walleops.cloud.triage.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class PsEventDTO {
    @Data
    public class EventContentDTO{
        @J<PERSON>NField(name = "longitude")
        private Double longitude;

        @JSONField(name = "latitude")
        private Double latitude;

        @JSONField(name = "evaluation_alert")
        private String evaluationAlert;

        @JSONField(name = "record_name")
        private String recordName;

        @JSONField(name = "accident_level")
        private Integer accidentLevel;
    }

    @JSONField(name = "event_id")
    private String eventId;

    @JSONField(name = "event_type")
    private Integer eventType;

    @JSONField(name = "event_timestamp")
    private Long eventTimestamp;

    @JSONField(name = "send_timestamp")
    private Long sendTimestamp;

    @JSONField(name = "vin")
    private String vin;

    @JSONField(name = "vehicle_name")
    private String vehicleName;

    @JSONField(name = "vehicle_id")
    private String vehicleId;

    @JSONField(name = "content")
    private EventContentDTO eventContentDTO;
}

