package com.sankuai.walleops.cloud.triage.pojo.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class VideoHistoryStatusRequest {

    @NotBlank(message = "车架号不能为空")
    private String vin; //车架号

    @NotNull(message = "开始时间不能为空")
    private String startTime; //开始时间

    @NotNull(message = "结束时间不能为空")
    private String endTime; //结束时间
}
