package com.sankuai.walleops.cloud.triage.thrift.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "碰撞检测事件详情查询接口响应")
public class CollisionDetectionEventsQueryResponse {

    @ThriftField(1)
    @FieldDoc(description = "异常事件数量")
    private Integer number;

    @ThriftField(2)
    @FieldDoc(description = "响应消息")
    private List<CollisionDetectionEventDetailVO> detailVOS;

}
