package com.sankuai.walleops.cloud.triage.component;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.AbstractMdpListener;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.CloudTriageEventMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.walleops.cloud.triage.adaptor.eve.EVEAdaptor;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class CloudTriageEventCommonConsumer {

    @Resource
    private InformationApiService informationApiService;

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private EVEAdaptor eveAdaptor;

    @ConfigValue(key = CommonConstant.LION_KEY_EVENT_VEHICLE_FILTER_CONFIG, defaultValue = "{}")
    private Map<String, Set<String>> eventVehicleFilterConfig;

    @MdpMafkaMsgReceive
    public ConsumeStatus consume(String message, AbstractMdpListener.MdpMqContext context) {
        log.info("CloudTriageEventCommonConsumer# consume# message = {}", message);
        try {
            // 1 解析消息
            EveMqCommonMessage<CloudTriageEventMessageDTO> commonMessageDTO = JacksonUtils.from(message,
                    new TypeReference<EveMqCommonMessage<CloudTriageEventMessageDTO>>() {
                    });

            if (Objects.isNull(commonMessageDTO) || Objects.isNull(commonMessageDTO.getBody())) {
                log.error("CloudTriageEventCommonConsumer# consume# message parse error, message = {}", message);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 2 消息体校验
            CloudTriageEventMessageDTO messageDTO = commonMessageDTO.getBody();
            InputCheckUtil.isNotBlank(messageDTO.getEventId(), "事件ID不能为空");
            InputCheckUtil.isNotBlank(messageDTO.getVin(), "车架号不能为空");
            InputCheckUtil.isNotNull(messageDTO.getEventType(), "事件类型不能为空");
            InputCheckUtil.isNotNull(messageDTO.getEventTime(), "事件时间不能为空");

            // 2.1 根据事件类型进行过滤
            Integer eventType = messageDTO.getEventType();
            if (shouldFilterEvent(eventType, messageDTO)) {
                log.info("CloudTriageEventCommonConsumer# consume# eventType = {}, message = {}", eventType, message);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 3 根据 status 字段进行分流，如果为 null 或者 为 0 则表示创建， 否则进行更新
            if (Objects.isNull(messageDTO.getStatus()) || Objects.equals(messageDTO.getStatus(),
                    EventStatusEnum.INITED.getCode())) {
                // 存储事件
                BizCloudTriageEvent event = buildCloudEvent(messageDTO);
                cloudTriageEventService.save(event);
            } else {
                // 更新事件
                log.info("CloudTriageEventCommonConsumer# consume# update event, message = {}", message);
                CloudTriageEventUpdateRequest updateRequest = new CloudTriageEventUpdateRequest();
                updateRequest.setEventId(messageDTO.getEventId());
                updateRequest.setStatus(messageDTO.getStatus());
                cloudTriageEventService.statusCheckAndUpdate(updateRequest);
            }
        } catch (Exception e) {
            log.error("CloudTriageEventCommonConsumer# consume# error, message = {}", message, e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 构建分诊异常事件
     *
     * @param messageDTO
     * @return
     */
    private BizCloudTriageEvent buildCloudEvent(CloudTriageEventMessageDTO messageDTO) {
        BizCloudTriageEvent event = new BizCloudTriageEvent();
        event.setEventTime(messageDTO.getEventTime());
        event.setEventId(messageDTO.getEventId());
        event.setStatus(EventStatusEnum.INITED.getCode());
        event.setReporter(messageDTO.getReporter());
        event.setEventType(messageDTO.getEventType());
        event.setVin(messageDTO.getVin());
        event.setRemark(messageDTO.getExtInfo());

        // 查询车辆实时状态
        VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = informationApiService.queryVehicleStatusByVin(
                messageDTO.getVin());

        // 获取用车目的和场地信息
        if (Objects.nonNull(vehicleRealtimeStatusDTO)) {
            event.setPurpose(vehicleRealtimeStatusDTO.getPurpose());
            event.setPlace(vehicleRealtimeStatusDTO.getPark());
            event.setVehicleId(vehicleRealtimeStatusDTO.getVehicleId());
        }
        log.info("buildCloudEvent, event: {}", event);
        return event;
    }

    /**
     * 判断事件是否需要被过滤
     *
     * @param eventType 事件类型
     * @param eventDTO  事件数据
     * @return true-需要过滤, false-不需要过滤
     */
    private boolean shouldFilterEvent(Integer eventType, CloudTriageEventMessageDTO eventDTO) {
        try {
            // 检查配置是否为空
            if (Objects.isNull(eventVehicleFilterConfig)) {
                return false;
            }

            // 将eventType转换为字符串作为key
            String eventTypeKey = String.valueOf(eventType);

            // 检查是否有对应eventType的配置
            if (!eventVehicleFilterConfig.containsKey(eventTypeKey)) {
                return false;
            }

            // 获取需要过滤的车辆列表
            Set<String> filterVehicleList = eventVehicleFilterConfig.get(eventTypeKey);
            if (CollectionUtils.isEmpty(filterVehicleList)) {
                return false;
            }

            // 获取车辆信息进行匹配
            String firstClassModel = null;
            // 调用远程服务获取车辆信息
            VehicleStatusDTO vehicleInfo = eveAdaptor.getVehicleStatusFromDataBusByVin(eventDTO.getVin());
            if (vehicleInfo != null && vehicleInfo.getVehicleManage() != null) {
                firstClassModel = vehicleInfo.getVehicleManage().getFirstClassModel();
            }

            // 检查一级车型是否在过滤列表中
            if (firstClassModel != null && filterVehicleList.contains(firstClassModel)) {
                log.info("车辆一级车型匹配过滤规则: firstClassModel={}, eventType={}",
                        firstClassModel, eventType);
                return true; // 匹配到过滤规则，返回true表示需要过滤
            }
            return false;
        } catch (Exception e) {
            log.error("shouldFilterEvent执行异常: eventType={}, eventDTO={}", eventType, eventDTO, e);
            return false;
        }
    }


}
