package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleHistoryStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.request.VideoHistoryStatusRequest;

import java.util.List;

public interface VehicleStatusService {

    /**
     * 从信息中心查询车的历史状态信息
     * @param request 通过vin、startTime、endTime等参数查询
     * @return VehicleStatusDTO集合
     * */
    List<VehicleHistoryStatusDTO> queryVehicleHistoryStatus(VideoHistoryStatusRequest request);
}
