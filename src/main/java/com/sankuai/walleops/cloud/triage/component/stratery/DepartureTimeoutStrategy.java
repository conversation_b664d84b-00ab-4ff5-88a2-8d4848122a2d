package com.sankuai.walleops.cloud.triage.component.stratery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.DepartureTimeoutEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/25
 */
@Component("departure_timeout")
@Slf4j
public class DepartureTimeoutStrategy extends EventStrategy{
    @Override
    public BizCloudTriageEvent transferEvent(InformationDTO information, EventTypeEnum eventTypeEnum) {
        DepartureTimeoutEventDTO eventDTO;
        try {
            eventDTO = JSON.parseObject(information.getData(), DepartureTimeoutEventDTO.class);
        } catch (Exception e) {
            log.error("information data parse error: {}", information.getData(), e);
            return null;
        }

        if (timeoutRecover(information.getInformationId(), eventDTO.getStatus())) {
            return null;
        }
        BizCloudTriageEvent cloudTriageEvent = generateDepartureExceptionEvent(eventDTO,
                eventTypeEnum, new Date(information.getInformationTimestamp()));

        Date departureTime = new Date(eventDTO.getDepartureTimestamp());
        String departureTimeStr = DateUtil.format(departureTime, DateUtil.YMD_HMS_SSS_UNSIGNED);

        JSONObject remarkJson = new JSONObject();
        remarkJson.put("departureTime", departureTimeStr);

        String content = eventDTO.getContent();
        if(StringUtils.isNotBlank(content)) {
            JSONObject contentJson = JSON.parseObject(content);

            String tripNodeId = contentJson.getString("trip_node_id");
            remarkJson.put("trip_node_id", StringUtils.isBlank(tripNodeId) ? "" : tripNodeId);

            String tripNodeName = contentJson.getString("trip_node_name");
            remarkJson.put("trip_node_name", StringUtils.isBlank(tripNodeName) ? "" : tripNodeName);

            String nextTripNodeId = contentJson.getString("next_trip_node_id");
            remarkJson.put("next_trip_node_id", StringUtils.isBlank(nextTripNodeId) ? "" : nextTripNodeId);

            String nextTripNodeName = contentJson.getString("next_trip_node_name");
            remarkJson.put("next_trip_node_name", StringUtils.isBlank(nextTripNodeName) ? "" : nextTripNodeName);

            String datasource = contentJson.getString("datasource");
            remarkJson.put("datasource", StringUtils.isBlank(datasource) ? "" : datasource);
        }

        cloudTriageEvent.setRemark(remarkJson.toJSONString());

        return cloudTriageEvent;
    }
}
