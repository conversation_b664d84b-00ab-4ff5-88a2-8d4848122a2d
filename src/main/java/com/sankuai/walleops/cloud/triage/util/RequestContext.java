package com.sankuai.walleops.cloud.triage.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/8/29
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RequestContext {
    private static final ThreadLocal<String> USER_IP = new ThreadLocal<>();

    public static void setIp(String ip) {
        USER_IP.set(ip);
    }
    public static String getIp() {
        return USER_IP.get();
    }
    public static void removeIp() {
        USER_IP.remove();
    }

}
