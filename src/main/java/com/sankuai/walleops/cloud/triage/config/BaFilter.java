package com.sankuai.walleops.cloud.triage.config;

import com.google.common.net.HttpHeaders;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2021/08/26
 */
@Slf4j
@Component
@WebFilter
@Order(value = 3)
public class BaFilter implements Filter {

    @Value("${environment}")
    private String environment;

    private static final String SSO_CLIENT_ID = "sso.client.id";

    private static final String SSO_SECRET = "sso.client.secret";

    private static final String BA_SUFFIX = "/ba";

    private static String clientId = null;

    private static String clientSecret = null;


    static {
        try {
            clientId = Kms.getByName(CommonConstant.APP_KEY, SSO_CLIENT_ID);
            clientSecret = Kms.getByName(CommonConstant.APP_KEY, SSO_SECRET);
        } catch (KmsResultNullException e) {
            log.error("获取ba认证kms的secret失败: ", e);
        }
    }

    @Override
    public void init(FilterConfig filterConfig) {
        // no need to init, do nothing
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {

        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpServletResponse = ((HttpServletResponse) servletResponse);
        String url = httpServletRequest.getRequestURL().toString();
        String method = httpServletRequest.getMethod();
        String uri = httpServletRequest.getRequestURI();

        if (!uri.endsWith(BA_SUFFIX)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        boolean isAuth = checkBasicAuth(url, method, httpServletRequest, clientId, clientSecret);
        if (!isAuth) {
            httpServletResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return;
        }
        String tmpUri = uri.replace(BA_SUFFIX, "");
        RequestDispatcher requestDispatcher = servletRequest.getRequestDispatcher(tmpUri);
        if (requestDispatcher == null) {
            return;
        }
        requestDispatcher.forward(servletRequest, servletResponse);
    }

    public boolean checkBasicAuth(String url,
                                  String method,
                                  HttpServletRequest httpServletRequest,
                                  String clientId,
                                  String clientSecret) {
        String originDate = httpServletRequest.getHeader(HttpHeaders.DATE);
        Map<String, String> headers = buildHeaders(url, method, originDate, clientId, clientSecret);
        String authorization = headers.getOrDefault("Authorization", "");
        String originAuthorization = httpServletRequest.getHeader(HttpHeaders.AUTHORIZATION);
        log.info("authorization: {}, originAuthorization: {}", authorization, originAuthorization);
        return authorization.equals(originAuthorization);
    }

    public Map<String, String> buildHeaders(String url, String method,
                                            String date, String clientId, String clientSecret) {
        Map<String, String> headers = new HashMap<>();
        try {
            String uri = new URI(url).getPath();
            log.info("url is {}, method is {}, date is {}, clientId is {}, clientSecret is {}, uri is {}",
                    url, method, date, clientId, clientSecret, uri);
            String authorizationHeader = CommonUtil.getAuthorization(uri, method, date, clientId, clientSecret);
            headers.put("Date", date);
            headers.put("Authorization", authorizationHeader);
        } catch (Exception e) {
            log.error("Build headers failed, url is {}", url, e);
        }
        return headers;
    }

    @Override
    public void destroy() {
        // no need of this method, do nothing
    }
}
