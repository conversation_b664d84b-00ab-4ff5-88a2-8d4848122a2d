package com.sankuai.walleops.cloud.triage.config;

import com.meituan.mtrace.http.TraceMethodInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * <AUTHOR>
 * @date 2021/08/26
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        TraceMethodInterceptor traceHandlerInterceptor = new TraceMethodInterceptor();
        registry.addInterceptor(traceHandlerInterceptor).addPathPatterns("/**");
        registry.addInterceptor(new HttpInterceptor());
    }

}
