package com.sankuai.walleops.cloud.triage.pojo.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CloudOperationRequest {
    /**
     * 车架号
     */
    String vin;

    /**
     * 呼叫云控原因
     */
    Integer reason;

    /**
     * 呼叫来源
     */
    Integer source;

    /**
     * 请求时间戳（毫秒）
     */
    Long timestamp;

    /**
     * 请求动作
     */
    String action;

    /**
     * 是否需要一直呼叫
     */
    @Builder.Default
    private Boolean needCancelCommand = false;
}
