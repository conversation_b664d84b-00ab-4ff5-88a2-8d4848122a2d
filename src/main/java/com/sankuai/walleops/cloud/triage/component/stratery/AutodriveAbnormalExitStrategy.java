package com.sankuai.walleops.cloud.triage.component.stratery;

import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component("autodrive_abnormal_exit")
@Slf4j
public class AutodriveAbnormalExitStrategy extends EventStrategy {
    @Override
    public BizCloudTriageEvent transferEvent(InformationDTO information, EventTypeEnum eventTypeEnum) {
        return super.transferEvent(information, eventTypeEnum);
    }
}
