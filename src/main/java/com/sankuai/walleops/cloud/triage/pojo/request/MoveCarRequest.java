package com.sankuai.walleops.cloud.triage.pojo.request;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class MoveCarRequest {

    /**
     * 车牌号
     */
    private String vehicleId;

    /**
     * 异常事件类型
     */
    private Integer eventType;

    /**
     * 车辆位置
     */
     private String carPosition;

    /**
     * 车辆经纬度信息
     */
    private String carPositionGps;

    /**
     * 挪车原因
     */
    private String moveCarReason;

    /**
     * 上报人手机号
     */
    private String phoneNumber;

    /**
     * 现场照片url
     */
    private List<String> urlList;
}
