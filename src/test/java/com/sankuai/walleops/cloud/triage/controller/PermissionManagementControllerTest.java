package com.sankuai.walleops.cloud.triage.controller;

import com.alibaba.fastjson.JSONObject;
import com.meituan.servicecatalog.api.annotations.MediaType;
import com.sankuai.walleops.cloud.triage.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
@Slf4j
@Ignore
public class PermissionManagementControllerTest {

    @Autowired
    protected MockMvc mockMvc;

    @Test
    void queryPermissionManage() throws Exception {
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post("/permission/query")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }

    @Test
    void operatePermissionManage() throws Exception {
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post("/permission/operate")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        log.info("Mock结果是:{}", result.getResponse().toString());
    }
}
