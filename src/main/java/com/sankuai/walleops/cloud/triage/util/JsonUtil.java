package com.sankuai.walleops.cloud.triage.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class JsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将对象序列化为JSON字符串
     * @param obj 要序列化的对象
     * @return JSON字符串
     */
    public static String toJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("序列化对象到JSON字符串时发生错误", e);
        }
    }

    /**
     * 将JSON字符串反序列化为对象
     * @param json JSON字符串
     * @param clazz 目标对象的类类型
     * @param <T> 目标对象的类型
     * @return 反序列化的对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("从JSON字符串反序列化对象时发生错误", e);
        }
    }

    /**
     * 将JSON字符串反序列化为对象
     * @param jsonData JSON字符串
     * @param clazz 目标对象的类型引用
     * @param <T> 目标对象的类型
     * @return 反序列化的对象
     */

    public static <T> T fromJson(String jsonData, TypeReference<T> clazz) {
        try {
            if (StringUtils.isBlank(jsonData)) {
                return null;
            }
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return objectMapper.readValue(jsonData, clazz);
        } catch (Exception e) {
            throw new RuntimeException("解析JSON数据时发生错误", e);
        }
    }

}