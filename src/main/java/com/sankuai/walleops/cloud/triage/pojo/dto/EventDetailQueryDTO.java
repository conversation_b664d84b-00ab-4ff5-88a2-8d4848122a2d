package com.sankuai.walleops.cloud.triage.pojo.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EventDetailQueryDTO {
    /**
     * 车架号
     */
    private String vin;

    /**
     * 异常事件列表
     */
    private List<Integer> eventTypeList;

    /**
     * 状态列表
     */
    private List<Integer> statusList;

    /**
     * 异常发生时间
     */
    private String eventTime;

    /**
     * 查询开始时间
     */
    private String startTime;

    /**
     * 查询结束时间
     */
    private String endTime;
    
    /**
     * 处置方
     */
    private Integer operatorType;
}
