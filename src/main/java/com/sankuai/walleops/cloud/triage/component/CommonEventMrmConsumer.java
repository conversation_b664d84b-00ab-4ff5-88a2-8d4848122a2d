package com.sankuai.walleops.cloud.triage.component;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.AbstractMdpListener;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.MrmCalledStatusEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.message.CockpitOperationMessageDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CommonEventMrmConsumer {

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    // 参考文档： https://km.sankuai.com/collabpage/2353988893
    @MdpMafkaMsgReceive
    public ConsumeStatus consume(String message, AbstractMdpListener.MdpMqContext context) {
        log.info("CommonEventMrmConsumer,consume# message = {}", message);
        try {
            // 1 解析消息
            EveMqCommonMessage<CockpitOperationMessageDTO> commonMessageDTO = JacksonUtils.from(message,
                    new TypeReference<EveMqCommonMessage<CockpitOperationMessageDTO>>() {
                    });

            if (Objects.isNull(commonMessageDTO) || Objects.isNull(commonMessageDTO.getBody())) {
                log.error("CommonEventMrmConsumer# consume# message parse error, message = {}", message);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 2 消息体校验
            CockpitOperationMessageDTO messageDTO = commonMessageDTO.getBody();
            InputCheckUtil.isNotNull(messageDTO.getEventType(), "事件类型不能为空");
            InputCheckUtil.isNotBlank(messageDTO.getVin(), "车架号不能为空");

            // 3、 拆分消息类型
            switch (messageDTO.getEventType()) {
                // 坐席分配成功
                case 3:
                    // 主动连入
                case 8:
                    List<BizCloudTriageEvent> unHandleEventList = getAbnormalEventList(messageDTO.getVin(),
                            EventStatusEnum.INITED);
                    if (CollectionUtils.isEmpty(unHandleEventList)) {
                        log.info("CommonEventMrmConsumer# consume# unHandleEventList is empty, message = {}", message);
                        break;
                    }
                    // 更新前或者更新过程中记录下本次操作
                    unHandleEventList.forEach(event -> {
                        updateEventStatus(event, EventStatusEnum.HANDLING);
                    });
                    break;
                case 6: // 坐席退控
                    List<BizCloudTriageEvent> inHandleEventList = getAbnormalEventList(messageDTO.getVin(),
                            EventStatusEnum.HANDLING);
                    if (CollectionUtils.isEmpty(inHandleEventList)) {
                        log.info("CommonEventMrmConsumer# consume# inHandleEventList is empty, message = {}", message);
                        break;
                    }
                    inHandleEventList.forEach(event -> {
                        updateEventStatus(event, EventStatusEnum.COMPLETED);
                    });
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("CloudTriageEventCommonConsumer# consume# error, message = {}", message, e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }


    /**
     * 获取指定车辆 + 指定状态 + 指定类型的事件列表
     *
     * @param vin
     * @param statusEnum
     * @return
     */
    private List<BizCloudTriageEvent> getAbnormalEventList(String vin, EventStatusEnum statusEnum) {
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        // 已发起呼叫
        request.setMrmCalled(MrmCalledStatusEnum.CALLING.getCode());
        request.setStatus(statusEnum.getCode());
        // 指定异常事件类型 - 碰撞检测不依赖这个状态
        request.setEventType(Arrays.asList(EventTypeEnum.TRAFFICCONGESTION_ALERT.getCode()));
        // operator_type = 2 (云辅助处置)
        request.setOperatorType(EventOperatorTypeEnum.COCKPIT.getCode());
        // 设置指定车辆
        request.setVinList(Arrays.asList(vin));
        // 设置查询时间
        // fixme: 修改时间支持配置
        Date startTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, 30);
        request.setStartTime(DatetimeUtil.formatTime(startTime));
        request.setEndTime(DatetimeUtil.formatTime(new Date()));
        return cloudTriageEventService.commonQuery(request);
    }

    /**
     * 更新事件状态
     *
     * @param event
     * @param statusEnum
     */
    private void updateEventStatus(BizCloudTriageEvent event, EventStatusEnum statusEnum) {
        CloudTriageEventUpdateRequest cloudTriageEventUpdateRequest = new CloudTriageEventUpdateRequest();
        cloudTriageEventUpdateRequest.setEventId(event.getEventId());
        cloudTriageEventUpdateRequest.setStatus(statusEnum.getCode());
        cloudTriageEventUpdateRequest.setOperator("system");
        cloudTriageEventService.update(cloudTriageEventUpdateRequest);
    }

}
