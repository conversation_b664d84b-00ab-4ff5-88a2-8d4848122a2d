package com.sankuai.walleops.cloud.triage.pojo.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 车辆状态DTO
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleStatusDTO {

    /**
     * 车架号
     */
    private String vin;

    /**
     * 事故工单状态
     */
    @JsonAlias("accident_order")
    private AccidentOrder accidentOrder;

    /**
     * 维保工单状态
     */
    @JsonAlias("maintenance_order")
    private MaintenanceOrder maintenanceOrder;

    /**
     * 救援工单状态
     */
    @JsonAlias("rescue_order")
    private RescueOrder rescueOrder;

    /**
     * 状态监控加工数据
     */
    @JsonAlias("monitor_compute")
    private MonitorCompute monitorCompute;

    /**
     * 约车数据
     */
    @JsonAlias("reserve_vehicle")
    private ReserveVehicle reserveVehicle;

    /**
     * 标签数据
     */
    @JsonAlias("label")
    private Label label;

    /**
     * 车辆基础信息
     */
    @JsonAlias("vehicle_manage")
    private VehicleManage vehicleManage;

    /**
     * 车辆基础信息
     */
    @Data
    public static class VehicleManage {

        /**
         * 车辆名称
         */
        @JsonAlias("vehicle_name")
        private String vehicleName;

        /**
         * 一级车型
         */
        @JsonAlias("first_class_model")
        private String firstClassModel;

        /**
         * 二级车型
         */
        @JsonAlias("second_class_model")
        private String secondClassModel;

    }




    /**
     * 获取云控VHR值
     *
     * @return
     */
    public String getTelecontrolVHRValue() {
        return Optional.ofNullable(label)
                .map(Label::getTags)
                .map(Label.Tags::getTelecontrolVHR)
                .map(Label.Tags.VhrInfo::getValue)
                .orElse("");
    }

    /**
     * 获取近场VHR值
     *
     * @return
     */
    public String getNearbyRescueVHRValue() {
        return Optional.ofNullable(label)
                .map(Label::getTags)
                .map(Label.Tags::getNearbyRescueVHR)
                .map(Label.Tags.VhrInfo::getValue)
                .orElse("");
    }


    @Data
    public static class AccidentOrder {

        /**
         * 是否存在工单
         */
        @JsonAlias("has_order")
        Boolean hasOrder;
    }

    @Data
    public static class MaintenanceOrder {

        /**
         * 是否存在工单
         */
        @JsonAlias("has_order")
        Boolean hasOrder;
    }

    @Data
    public static class RescueOrder {

        /**
         * 是否存在工单
         */
        @JsonAlias("has_order")
        Boolean hasOrder;
    }

    @Data
    public static class MonitorCompute {

        /**
         * 车辆在线状态
         */
        @JsonAlias("online_status")
        Integer onlineStatus;

        /**
         * 车辆在线状态描述
         */
        @JsonAlias("online_desc")
        List<String> onlineStatusDescList;

        /**
         * 云控连接人员mis号
         */
        @JsonAlias("remote_control_mis")
        String remoteControlMis;

        /**
         * 是否有云控连车
         */
        @JsonAlias("remote_control_status")
        Boolean isRemoteControlConnected;

        /**
         * 更新时间
         */
        @JsonAlias("update_time")
        Long updateTime;
    }


    @Data
    public static class ReserveVehicle {

        /**
         * 云控安全员
         */
        @JsonAlias("vresv_list")
        private List<Vresv> vresvList;
    }

    @Data
    public static class Vresv {

        /**
         * 约车Id
         */
        @JsonAlias("resv_id")
        private String resvId;

        /**
         * 约车开始时间
         */
        @JsonAlias("start_time")
        private Long startTime;

        /**
         * 约车结束时间
         */
        @JsonAlias("end_time")
        private Long endTime;

        /**
         * 云控安全员
         */
        @JsonAlias("telecontrol")
        private String telecontrol;

        /**
         * 是否审核通过
         */
        @JsonAlias("approved")
        Boolean approved;

        /**
         * 是否删除
         */
        @JsonAlias("deleted")
        Boolean deleted;
    }

    /**
     * 标签数据
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Label {

        /**
         * 更新时间
         */
        @JsonProperty("update_time")
        private Long updateTime;

        /**
         * 标签
         */
        private Tags tags;

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Tags {

            /**
             * 云控VHR
             */
            @JsonProperty("telecontrolVHR")
            private VhrInfo telecontrolVHR;
            /**
             * 近场VHR
             */
            @JsonProperty("nearbyRescueVHR")
            private VhrInfo nearbyRescueVHR;

            @Data
            @Builder
            @AllArgsConstructor
            @NoArgsConstructor
            public static class VhrInfo {

                /**
                 * appkey列表
                 */
                @JsonProperty("appkeys")
                private List<String> appkeys;

                /**
                 * 取值
                 */
                private String value;

            }
        }
    }

    /**
     * 检查是否存在事故工单
     *
     * @return 存在事故工单为true, else 返回false
     */
    public Boolean hasAccidentOrder() {
        if (this.accidentOrder != null && this.accidentOrder.getHasOrder() != null) {
            return this.accidentOrder.getHasOrder();
        }
        return false;
    }

    /**
     * 检查是否存在救援工单
     * @return 存在救援工单为true, else 返回false
     */
    public Boolean hasRescueOrder() {
        if (this.rescueOrder != null && this.rescueOrder.getHasOrder() != null) {
            return this.rescueOrder.getHasOrder();
        }
        return false;
    }

    /**
     * 检查是否存在维修工单
     * @return 存在维修工单为true, else 返回false
     */
    public Boolean hasMaintenanceOrder() {
        if (this.maintenanceOrder != null && this.maintenanceOrder.getHasOrder() != null) {
            return this.maintenanceOrder.getHasOrder();
        }
        return false;
    }

    /**
     * 检查车辆是否在线
     * @return 车辆在线返回true, else 返回 false(包含离线和未知)
     */
    public Boolean isVehicleOnline(){
        if(this.monitorCompute != null && this.monitorCompute.getOnlineStatus() != null
                && this.monitorCompute.getOnlineStatus().equals(CommonConstant.VEHICLE_ONLINE_STATUS)){
            return true;
        }
        return false;
    }

    /**
     * 获取车辆在线/离线状态
     * @return 返回车辆在线/离线状态，否则返回未知
     */
    public String getVehicleStatus(){
        if(this.monitorCompute != null && !CollectionUtils.isEmpty(this.monitorCompute.getOnlineStatusDescList())
                && this.monitorCompute.getOnlineStatus() != null){
            return this.monitorCompute.getOnlineStatusDescList().get(this.monitorCompute.getOnlineStatus());
        }
        return CommonConstant.UNKNOWN;
    }

    /**
     * 获取车辆连接云控状态
     * @return 返回车辆连接云控状态，否则返回 false
     */
    public Boolean hasRemoteControlConnected(){
        if(this.monitorCompute != null && this.monitorCompute.getIsRemoteControlConnected() != null){
            return this.monitorCompute.getIsRemoteControlConnected();
        }
        return false;
    }

    /**
     * 云控连接人员mis号
     * @return 返回云控连接人员mis号，否则返回 未知
     */
    public String getRemoteControlMis(){
        if(this.monitorCompute != null && !StringUtils.isEmpty(this.monitorCompute.getRemoteControlMis())){
            return this.monitorCompute.getRemoteControlMis();
        }
        return CommonConstant.UNKNOWN;
    }

    /**
     * 获取云控安全员mis信息
     *
     * @return 返回云控安全员mis号，否则返回 未知
     */
    public String getTelecontrol(Date targetDate) {
        // 检查预约车辆信息是否存在且预约列表不为空
        if (this.reserveVehicle != null && !StringUtils.isEmpty(this.reserveVehicle.getVresvList())) {
            // 筛选出符合条件的预约信息：已审批、未删除、时间范围内的预约
            List<Vresv> filteredVresvs = this.reserveVehicle.getVresvList().stream()
                    .filter(vresv -> vresv.getApproved() != null && vresv.getApproved()) // 已经审批通过的
                    .filter(vresv -> vresv.getDeleted() != null && !vresv.getDeleted()) // 未删除的
                    .filter(vresv -> vresv.getStartTime() != null && vresv.getEndTime() != null) // 确保开始时间和结束时间不为空
                    .filter(vresv -> targetDate.getTime() >= vresv.getStartTime()
                            && targetDate.getTime() <= vresv.getEndTime()) // 目标日期在开始时间和结束时间内
                    .collect(Collectors.toList());
            // 如果筛选后的列表不为空，则返回第一个预约的云控安全员信息
            if (!CollectionUtils.isEmpty(filteredVresvs)) {
                return filteredVresvs.get(0).getTelecontrol();
            }
        }
        // 如果没有符合条件的预约信息，则返回未知
        return CommonConstant.UNKNOWN;
    }

}
