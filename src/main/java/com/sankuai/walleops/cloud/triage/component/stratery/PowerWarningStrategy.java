package com.sankuai.walleops.cloud.triage.component.stratery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.PowerWarningEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component("power_warning")
public class PowerWarningStrategy extends EventStrategy {

    @Override
    public BizCloudTriageEvent transferEvent(InformationDTO information, EventTypeEnum eventTypeEnum) {
        PowerWarningEventDTO eventDTO;
        try {
            eventDTO = JSON.parseObject(information.getData(), PowerWarningEventDTO.class);
        } catch (Exception e) {
            log.error("information data parse error: {}", information.getData(), e);
            return null;
        }

        Date eventTime = new Date(eventDTO.getEventTimestamp());
        String eventId = CommonUtil.generateEventId(eventTime, eventTypeEnum.getEventName(),
                eventDTO.getVehicleName());

        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
        cloudTriageEvent.setEventId(eventId);
        cloudTriageEvent.setEventTime(eventTime);
        cloudTriageEvent.setVehicleId(eventDTO.getVehicleId());
        cloudTriageEvent.setEventType(eventTypeEnum.getCode());
        cloudTriageEvent.setVin(eventDTO.getVin());
        cloudTriageEvent.setLongitude(eventDTO.getLongitude() == null ? "" : eventDTO.getLongitude().toString());
        cloudTriageEvent.setLatitude(eventDTO.getLatitude() == null ? "" : eventDTO.getLatitude().toString());

        JSONObject remarkJson = new JSONObject();
        remarkJson.put("vehicle_name", eventDTO.getVehicleName() == null ? "" : eventDTO.getVehicleName());

        String content = eventDTO.getContent();
        if(StringUtils.isNotBlank(content)) {
            JSONObject contentJson = JSON.parseObject(content);

            if(contentJson.containsKey("issue_list")) {
                JSONArray jsonArray = contentJson.getJSONArray("issue_list");
                if(jsonArray.size() > 0) {
                    JSONObject issueObjectJson = jsonArray.getJSONObject(0);
                    String powerWarnDesc = issueObjectJson.getString("text")
                                                            .replace("【", "")
                                                            .replace("】", "");
                    remarkJson.put("power_warn_desc", powerWarnDesc);
                }
            }
        }
        cloudTriageEvent.setRemark(remarkJson.toJSONString());

        return cloudTriageEvent;
    }
}
