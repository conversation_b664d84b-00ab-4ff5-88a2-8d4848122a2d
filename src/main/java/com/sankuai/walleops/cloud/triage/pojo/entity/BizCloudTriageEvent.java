package com.sankuai.walleops.cloud.triage.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import javax.persistence.Table;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/24
 */
@Data
@Table(name = "biz_cloud_triage_event")
public class BizCloudTriageEvent {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 事件id
     */
    private String eventId;

    /**
     * 信息id
     */
    private String informationId;

    /**
     * record名称
     */
    private String recordName;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车辆id
     */
    private String vehicleId;

    /**
     * 事件发生时间
     */
    private Date eventTime;

    /**
     * 恢复时间
     */
    private Date recoveryTime;

    /**
     * 事件类型，-1表示未知，0表碰撞检测事件，1表示车辆停止不前，其他待补充
     */
    private Integer eventType;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 状态， 0表示未处理，1表示进行中，2表示完成，3表示取消
     */
    private Integer status;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 处理开始时间
     */
    private Date operateStartTime;

    /**
     * 处理结束时间
     */
    private Date operateEndTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用车目的
     */
    private String purpose;

    /**
     * 场地
     */
    private String place;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private Integer isDeleted;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_time
     */
    private Date updateTime;

    // 上报人
    private String reporter;

    /**
     * 操作类型 操作类型[0-人工处置｜1-系统自动呼叫云控处置]
     */
    private Integer operationType;

    /**
     * 处置方
     */
    private Integer operatorType;

    /**
     * 是否发起呼叫
     */
    private Integer mrmCalled;


}
