package com.sankuai.walleops.cloud.triage.service.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleops.cloud.triage.adaptor.BitrateChangeAdaptor;
import com.sankuai.walleops.cloud.triage.adaptor.CallCloudOperationServiceAdaptor;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.GrayConfigDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.GrayConfigDTO.GrayConfigItem;
import com.sankuai.walleops.cloud.triage.pojo.dto.VideoDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudOperationRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.CockpitHandleService;
import com.sankuai.walleops.cloud.triage.service.VideoService;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CockpitHandleServiceImpl implements CockpitHandleService {

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private CallCloudOperationServiceAdaptor cloudOperationServiceAdaptor;

    @Resource
    private BitrateChangeAdaptor bitrateChangeAdaptor;

    @Resource
    private VideoService videoService;

    @ConfigValue(key = "call.cockpit.config", defaultValue = "")
    private GrayConfigDTO grayConfigDTO;

    @MdpConfig("videoRequestDuration")
    private ArrayList<Integer> videoRequestDuration;

    /**
     * 查询未处理的碰撞检测事件列表
     *
     * @param vin
     * @return
     */
    @Override
    public List<BizCloudTriageEvent> queryUnCheckCollisionDetectionEventList(String vin) {
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        // 指定VIN
        request.setVinList(Collections.singletonList(vin));
        // 所有的碰撞检测事件类型
        request.setEventType(EventTypeEnum.getCollisionDetectionTypeList());
        // 未处理（未检核）
        List<Integer> inStatusList = new ArrayList<>();
        inStatusList.add(EventStatusEnum.INITED.getCode());
        inStatusList.add(EventStatusEnum.HANDLING.getCode());
        request.setInStatusList(inStatusList);
        // 查询云控处置
        request.setOperatorType(EventOperatorTypeEnum.COCKPIT.getCode());
        // 设置查询事件
        // fixme: 修改时间支持配置
        Date startTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, 30);
        request.setStartTime(DatetimeUtil.formatTime(startTime));
        request.setEndTime(DatetimeUtil.formatTime(new Date()));
        return cloudTriageEventService.commonQuery(request);
    }

    /**
     * 判断是否灰度
     *
     * @param eventType
     * @param purpose
     * @return
     */
    @Override
    public Boolean isInGray(Integer eventType, String purpose) {
        if (Objects.isNull(eventType) || Objects.isNull(grayConfigDTO)) {
            return false;
        }
        Map<String, GrayConfigItem> stringGrayConfigItemMap = grayConfigDTO.getConfigs();
        if (MapUtils.isEmpty(stringGrayConfigItemMap) || !stringGrayConfigItemMap.containsKey(
                String.valueOf(eventType))) {
            return false;
        }
        GrayConfigItem grayConfigItem = stringGrayConfigItemMap.get(String.valueOf(eventType));
        return grayConfigItem.isInGrayScope(purpose);
    }

    /**
     * 取消呼叫云控处置
     *
     * @param vin
     * @param eventType
     */
    @Override
    public void requestCloudOperation(String vin, Integer eventType, String action) throws Exception {
        if (StringUtils.isBlank(vin) || Objects.isNull(eventType) || Objects.isNull(grayConfigDTO)) {
            throw new IllegalArgumentException("参数错误");
        }
        Map<String, GrayConfigItem> stringGrayConfigItemMap = grayConfigDTO.getConfigs();
        if (MapUtils.isEmpty(stringGrayConfigItemMap) || !stringGrayConfigItemMap.containsKey(
                String.valueOf(eventType))) {
            throw new IllegalArgumentException("未找到对应的灰度配置");
        }
        GrayConfigItem grayConfigItem = stringGrayConfigItemMap.get(String.valueOf(eventType));
        CloudOperationRequest request = CloudOperationRequest.builder()
                .vin(vin)
                .reason(grayConfigItem.getReason())
                .source(CommonConstant.REQUEST_CLOUD_OPERATION_SOURCE)
                .timestamp(new Date().getTime())
                .action(action)
                .needCancelCommand(true).build();
        cloudOperationServiceAdaptor.requestCloudOperation(request);
    }

    /**
     * 处理碰撞检测事件
     *
     * @param cloudTriageEvent
     */
    @Override
    public void handleCollisionDetectionEvent(BizCloudTriageEvent cloudTriageEvent) {
        if (cloudTriageEvent == null) {
            return;
        }
        // 异常事件保存
        try {
            int result = cloudTriageEventService.save(cloudTriageEvent);
            if (result < 1) {
                log.info("database write failure! cloudTriageEvent = {}", cloudTriageEvent);
            }
            // 插入成功后，且为分配给坐席的，且初始化的状态，立即发起呼叫
            if (result > 0) {
                isCallCockpit(cloudTriageEvent);
            }
            //当数据插入成功后，开始触发高清视频打捞
            startHDVideoSalvage(cloudTriageEvent.getVin(), cloudTriageEvent.getEventTime());
        } catch (Exception e) {
            log.error("save cloudTriageEvent error: {}", cloudTriageEvent, e);
        }
    }


    /**
     * 判断是否需要呼叫中控台
     *
     * @param event
     */
    private void isCallCockpit(BizCloudTriageEvent event) {
        log.info("CollisionDetectionHandleService#isCallCockpit, event:{}", event);
        if (Objects.nonNull(event) &&
                (Objects.isNull(event.getStatus())
                        || Objects.equals(event.getStatus(), EventStatusEnum.INITED.getCode()))
        ) {
            try {
                // 更新呼叫字段，并发起呼叫
                ((CloudSecurityHandleServiceImpl) AopContext.currentProxy()).updateEventAndCallCockpit(event);
            } catch (Exception e) {
                log.error("updateEventAndCallCockpit error, event:{}", event, e);
            }
        }
    }

    /**
     * 触发高清视频打捞
     *
     * @param vin
     * @param eventTime
     */
    public void startHDVideoSalvage(String vin, Date eventTime) {
        // 1）触发视频码率切换， todo 历史逻辑
        bitrateChangeAdaptor.handleBitrateChangeEvent(vin, eventTime);
        // todo : 为什么不直接使用上文中的事故时间，因为该值与数据库值不统一，而前端是基于数据库中的值进行请求，因为这里读取数据库保持一致
        long accidentTime = DateUtil.roundAndConvertToSeconds(eventTime.getTime());
        // 1）请求前半段的高清环视视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(0), new Date().getTime() / 1000,
                Arrays.asList("v_loop"), true);

        //2）请求完整时间段的高清环视视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(0),
                accidentTime + videoRequestDuration.get(1), Arrays.asList("v_loop"), true);
        //3） 请求前半段的高清拼接视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(2), accidentTime, Arrays.asList("v_concat"),
                true);
        //4）请求完整时间段的高清拼接视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(2),
                accidentTime + videoRequestDuration.get(3), Arrays.asList("v_concat"), true);
    }


    /**
     * 请求高清视频
     *
     * @param vin
     * @param start_ts
     * @param end_ts
     * @param perspectives
     * @param immediateRetrieve
     */
    public void requestHDVideoByType(String vin, Long start_ts, Long end_ts, List<String> perspectives,
            Boolean immediateRetrieve) {
        VideoDTO videoDTO = VideoDTO.builder()
                .vin(vin)
                .start_ts(start_ts)
                .end_ts(end_ts)
                .position(perspectives)
                .immediateRetrieve(immediateRetrieve).build();
        videoService.getHDVideo(videoDTO);
    }


}
