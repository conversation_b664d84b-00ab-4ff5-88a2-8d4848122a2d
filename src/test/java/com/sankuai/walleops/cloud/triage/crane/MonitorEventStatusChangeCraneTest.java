package com.sankuai.walleops.cloud.triage.crane;

import com.sankuai.walleops.cloud.triage.BaseTest;
import javax.annotation.Resource;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 轮询云分诊事件状态crane测试类
 *
 * <AUTHOR>
 * @date 2024/05/14
 */
@Ignore
public class MonitorEventStatusChangeCraneTest extends BaseTest {

    @Resource
    MonitorEventStatusChangeCrane monitorEventStatusChangeCrane;

    @Resource
    MonitorTimeoutUnprocessedEventCrane monitorTimeoutUnprocessedEventCrane;

    @Test
    public void testMonitorEventStatusChange() {
        monitorEventStatusChangeCrane.monitorEventStatusChange();
    }

    @Test
    public void testMonitorTimeoutUnprocessedEvent(){
        monitorTimeoutUnprocessedEventCrane.monitorTimeoutUnprocessedEvent();
    }
}
