package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventGroupQueryDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventGroupResultDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.LastEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.thrift.vo.EventDetailVO;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2022/8/24
 */
public interface CloudTriageEventService {

    int save(BizCloudTriageEvent event);

    void statusCheckAndUpdate(CloudTriageEventUpdateRequest request);

    int update(CloudTriageEventUpdateRequest request);

    int updateOnly(BizCloudTriageEvent cloudTriageEvent);

    int upsertAccident(BizCloudTriageEvent cloudTriageEvent);

    List<BizCloudTriageEvent> commonQuery(CloudTriageEventQueryPageRequest request);

    List<BizCloudTriageEvent> pageQuery(CloudTriageEventQueryPageRequest request);

    List<BizCloudTriageEvent> pageQueryGroup(CloudTriageEventQueryPageRequest request);

    int count(CloudTriageEventQueryPageRequest request);

    int countGroup(CloudTriageEventQueryPageRequest request);

    Long getMaxId(CloudTriageEventQueryPageRequest request);

    Long getMaxIdGroup(CloudTriageEventQueryPageRequest request);

    List<EventGroupResultDTO> groupUnprocessedEvent(EventGroupQueryDTO request);

    List<BizCloudTriageEvent> queryEventTypeByIds(List<Long> ids);

    BizCloudTriageEvent queryAllByUniqueKey(@Param("eventId") String eventId, @Param("id") Long id);

    BizCloudTriageEvent queryByUniqueKey(String eventId, Long id);

    BizCloudTriageEvent queryByInformationId(String informationId);

    BizCloudTriageEvent queryEventDetailById(Long id);

    List<LastEventDTO> queryLastEvent(List<String> vinList, Long startTime, Long endTime, Integer eventType,
            Integer operatorType);

    EventDetailVO queryLatestCollisionDetectionEvent(String vin);

    BizCloudTriageEvent queryLatestEventDetailByUniqueKey(String eventId, Date minCreateTime);

    List<BizCloudTriageEvent> queryAllEventDetailByUniqueKey(String eventId, Date minCreateTime);

    List<BizCloudTriageEvent> queryByUpdateTimeRangeAndOperationType(Integer operationType, List<Integer> statusList,
            Date maxCreateTime, Date minCreateTime, Integer operatorType);

    List<BizCloudTriageEvent> queryByOperateStartTimeRangeAndOperationType(Integer operationType, Integer status,
            Date maxOperateStartTime, Date minOperateStartTime, Integer operatorType);

    Boolean updateBizCloudTriageEvent(BizCloudTriageEvent cloudTriageEvent);

    List<BizCloudTriageEvent> queryEventsByTimeVinStatus(String vin, List<Integer> eventTypeList,
            List<Integer> statusList, String startTime, String endTime, Integer operatorType);

    List<BizCloudTriageEvent> getUnprocessedAndUncompletedEvents(Date minCreateTime, Date maxCreateTime,
            Date minOperateStartTime, Date maxOperateStartTime);

    void createAndSendMQ(BizCloudTriageEvent bizCloudTriageEvent, EventStatusEnum statusEnum, String operator);

    String getCollisionDetectionAlert(String vin, Date eventTime);

    List<BizCloudTriageEvent> queryByTimeRange(Date startTime, Date endTime, List<Integer> eventTypeList,
            Integer operatorType);

    int queryThenUpdate(CloudTriageEventUpdateRequest request);
}
