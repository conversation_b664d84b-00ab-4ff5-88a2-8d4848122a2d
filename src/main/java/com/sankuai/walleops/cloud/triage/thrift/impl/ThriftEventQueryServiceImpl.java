package com.sankuai.walleops.cloud.triage.thrift.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.walledelivery.basic.client.response.deliverer.DelivererResponse;
import com.sankuai.walledelivery.thrift.response.ThriftResponse;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleops.cloud.triage.adaptor.DelivererQueryThriftServiceAdaptor;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.CollisionDetectionQueryConfigDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VideoDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.VideoReponse;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.CockpitHandleService;
import com.sankuai.walleops.cloud.triage.service.VideoService;
import com.sankuai.walleops.cloud.triage.service.impl.NearbyRescueHandleServiceImpl;
import com.sankuai.walleops.cloud.triage.thrift.ThriftEventQueryService;
import com.sankuai.walleops.cloud.triage.thrift.vo.CockpitCollisionDetectionDetailVO;
import com.sankuai.walleops.cloud.triage.thrift.vo.CollisionAlertQueryRequest;
import com.sankuai.walleops.cloud.triage.thrift.vo.CollisionDetectionEventDetailVO;
import com.sankuai.walleops.cloud.triage.thrift.vo.CollisionDetectionEventsQueryRequest;
import com.sankuai.walleops.cloud.triage.thrift.vo.CollisionDetectionEventsQueryResponse;
import com.sankuai.walleops.cloud.triage.thrift.vo.CollisionDetectionUpdateRequest;
import com.sankuai.walleops.cloud.triage.thrift.vo.EventDetailVO;
import com.sankuai.walleops.cloud.triage.thrift.vo.NearbyRescueCollisionDetectionDetailVO;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import com.sankuai.walleops.cloud.triage.util.exception.ParamInputErrorException;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@MdpThriftServer
public class ThriftEventQueryServiceImpl implements ThriftEventQueryService {

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private VideoService videoService;

    @Resource
    private DelivererQueryThriftServiceAdaptor delivererQueryThriftServiceAdaptor;

    @Resource
    private NearbyRescueHandleServiceImpl nearbyRescueHandleService;

    @Resource
    private CockpitHandleService cockpitHandleService;

    @MdpConfig("collision.detection.query.interface.config")
    CollisionDetectionQueryConfigDTO configDTO;

    /**
     * 获取碰撞检测告警信息
     *
     * @param request
     * @return
     */
    @Override
    public ThriftResponse<String> getCollisionDetectionAlert(CollisionAlertQueryRequest request) {
        log.info("getCollisionDetectionAlert, CollisionAlertQueryRequest = {}", request);

        try {
            InputCheckUtil.isNotBlank(request.getVin(), "车架号不能为空");
            InputCheckUtil.isNotNull(request.getEventTime(), "异常发生时间不可为空");

            String alert = cloudTriageEventService.getCollisionDetectionAlert(request.getVin(),
                    new Date(request.getEventTime()));
            log.info("getCollisionDetectionAlert, alert = {}", alert);
            if (Objects.isNull(alert)) {
                return ThriftResponse.errorWithMessage("未查询到符合条件的碰撞检测事件").build();
            }
            return ThriftResponse.ok(alert);
        } catch (ParamInputErrorException e) {
            return ThriftResponse.errorWithMessage(e.getMessage()).build();
        } catch (Exception e) {
            log.error("getCollisionDetectionAlert error", e);
            return ThriftResponse.errorWithMessage("system error").build();
        }
    }

    /**
     * 查询最近一次碰撞检测事件
     *
     * @param vin
     * @return
     */
    @Override
    public ThriftResponse<EventDetailVO> queryLatestCollisionDetectionEvent(String vin) {
        log.info("queryLatestCollisionDetectionEvent, vin = {}", vin);
        try {
            InputCheckUtil.isNotBlank(vin, "车架号不能为空");
            EventDetailVO eventDetailVO = cloudTriageEventService.queryLatestCollisionDetectionEvent(vin);
            log.info("queryLatestCollisionDetectionEvent, EventDetailVO = {}", JacksonUtils.to(eventDetailVO));
            return ThriftResponse.ok(eventDetailVO);
        } catch (ParamInputErrorException e) {
            return ThriftResponse.errorWithMessage(e.getMessage()).build();
        } catch (Exception e) {
            log.error("system error", e);
            return ThriftResponse.errorWithMessage("system error").build();
        }
    }

    /**
     * 根据时间范围查询碰撞检测事件列表 （云安全）
     *
     * @param request
     * @return
     */
    @Override
    public ThriftResponse<CollisionDetectionEventsQueryResponse> queryCollisionDetectionEventsByTimeRange(
            CollisionDetectionEventsQueryRequest request) {
        log.info("queryCollisionDetectionEventsByTimeRange, CollisionDetectionEventsQueryRequest = {}", request);
        try {
            InputCheckUtil.isNotNull(configDTO, "未配置碰撞检测查询接口");
            // 先根据时间查询事件列表
            InputCheckUtil.isNotNull(request.getStartTime(), "查询开始时间不可为空");
            InputCheckUtil.isNotNull(request.getEndTime(), "查询结束时间不可为空");

            CollisionDetectionEventsQueryResponse response = new CollisionDetectionEventsQueryResponse();

            // 指定异常事件类型
            List<Integer> eventTypeList = EventTypeEnum.getCollisionDetectionTypeList();
            List<BizCloudTriageEvent> allEvents = cloudTriageEventService.queryByTimeRange(
                    new Date(request.getStartTime()),
                    new Date(request.getEndTime()),
                    eventTypeList,
                    EventOperatorTypeEnum.CLOUD_SECURITY.getCode());
            log.info("queryCollisionDetectionEventsByTimeRange, eventList = {}", JacksonUtils.to(allEvents));
            if (CollectionUtils.isEmpty(allEvents)) {
                log.info("queryCollisionDetectionEventsByTimeRange, 未查询到事件列表");
                response.setNumber(0);
                response.setDetailVOS(Collections.emptyList());
                return ThriftResponse.ok().build();
            }
            List<String> vinList = allEvents.stream().map(BizCloudTriageEvent::getVin).collect(Collectors.toList());
            Map<String, String> vin2nameMap = batchQueryVinToName(vinList);
            // 设置响应中的事件数量
            AtomicReference<Integer> number = new AtomicReference<>(allEvents.size());
            // 设置分页
            int pageSize = Math.min(request.getPageSize() != null ? request.getPageSize() : configDTO.getMaxPageSize(),
                    configDTO.getMaxPageSize());
            int pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, allEvents.size());
            List<BizCloudTriageEvent> eventList = allEvents.subList(start, end);

            // 遍历事件列表取异常时间进行视频列表查询
            List<CollisionDetectionEventDetailVO> eventDetailVOList = Lists.newArrayList();
            eventList.stream().forEach(event -> {
                if (Objects.equals(event.getStatus(), EventStatusEnum.INITED.getCode())) {
                    number.getAndSet(number.get() - 1);
                    return;
                }
                // 判断碰撞检测事件是否取消,取消表示查否，否则表示查是
                int checkResult;
                if (Objects.equals(event.getStatus(), EventStatusEnum.CANCELED.getCode())) {
                    checkResult = Objects.equals(event.getOperator(), CommonConstant.SYSTEM) ? 0 : 1;
                } else {
                    checkResult = 2;
                }
                List<String> videoLinkList = Lists.newArrayList();

                // 获取环视视频
                VideoReponse loopVideoReq = getVideoLinkList(event, Lists.newArrayList("v_loop"),
                        configDTO.getIsRecycleLoopVideo());
                log.info("queryCollisionDetectionEventsByTimeRange, loopVideoReq = {}", JacksonUtils.to(loopVideoReq));
                //高清环视
                if (Objects.nonNull(loopVideoReq) && !CollectionUtils.isEmpty(loopVideoReq.getV_loop())) {
                    videoLinkList.addAll(loopVideoReq.getV_loop());
                }
                // 低清环视
                if (Objects.nonNull(loopVideoReq) && !CollectionUtils.isEmpty(loopVideoReq.getLoop())) {
                    videoLinkList.addAll(loopVideoReq.getLoop());
                }
                // 获取拼接视频
                VideoReponse concatVideoReq = getVideoLinkList(event, Lists.newArrayList("v_concat"),
                        true);
                log.info("queryCollisionDetectionEventsByTimeRange, concatVideoReq = {}",
                        JacksonUtils.to(concatVideoReq));
                if (Objects.nonNull(concatVideoReq) && !CollectionUtils.isEmpty(concatVideoReq.getV_concat())) {
                    videoLinkList.addAll(concatVideoReq.getV_concat());
                }
                eventDetailVOList.add(CollisionDetectionEventDetailVO.builder().eventId(event.getEventId())
                        .eventTime(DatetimeUtil.formatTime(event.getEventTime()))
                        .vehicleName(vin2nameMap.getOrDefault(event.getVin(), ""))
                        .vehicleId(event.getVehicleId())
                        .vin(event.getVin())
                        .checkResult(checkResult)
                        .videoLinkList(videoLinkList).build());
            });
            response.setNumber(number.get());
            response.setDetailVOS(eventDetailVOList);
            return ThriftResponse.ok(response);

        } catch (ParamInputErrorException e) {
            log.error("queryCollisionDetectionEventsByTimeRange error", e);
        }
        return ThriftResponse.fail().build();
    }

    /**
     * 查询实时碰撞检测事件详情 (近场)
     *
     * @param vin
     * @return
     */
    @Override
    public ThriftResponse<NearbyRescueCollisionDetectionDetailVO> queryRealtimeCollisionDetectionEvent(String vin) {
        log.info("queryRealtimeCollisionDetectionEvent, vin = {}", vin);
        try {
            InputCheckUtil.isNotBlank(vin, "车架号不能为空");
            NearbyRescueCollisionDetectionDetailVO detailVO = nearbyRescueHandleService.queryRealtimeCollisionDetectionEvent(
                    vin);
            return ThriftResponse.ok(detailVO);
        } catch (ParamInputErrorException e) {
            return ThriftResponse.failWithMessage(e.getMessage()).build();
        } catch (Exception e) {
            log.error("queryRealtimeCollisionDetectionEvent error", e);
        }
        return ThriftResponse.fail().build();
    }

    /**
     * 更新碰撞检测事件状态 （近场）
     *
     * @param request
     * @return
     */
    @Override
    public ThriftResponse<EmptyResponse> updateCollisionDetectionStatus(
            CollisionDetectionUpdateRequest request) {
        log.info("updateCollisionDetectionStatus, CollisionDetectionUpdateRequest = {}", request);
        try {
            InputCheckUtil.isNotNull(request, "请求参数不能为空");
            InputCheckUtil.isNotBlank(request.getEventId(), "事件ID不能为空");
            InputCheckUtil.isNotNull(request.getStatus(), "状态不可为空");
            CloudTriageEventUpdateRequest updateRequest = CloudTriageEventUpdateRequest.builder()
                    .eventId(request.getEventId())
                    .operator(request.getSsoUserId())
                    .status(request.getStatus()).build();
            ResponseCodeEnum responseCodeEnum = nearbyRescueHandleService.updateCollisionDetectionEvent(
                    updateRequest);
            if (responseCodeEnum == ResponseCodeEnum.SUCCESS) {
                return ThriftResponse.ok().build();
            }
            return ThriftResponse.failWithMessage(responseCodeEnum.getMsg()).build();
        } catch (ParamInputErrorException e) {
            return ThriftResponse.failWithMessage(e.getMessage()).build();
        } catch (Exception e) {
            log.error("updateCollisionDetectionStatus error", e);
            return ThriftResponse.failWithMessage("system error").build();
        }

    }

    /**
     * 查询未开始处置的碰撞检测事件列表
     *
     * @param vin
     * @return
     */
    @Override
    public ThriftResponse<List<CockpitCollisionDetectionDetailVO>> queryUnCheckCollisionDetectionEventList(String vin) {
        log.info("queryUnCheckCollisionDetectionEventList, vin = {}", vin);
        try {
            // 1. 参数校验
            InputCheckUtil.isNotNull(vin, "vin不可为空");
            // 参数转换 - VIN 转大写
            vin = vin.toUpperCase();
            // 2. 查询指定车辆在指定时间范围内所有未开始检核的碰撞检测列表
            List<BizCloudTriageEvent> eventList = cockpitHandleService.queryUnCheckCollisionDetectionEventList(vin);
            if (CollectionUtils.isEmpty(eventList)) {
                return ThriftResponse.ok().build();
            }
            // 3. 响应格式转换
            List<CockpitCollisionDetectionDetailVO> detailVOList = eventList.stream()
                    .map(event -> CockpitCollisionDetectionDetailVO.builder()
                            .eventId(event.getEventId())
                            .vin(event.getVin())
                            .vehicleId(event.getVehicleId())
                            .eventType(event.getEventType())
                            .eventTypeDes(EventTypeEnum.getMsgByCode(event.getEventType()))
                            .status(event.getStatus())
                            .statusDes(EventStatusEnum.getMsgByCode(event.getStatus()))
                            .eventTime(event.getEventTime().getTime() / 1000)
                            .createTime(event.getCreateTime().getTime() / 1000)
                            .build())
                    .collect(Collectors.toList());
            log.info("queryUnCheckCollisionDetectionEventList, eventList = {}", JacksonUtils.to(eventList));
            return ThriftResponse.ok(detailVOList);
        } catch (ParamInputErrorException e) {
            log.error("queryUnCheckCollisionDetectionEventList ParamInputErrorException error", e);
            return ThriftResponse.failWithMessage(e.getMessage()).build();
        } catch (Exception e) {
            log.error("queryUnCheckCollisionDetectionEventList system error", e);
            return ThriftResponse.fail().build();
        }

    }

    /**
     * 获取视频链接列表
     *
     * @param event
     * @param positionList
     * @param immediateRetrieve
     * @return
     */
    private VideoReponse getVideoLinkList(BizCloudTriageEvent event, List<String> positionList,
            Boolean immediateRetrieve) {
        // 设置查询时间
        Date eventTime = event.getEventTime();
        Long startTime = eventTime.getTime() / 1000 - configDTO.getBeforeEventSeconds();
        Long endTime = eventTime.getTime() / 1000 + configDTO.getAfterEventSeconds();
        // 查询视频列表
        VideoDTO videoDTO = VideoDTO.builder().start_ts(startTime).end_ts(endTime)
                .position(positionList)
                .vin(event.getVin())
                .immediateRetrieve(immediateRetrieve).build();
        return videoService.getHDVideo(videoDTO);
    }

    /**
     * 批量查询vin对应的车辆名称
     *
     * @param vinList
     * @return
     */
    private Map<String, String> batchQueryVinToName(List<String> vinList) {
        List<DelivererResponse> delivererResponseList = delivererQueryThriftServiceAdaptor.getDelivererListByVinList(
                vinList);
        if (CollectionUtils.isEmpty(delivererResponseList)) {
            return Collections.emptyMap();
        }
        Map<String, String> vinToNameMap = Maps.newHashMap();
        delivererResponseList.stream().forEach(delivererResponse -> {
            vinToNameMap.put(delivererResponse.getIdentifyNum(), delivererResponse.getAccount());
        });
        return vinToNameMap;
    }
}
