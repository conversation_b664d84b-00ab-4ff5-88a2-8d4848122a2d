package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/8/24
 */
@AllArgsConstructor
@Getter
public enum ResponseCodeEnum {
    SUCCESS(0, "ok"),
    FAILED(1, "failed"),
    SYSTEM_ERROR(2, "system error"),
    BAD_REQUEST(3, "bad request"),
    RECONFIRM(4, "reconfirm"),

    // 扫码挪车
    CHECK_VEHICLE_ID_ERROR(10, "check vehicleId error"),
    MOVE_CAR_REPEAT(11, "move car repeat"),
    REPORT_TIME_REACH_LIMIT(12, "the number of reports reached the upper limit"),
    
    // 车机更新状态
    OPERATING_BY_OTHERS(100, "他人正在操作"),
    STATUS_NOT_ROLLBACK(101, "状态不可回退"),



    ;

    private final Integer code;

    private final String msg;
}
