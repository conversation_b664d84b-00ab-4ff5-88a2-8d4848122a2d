package com.sankuai.walleops.cloud.triage.thrift;

import com.facebook.swift.service.ThriftException;
import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.walledelivery.thrift.exception.BizThriftException;
import com.sankuai.walledelivery.thrift.response.ThriftResponse;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleops.cloud.triage.thrift.vo.CockpitCollisionDetectionDetailVO;
import com.sankuai.walleops.cloud.triage.thrift.vo.CollisionAlertQueryRequest;
import com.sankuai.walleops.cloud.triage.thrift.vo.CollisionDetectionEventsQueryRequest;
import com.sankuai.walleops.cloud.triage.thrift.vo.CollisionDetectionEventsQueryResponse;
import com.sankuai.walleops.cloud.triage.thrift.vo.CollisionDetectionUpdateRequest;
import com.sankuai.walleops.cloud.triage.thrift.vo.EventDetailVO;
import com.sankuai.walleops.cloud.triage.thrift.vo.NearbyRescueCollisionDetectionDetailVO;
import java.util.List;

@InterfaceDoc(
        type = "Thrift",
        displayName = "查询碰撞检测事件触发原因",
        description = "查询碰撞检测事件触发原因",
        scenarios = "")
@ThriftService
public interface ThriftEventQueryService {

    @MethodDoc(
            displayName = "查询碰撞检测事件触发原因",
            description = "查询碰撞检测事件触发原因",
            parameters = {
                    @ParamDoc(name = "request", type = CollisionAlertQueryRequest.class, description = "", example = "{}")
            },
            returnValueDescription = "指定车辆、异常时间的碰撞事件的触发原因",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", type = String.class,  description = "碰撞检测标签", example = "{}")
            }
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 1)})
    ThriftResponse<String> getCollisionDetectionAlert(CollisionAlertQueryRequest request);


    @MethodDoc(
            displayName = "查询车辆最近的碰撞检测事件详情",
            description = "查询车辆最近的碰撞检测事件详情",
            parameters = {
                    @ParamDoc(name = "vin", type = String.class, description = "车架号", example = "{}")
            },
            returnValueDescription = "异常事件详情",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", type = EventDetailVO.class, description = "异常事件详情", example = "{}")
            }
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 2)})
    ThriftResponse<EventDetailVO> queryLatestCollisionDetectionEvent(String vin);

    @MethodDoc(
            displayName = "查询指定时间范围内的碰撞检测事件详情",
            description = "查询指定时间范围内的碰撞检测事件详情",
            parameters = {
                    @ParamDoc(name = "vin", type = String.class, description = "车架号", example = "{}"),
                    @ParamDoc(name = "startTime", type = Long.class, description = "开始时间戳", example = "1625097600000"),
                    @ParamDoc(name = "endTime", type = Long.class, description = "结束时间戳", example = "1625184000000")
            },
            returnValueDescription = "指定时间范围内的碰撞检测事件详情列表",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", type = List.class, description = "碰撞检测事件详情列表", example = "{}")
            }
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 3)})
    ThriftResponse<CollisionDetectionEventsQueryResponse> queryCollisionDetectionEventsByTimeRange(
            CollisionDetectionEventsQueryRequest request);


    @MethodDoc(
            displayName = "车机查询实时车辆碰撞检测详情接口",
            description = "车机查询实时车辆碰撞检测详情接口",
            parameters = {
                    @ParamDoc(name = "vin", type = String.class, description = "车架号", example = "{}")
            },
            returnValueDescription = "实时车辆碰撞检测详情",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", type = List.class, description = "实时车辆碰撞检测详情", example = "{}")
            }
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 4)})
    ThriftResponse<NearbyRescueCollisionDetectionDetailVO> queryRealtimeCollisionDetectionEvent(String vin);


    @MethodDoc(
            displayName = "车机更新碰撞检测状态接口",
            description = "车机更新碰撞检测状态接口",
            parameters = {
                    @ParamDoc(name = "vin", type = String.class, description = "车架号", example = "{}")
            },
            returnValueDescription = "状态响应",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", type = List.class, description = "", example = "{}")
            }
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 4)})
    ThriftResponse<EmptyResponse> updateCollisionDetectionStatus(
            CollisionDetectionUpdateRequest request);

    @MethodDoc(
            displayName = "提供给云控查询当前车辆存在的未检核的碰撞检测事件列表",
            description = "提供给云控查询当前车辆存在的未检核的碰撞检测事件列表",
            parameters = {
                    @ParamDoc(name = "vin", type = String.class, description = "车架号", example = "{}")
            },
            returnValueDescription = "状态响应",
            responseParams = {
                    @ParamDoc(name = "code", description = "响应码", example = {"0"}),
                    @ParamDoc(name = "message", description = "响应信息", example = {"success"}),
                    @ParamDoc(name = "data", type = List.class, description = "", example = "{}")
            }
    )
    @ThriftMethod(exception = {@ThriftException(type = BizThriftException.class, id = 5)})
    ThriftResponse<List<CockpitCollisionDetectionDetailVO>> queryUnCheckCollisionDetectionEventList(
            String vin);


}
