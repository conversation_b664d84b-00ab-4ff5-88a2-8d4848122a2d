package com.sankuai.walleops.cloud.triage.mockTest.crane;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.google.common.collect.Lists;
import com.sankuai.walleops.cloud.triage.adaptor.eve.EVEAdaptor;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.crane.MonitorEventStatusChangeCrane;
import com.sankuai.walleops.cloud.triage.mockTest.WithoutSpringBaseTest;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusDTO.MonitorCompute;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.service.AbnormalOrderReportService;
import com.sankuai.walleops.cloud.triage.service.impl.CloudTriageEventServiceImpl;
import com.sankuai.walleops.cloud.triage.service.impl.OperationChangeLogServiceImpl;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * 轮询云分诊事件状态crane, mock测试类
 *
 * <AUTHOR>
 * @date 2024/05/14
 */

// TODO add test
@Slf4j
@Ignore
public class MonitorEventStatusChangeCraneTest extends WithoutSpringBaseTest {

    @InjectMocks
    @Spy
    private MonitorEventStatusChangeCrane monitorEventStatusChangeCrane;

    @InjectMocks
    @Spy
    private CloudTriageEventServiceImpl cloudTriageEventService;

    @Mock
    private OperationChangeLogServiceImpl operationChangeLogService;

    @Mock
    AbnormalOrderReportService abnormalOrderReportService;

    @Mock
    private EVEAdaptor eveAdapter;

    @Test
    public void testNoUncompletedEventStatusSuccess() {
        // 近5min没有未完成的工单，无需更新
        // mock 返回工单
        ReflectionTestUtils.setField(monitorEventStatusChangeCrane, "orderProcessMaxDuration", 5);
        List<BizCloudTriageEvent> eventList = Lists.newArrayList();
        Mockito.doReturn(eventList).when(bizCloudTriageEventMapper).pageQuery(any());

        //调用测试函数
        monitorEventStatusChangeCrane.monitorEventStatusChange();

        // 验证没有数据被更新
        verify(bizCloudTriageEventMapper, times(0)).updateOne(any());
        verify(operationChangeLogService, times(0)).insertOperationLog(any());
        verify(abnormalOrderReportService, times(0)).sendProcessMessageToReporter(anyString(), any());

    }
    @Test
    public void testAllEventStatusUpdateSuccess() {
        // 一个工单从0->1（车连了云控）(0-未处理，1-处理中 2-已完成)
        // 一个工单从1->2（车没有云控）
        String vin1 = "testVin1";
        String vin2 = "testVin2";

        // mock 返回工单
        ReflectionTestUtils.setField(monitorEventStatusChangeCrane, "orderProcessMaxDuration", 5);
        BizCloudTriageEvent event1 = new BizCloudTriageEvent();
        event1.setId(1L);
        event1.setEventId("testEventId1");
        event1.setStatus(EventStatusEnum.INITED.getCode());
        event1.setVin(vin1);
        event1.setReporter("testReporter1");

        BizCloudTriageEvent event2 = new BizCloudTriageEvent();
        event2.setId(2L);
        event2.setEventId("testEventId2");
        event2.setStatus(EventStatusEnum.HANDLING.getCode());
        event2.setVin(vin2);
        event2.setReporter("testReporter2");
        List<BizCloudTriageEvent> eventList = Lists.newArrayList(event1, event2);
        Mockito.doReturn(eventList).when(bizCloudTriageEventMapper).pageQuery(any());

        // mock 总线返回的车辆数据
        VehicleStatusDTO vehicleStatus1 = new VehicleStatusDTO();
        vehicleStatus1.setVin(vin1);
        MonitorCompute monitorCompute1 = new MonitorCompute();
        monitorCompute1.setIsRemoteControlConnected(true);
        vehicleStatus1.setMonitorCompute(monitorCompute1);

        VehicleStatusDTO vehicleStatus2 = new VehicleStatusDTO();
        vehicleStatus2.setVin(vin2);
        MonitorCompute monitorCompute2 = new MonitorCompute();
        monitorCompute2.setIsRemoteControlConnected(false);
        vehicleStatus2.setMonitorCompute(monitorCompute2);
        List<VehicleStatusDTO> vehicleStatusList = Lists.newArrayList(vehicleStatus1, vehicleStatus2);
        Mockito.doReturn(vehicleStatusList).when(eveAdapter).getVehicleStatusByDataBus(any());

        // mock redis
        mockRedisLock();
        // mock 更新工单
        Mockito.doReturn(1).when(bizCloudTriageEventMapper).updateOne(any());

        // 调用测试函数
        monitorEventStatusChangeCrane.monitorEventStatusChange();

        verify(bizCloudTriageEventMapper, times(2)).updateOne(any());
        verify(operationChangeLogService, times(2)).insertOperationLog(any());
        verify(abnormalOrderReportService, times(2)).sendProcessMessageToReporter(anyString(), any());

    }

    @Test
    public void testOneEventStatusUpdateOneNoChangeSuccess() {
        // 一个工单从0->1，一个工单不变1（一辆车 车有云控）
        // 一个工单从1->2, 一个工单不变0（一辆车，车没有云控）
        String vin1 = "testVin1";
        String vin2 = "testVin2";

        // mock 返回工单
        ReflectionTestUtils.setField(monitorEventStatusChangeCrane, "orderProcessMaxDuration", 5);
        BizCloudTriageEvent event1 = new BizCloudTriageEvent();
        event1.setId(1L);
        event1.setEventId("testEventId1");
        event1.setStatus(EventStatusEnum.INITED.getCode());
        event1.setVin(vin1);
        event1.setReporter("testReporter1");

        BizCloudTriageEvent event2 = new BizCloudTriageEvent();
        event2.setId(2L);
        event2.setEventId("testEventId2");
        event2.setStatus(EventStatusEnum.HANDLING.getCode());
        event2.setVin(vin1);
        event2.setReporter("testReporter2");

        BizCloudTriageEvent event3 = new BizCloudTriageEvent();
        event3.setId(3L);
        event3.setEventId("testEventId3");
        event3.setStatus(EventStatusEnum.HANDLING.getCode());
        event3.setVin(vin2);
        event3.setReporter("testReporter3");

        BizCloudTriageEvent event4 = new BizCloudTriageEvent();
        event4.setId(4L);
        event4.setEventId("testEventId4");
        event4.setStatus(EventStatusEnum.INITED.getCode());
        event4.setVin(vin2);
        event4.setReporter("testReporter4");
        List<BizCloudTriageEvent> eventList = Lists.newArrayList(event1, event2, event3, event4);
        Mockito.doReturn(eventList).when(bizCloudTriageEventMapper).pageQuery(any());

        // mock 总线返回的车辆数据
        VehicleStatusDTO vehicleStatus1 = new VehicleStatusDTO();
        vehicleStatus1.setVin(vin1);
        MonitorCompute monitorCompute1 = new MonitorCompute();
        monitorCompute1.setIsRemoteControlConnected(true);
        vehicleStatus1.setMonitorCompute(monitorCompute1);

        VehicleStatusDTO vehicleStatus2 = new VehicleStatusDTO();
        vehicleStatus2.setVin(vin2);
        MonitorCompute monitorCompute2 = new MonitorCompute();
        monitorCompute2.setIsRemoteControlConnected(false);
        vehicleStatus2.setMonitorCompute(monitorCompute2);
        List<VehicleStatusDTO> vehicleStatusList = Lists.newArrayList(vehicleStatus1, vehicleStatus2);
        Mockito.doReturn(vehicleStatusList).when(eveAdapter).getVehicleStatusByDataBus(any());

        // mock redis
        mockRedisLock();
        // mock 更新工单
        Mockito.doReturn(1).when(bizCloudTriageEventMapper).updateOne(any());

        // 调用测试函数
        monitorEventStatusChangeCrane.monitorEventStatusChange();

        // 四个工单应该只有两个工单被更新了
        verify(bizCloudTriageEventMapper, times(2)).updateOne(any());
        verify(operationChangeLogService, times(2)).insertOperationLog(any());
        verify(abnormalOrderReportService, times(2)).sendProcessMessageToReporter(anyString(), any());

    }

    @Test
    public void testCannotGetBusDataNoUpdateSuccess() {
        // 一个工单状态0，一个1（获取的车辆状态中没有这个车/状态总线报错）
        String vin = "testVin";

        // mock 返回工单
        ReflectionTestUtils.setField(monitorEventStatusChangeCrane, "orderProcessMaxDuration", 5);
        BizCloudTriageEvent event1 = new BizCloudTriageEvent();
        event1.setId(1L);
        event1.setEventId("testEventId1");
        event1.setStatus(EventStatusEnum.INITED.getCode());
        event1.setVin(vin);
        event1.setReporter("testReporter1");

        BizCloudTriageEvent event2 = new BizCloudTriageEvent();
        event2.setId(2L);
        event2.setEventId("testEventId2");
        event2.setStatus(EventStatusEnum.HANDLING.getCode());
        event2.setVin(vin);
        event2.setReporter("testReporter2");
        List<BizCloudTriageEvent> eventList = Lists.newArrayList(event1, event2);
        Mockito.doReturn(eventList).when(bizCloudTriageEventMapper).pageQuery(any());

        // mock 总线返回异常
        Exception exception = new RuntimeException("unit test exception");
        Mockito.doThrow(exception).when(eveAdapter).getVehicleStatusByDataBus(any());

        // 调用测试函数
        try {
            monitorEventStatusChangeCrane.monitorEventStatusChange();
        } catch (Exception e) {
            log.info("result:{}", e.getMessage());
        }
    }

    @Test
    public void testCannotGetRemoteControlConnectionNoUpdateSuccess() {
        // 一个工单状态0，一个1（获取的车辆状态中云控是null）
        String vin = "testVin";

        // mock 返回工单
        ReflectionTestUtils.setField(monitorEventStatusChangeCrane, "orderProcessMaxDuration", 5);
        BizCloudTriageEvent event1 = new BizCloudTriageEvent();
        event1.setId(1L);
        event1.setEventId("testEventId1");
        event1.setStatus(EventStatusEnum.INITED.getCode());
        event1.setVin(vin);
        event1.setReporter("testReporter1");

        BizCloudTriageEvent event2 = new BizCloudTriageEvent();
        event2.setId(2L);
        event2.setEventId("testEventId2");
        event2.setStatus(EventStatusEnum.HANDLING.getCode());
        event2.setVin(vin);
        event2.setReporter("testReporter2");
        List<BizCloudTriageEvent> eventList = Lists.newArrayList(event1, event2);
        Mockito.doReturn(eventList).when(bizCloudTriageEventMapper).pageQuery(any());

        // mock 总线返回的车辆数据
        VehicleStatusDTO vehicleStatus1 = new VehicleStatusDTO();
        vehicleStatus1.setVin(vin);
        MonitorCompute monitorCompute1 = new MonitorCompute();
        monitorCompute1.setIsRemoteControlConnected(null);
        vehicleStatus1.setMonitorCompute(monitorCompute1);
        List<VehicleStatusDTO> vehicleStatusList = Lists.newArrayList(vehicleStatus1);
        Mockito.doReturn(vehicleStatusList).when(eveAdapter).getVehicleStatusByDataBus(any());

        // mock redis
        mockRedisLock();
        // mock 更新工单
        Mockito.doReturn(1).when(bizCloudTriageEventMapper).updateOne(any());

        // 调用测试函数
        monitorEventStatusChangeCrane.monitorEventStatusChange();

        // 没有工单被更新
        verify(bizCloudTriageEventMapper, times(0)).updateOne(any());
        verify(operationChangeLogService, times(0)).insertOperationLog(any());
        verify(abnormalOrderReportService, times(0)).sendProcessMessageToReporter(anyString(), any());

    }
}
