package com.sankuai.walleops.cloud.triage.adaptor;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.it.iam.common.base.gson.bridge.JSONObject;
import com.sankuai.walleops.cloud.triage.pojo.dto.IntegerItem;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusV2;
import com.sankuai.walleops.cloud.triage.pojo.response.VehicleRealtimeV2Response;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CheckVehicleConnectionStatusAdaptor {
    /**
     * v2接口：车辆实时状态查询 - BA鉴权版本
     */
    private static final String VEHICLE_REALTIME_V2_BA_URL = "/autonomous-driving/rmanage/v2/realtime-ba";
    /**
     * Spider服务域名
     */
    @Value("${spider.service.host.name}")
    String cloudOperationHostName;

    /**
     * 接口BA鉴权clientId
     */
    @Value("$KMS{cloud.operation.v2.ba.clientId}")
    private String clientId;

    /**
     * 接口BA鉴权clientSecret
     */
    @Value("$KMS{cloud.operation.v2.ba.clientSecret}")
    private  String clientSecret;


    /**
     * 查询车辆实时状态 v2 (BA鉴权)
     * @param vinList 车辆VIN码列表
     * @return 返回车辆实时状态信息
     */
    public VehicleRealtimeV2Response getVehicleRealtimeStatusV2BA(List<String> vinList)  {
        String url =  cloudOperationHostName+ VEHICLE_REALTIME_V2_BA_URL;

        // 1. 构建请求参数
        Map<String, Object> params = new HashMap<>();

        vinList = vinList.stream().map(String::toUpperCase).collect(Collectors.toList());
        params.put("vinList", String.join(",", vinList));

        // 2. 填充BA鉴权参数
        Map<String, String> headers = buildBaAuthHeaders(VEHICLE_REALTIME_V2_BA_URL, "GET");

        // 3. 发送GET请求
        try {
            String response = CommonUtil.doGet(url, params, headers);
            log.info("getVehicleRealtimeStatusV2BA, raw response = {}", response);
            VehicleRealtimeV2Response v2Response = JSONObject.parseObject(response, VehicleRealtimeV2Response.class);
           if (v2Response.getCode() == 0 && Objects.nonNull(v2Response.getData())) {
               // 过滤
               v2Response.getData().removeIf(vehicle ->
                       vehicle.getCockpitStatus().getPresent() == false);
               return v2Response;
           }
        } catch (Exception e) {
            log.error("getVehicleRealtimeStatusV2BA failed!", e);
        }
        return null;
    }

    /**
     * 构建BA鉴权请求头
     *
     * @param urlPath HTTP请求路径
     * @param httpMethod HTTP请求方法 (GET, POST等)
     * @return 包含BA鉴权信息的请求头Map
     */
    public Map<String, String> buildBaAuthHeaders(String urlPath, String httpMethod) {
        Map<String, String> headers = new HashMap<>();

        // 生成时间戳
        String dateBa = CommonUtil.getDate();

        // 生成Authorization签名
        String authorization = CommonUtil.getAuthorization(
                urlPath,
                httpMethod,
                dateBa,
                clientId,
                clientSecret
        );

        // 设置请求头
        headers.put("Content-Type", "application/json;charset=UTF-8");
        headers.put("Date", dateBa);
        headers.put("Authorization", authorization);

        return headers;
    }

    /**
     *
     * @param cockpitStatus 坐席状态
     * @return true表示已连接，false表示其他连接
     */
    public Boolean isCockpitConnected(IntegerItem cockpitStatus) {
        Integer statusValue = cockpitStatus.getValue();
        // 只有明确的连接状态才返回true
        // 2: 云代驾连接, 4: 云辅助连接 ,5:坐席键盘控车中(云辅助)
        return statusValue == 2 || statusValue == 4 ||statusValue == 5;
    }

}
