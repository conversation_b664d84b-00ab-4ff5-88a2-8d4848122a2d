<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sankuai.walleops.cloud.triage</groupId>
    <artifactId>cloud-triage</artifactId>
    <version>0.0.3</version>
    <packaging>jar</packaging>
    <name>cloud-triage</name>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-parent</artifactId>
        <version>1.8.5.3</version>
    </parent>

    <properties>
        <sso.version>2.5.5</sso.version>
    </properties>



    <dependencies>
        <dependency>
            <groupId>com.sankuai.carosscan</groupId>
            <artifactId>eve_common_client</artifactId>
            <version>1.0.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-simple</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lombok-mapstruct-binding</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.dpsf</groupId>
            <artifactId>dpsf-net</artifactId>
            <version>3.8.2</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.walleeve</groupId>
            <artifactId>walle-eve-domain</artifactId>
            <version>1.1.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
            <version>0.12.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.meituan.image</groupId>
            <artifactId>client</artifactId>
        </dependency>
        <!-- kms依赖包 -->
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-tls-sdk</artifactId>
            <version>0.7.1-RC3</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.walleeve</groupId>
            <artifactId>walle-eve-utils</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>mdp-boot-initializr-common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-mafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-crane</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>persistence-api</artifactId>
            <version>1.0.2</version>
        </dependency>

        <!--目前 scribe-log4j2 依赖 log4j2 版本 -->
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
            <version>2.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.inf</groupId>
                    <artifactId>xmd-log4j2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.inf</groupId>
                    <artifactId>xmd-common-log4j2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
            <version>2.2.0</version>
        </dependency>

        <!-- mtrace begin -->
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>idl-mtrace</artifactId>
        </dependency>
        <!-- mtrace end -->

        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-generator</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
            <version>2.0.3</version>
        </dependency>
        <!--database-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.2</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.4.1</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.5</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>2.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83_noneautotype</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
            <version>${sso.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.security</groupId>
            <artifactId>sec-sdk</artifactId>
            <version>1.3.9</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>

        </dependency>
        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-async-client</artifactId>
            <version>1.0.4</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cip</groupId>
            <artifactId>pike-message-sdk</artifactId>
            <version>2.0.9-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.cip</groupId>
            <artifactId>pike-message-client</artifactId>
            <version>2.0.5-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>uac-common-sdk</artifactId>
            <version>1.1.16</version>
        </dependency>

        <!--单测相关-->
        <!--mockito依赖-->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!--powermock依赖-->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.2</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.walledelivery</groupId>
            <artifactId>walle-delivery-basic-client</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
