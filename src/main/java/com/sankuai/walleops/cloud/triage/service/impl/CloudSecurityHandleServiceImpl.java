package com.sankuai.walleops.cloud.triage.service.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.adaptor.BitrateChangeAdaptor;
import com.sankuai.walleops.cloud.triage.component.handleStrategy.HandleStrategy;
import com.sankuai.walleops.cloud.triage.component.pike.PikeServer;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.MrmCalledStatusEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.VideoDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.CockpitHandleService;
import com.sankuai.walleops.cloud.triage.service.CollisionDetectionHandleService;
import com.sankuai.walleops.cloud.triage.service.ExternalInterfaceService;
import com.sankuai.walleops.cloud.triage.service.VideoService;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class CloudSecurityHandleServiceImpl implements CollisionDetectionHandleService {

    @Resource
    private Map<String, HandleStrategy> eventHandleStrategyMap;

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private VideoService videoService;

    @Resource
    private BitrateChangeAdaptor bitrateChangeAdaptor;

    @Resource
    private ExternalInterfaceService externalInterfaceService;

    @MdpConfig("videoRequestDuration")
    private ArrayList<Integer> videoRequestDuration;

    @Resource
    private PikeServer pikeServer;

    @Resource
    private CockpitHandleService cockpitHandleService;

    /**
     * 云安全处置碰撞检测事件
     *
     * @param cloudTriageEvent
     */
    @Override
    public void handleCollisionDetectionEvent(BizCloudTriageEvent cloudTriageEvent) {
        if (cloudTriageEvent == null) {
            return;
        }
        // 判断车辆是否被救援
        if (externalInterfaceService.getRescueOrderStatus(cloudTriageEvent.getVin())) {
            log.info("handleCollisionDetectionEvent, vin = {} is rescuing", cloudTriageEvent.getVin());
            return;
        }
        // 根据异常事件类型去执行响应的策略
        String strategyName = EventTypeEnum.getByCode(cloudTriageEvent.getEventType()).getEventName() + "-handle";
        if (eventHandleStrategyMap.containsKey(strategyName)) {
            HandleStrategy handleStrategy = eventHandleStrategyMap.get(strategyName);
            handleStrategy.process(cloudTriageEvent);
        } else {
            log.error("getCollisionDetectionList,can't find strategyName",
                    new IllegalArgumentException("当前碰撞检测事件找不到处置策略类"));
        }
        // 异常事件保存
        try {
            int result = cloudTriageEventService.save(cloudTriageEvent);
            if (result < 1) {
                log.info("database write failure! cloudTriageEvent = {}", cloudTriageEvent);
            }
            // 插入成功后，且为分配给坐席的，且初始化的状态，立即发起呼叫
            if (result > 0) {
                isCallCockpit(cloudTriageEvent);
            }
            //当数据插入成功后，开始触发高清视频打捞
            startHDVideoSalvage(cloudTriageEvent.getVin(), cloudTriageEvent.getEventTime());
            pikeServer.notifyNewEvent(cloudTriageEvent);
        } catch (Exception e) {
            log.error("save cloudTriageEvent error: {}", cloudTriageEvent, e);
        }
    }

    /**
     * 触发高清视频打捞
     *
     * @param vin
     * @param eventTime
     */
    public void startHDVideoSalvage(String vin, Date eventTime) {
        // 1）触发视频码率切换， todo 历史逻辑
        bitrateChangeAdaptor.handleBitrateChangeEvent(vin, eventTime);
        // todo : 为什么不直接使用上文中的事故时间，因为该值与数据库值不统一，而前端是基于数据库中的值进行请求，因为这里读取数据库保持一致
        long accidentTime = DateUtil.roundAndConvertToSeconds(eventTime.getTime());
        // 1）请求前半段的高清环视视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(0), new Date().getTime() / 1000,
                Arrays.asList("v_loop"), true);

        //2）请求完整时间段的高清环视视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(0),
                accidentTime + videoRequestDuration.get(1), Arrays.asList("v_loop"), true);
        //3） 请求前半段的高清拼接视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(2), accidentTime, Arrays.asList("v_concat"),
                true);
        //4）请求完整时间段的高清拼接视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(2),
                accidentTime + videoRequestDuration.get(3), Arrays.asList("v_concat"), true);
    }

    /**
     * 请求高清视频
     *
     * @param vin
     * @param start_ts
     * @param end_ts
     * @param perspectives
     * @param immediateRetrieve
     */
    public void requestHDVideoByType(String vin, Long start_ts, Long end_ts, List<String> perspectives,
            Boolean immediateRetrieve) {
        VideoDTO videoDTO = VideoDTO.builder()
                .vin(vin)
                .start_ts(start_ts)
                .end_ts(end_ts)
                .position(perspectives)
                .immediateRetrieve(immediateRetrieve).build();
        videoService.getHDVideo(videoDTO);
    }

    /**
     * 更新事件呼叫状态，并发起呼叫
     *
     * @param event
     * @throws Exception
     */
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public void updateEventAndCallCockpit(BizCloudTriageEvent event) throws Exception {
        // 1、更新事件呼叫状态
        event.setMrmCalled(MrmCalledStatusEnum.CALLING.getCode());
        Boolean result = cloudTriageEventService.updateBizCloudTriageEvent(event);
        // 2、发起呼叫
        if (result) {
            cockpitHandleService.requestCloudOperation(event.getVin(), event.getEventType(),
                    CommonConstant.REQUEST_CLOUD_OPERATION_ACTION);
        }
    }

    /**
     * 判断是否需要呼叫中控台
     *
     * @param event
     */
    private void isCallCockpit(BizCloudTriageEvent event) {
        log.info("CollisionDetectionHandleService#isCallCockpit, event:{}", event);
        if (Objects.nonNull(event) &&
                (Objects.isNull(event.getStatus())
                        || Objects.equals(event.getStatus(), EventStatusEnum.INITED.getCode()))
        ) {
            try {
                // 更新呼叫字段，并发起呼叫
                ((CloudSecurityHandleServiceImpl) AopContext.currentProxy()).updateEventAndCallCockpit(event);
            } catch (Exception e) {
                log.error("updateEventAndCallCockpit error, event:{}", event, e);
            }
        }
    }


}
