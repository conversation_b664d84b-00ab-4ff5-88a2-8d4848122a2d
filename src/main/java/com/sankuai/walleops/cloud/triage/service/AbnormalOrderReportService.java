package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.pojo.dto.NotificationMessageDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.StatusCheckResultDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;

import java.util.List;

/**
 * 用于处理人工上报分诊异常工单接口
 */
public interface AbnormalOrderReportService {

    void sendProcessMessageToReporter(String reporter, NotificationMessageDTO notificationMessageDTO);

    StatusCheckResultDTO validateVehicleStatusAndGenerateResult(String vin);

    Integer insertEventOrder(CloudTriageEventUpdateRequest request, VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO)
            throws Exception;

    String getVehicleNameFromEventId(String eventId);

    Boolean isReportCSMOrder(Integer eventType, String vin);

    Boolean isReportCSMOrderGray(Integer eventType, String vin);

    List<BizCloudTriageEvent> filterEvent(List<BizCloudTriageEvent> eventList);

    int insertOrderAndCallCloudOperation(BizCloudTriageEvent cloudTriageEvent) throws Exception ;

}
