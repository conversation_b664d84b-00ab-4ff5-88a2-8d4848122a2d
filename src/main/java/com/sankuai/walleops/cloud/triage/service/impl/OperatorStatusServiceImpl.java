package com.sankuai.walleops.cloud.triage.service.impl;

import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperatorStatusDTO;
import com.sankuai.walleops.cloud.triage.proxy.SquirrelProxy;
import com.sankuai.walleops.cloud.triage.service.OperatorStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class OperatorStatusServiceImpl implements OperatorStatusService {

    @Resource
    private SquirrelProxy squirrelProxy;

    /**
     * 查询运营人员上下线状态
     * @param misId
     * @return OperatorStatusDTO
     * */
    public OperatorStatusDTO queryOperatorStatus(String misId) {
        String value = squirrelProxy.hget(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY,
                                            CommonConstant.OPERATOR_STATUS,
                                            misId);
        if(value != null) {
            String[] items = value.split("_");
            if(items.length == 2) {
                Integer status = Integer.valueOf(items[0]);
                Long timestamp = Long.valueOf(items[1]);
                OperatorStatusDTO dto = new OperatorStatusDTO();
                dto.setMisId(misId);
                dto.setStatus(status);
                dto.setTimestamp(timestamp);
                return dto;
            }
        }

        return null;
    }

    /**
     * 设置运营人员上下线状态
     * @param misId
     * @param status 0: offline, 1: online
     * @return true: 设置成功, false: 设置失败
     * */
    public boolean setOperatorStatus(String misId, Integer status) {
        String value = status + "_" +  System.currentTimeMillis();

        boolean result = squirrelProxy.hset(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY,
                                            CommonConstant.OPERATOR_STATUS,
                                            misId,
                                            value);

        return result;
    }

    /**
     * 删除运营人员上下线状态信息
     * @param misId
     * @return true: 删除成功, false: 删除失败
     * */
    public boolean delOperatorStatus(String misId) {
        boolean result = squirrelProxy.hdel(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY,
                                            CommonConstant.OPERATOR_STATUS,
                                            misId);

        return result;
    }
}
