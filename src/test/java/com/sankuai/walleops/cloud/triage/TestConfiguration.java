package com.sankuai.walleops.cloud.triage;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.sankuai.walleops.cloud.triage.adaptor.eve.EVEAdaptor;
import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.component.pike.PikeServer;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;

import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试环境配置类
 * 提供测试所需的mock bean
 */
@Configuration
public class TestConfiguration {

    /**
     * 创建eventMsgProducer的mock bean
     * 解决No qualifying bean of type 'com.meituan.mafka.client.producer.IProducerProcessor[?, ?]'错误
     */
    @Bean(name = "eventMsgProducer")
    @Primary
    public IProducerProcessor mockEventMsgProducer() {
        return Mockito.mock(IProducerProcessor.class);
    }
    
    /**
     * 创建moveCarEventProducer的mock bean
     */
    @Bean(name = "moveCarEventProducer")
    public IProducerProcessor mockMoveCarEventProducer() {
        return Mockito.mock(IProducerProcessor.class);
    }
    
    /**
     * 创建infoProducer的mock bean
     */
    @Bean(name = "infoProducer")
    public IProducerProcessor mockInfoProducer() {
        return Mockito.mock(IProducerProcessor.class);
    }
    
    /**
     * Mock EVEAdaptor
     */
    @Bean
    @Primary
    public EVEAdaptor mockEVEAdaptor() {
        EVEAdaptor mockAdaptor = Mockito.mock(EVEAdaptor.class);
        
        // 为测试准备mock返回值
        VehicleStatusDTO vehicleStatusDTO = new VehicleStatusDTO();
        // 设置一个简单的车辆管理对象
        VehicleStatusDTO.VehicleManage vehicleManage = new VehicleStatusDTO.VehicleManage();
        vehicleManage.setFirstClassModel("H24"); // 设置为H24车型，用于测试过滤逻辑
        vehicleStatusDTO.setVehicleManage(vehicleManage);
        
        // 配置mock返回
        Mockito.when(mockAdaptor.getVehicleStatusFromDataBusByVin(Mockito.anyString()))
               .thenReturn(vehicleStatusDTO);
        
        return mockAdaptor;
    }
    
    /**
     * Mock InformationApiService
     */
    @Bean
    @Primary
    public InformationApiService mockInformationApiService() {
        InformationApiService mockService = Mockito.mock(InformationApiService.class);
        
        // 为测试准备mock返回值
        VehicleRealtimeStatusDTO statusDTO = new VehicleRealtimeStatusDTO();
        statusDTO.setPurpose("测试用途");
        statusDTO.setPark("测试停车场");
        statusDTO.setVehicleId("京AG1234");
        
        // 配置mock返回
        Mockito.when(mockService.queryVehicleStatusByVin(Mockito.anyString()))
               .thenReturn(statusDTO);
        
        List<VehicleRealtimeStatusDTO> resultList = new ArrayList<>();
        resultList.add(statusDTO);
        
        Mockito.when(mockService.callVehicleStatusService(Mockito.any(Map.class)))
               .thenReturn(resultList);
        
        return mockService;
    }
    
    /**
     * Mock PikeServer
     */
    @Bean
    @Primary
    public PikeServer mockPikeServer() {
        PikeServer mockServer = Mockito.mock(PikeServer.class);
        
        // 配置mock行为，notifyNewEvent方法不做任何实际操作
        Mockito.doNothing().when(mockServer).notifyNewEvent(Mockito.any());
        
        return mockServer;
    }
} 