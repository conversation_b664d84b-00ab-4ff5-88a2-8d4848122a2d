package com.sankuai.walleops.cloud.triage.service.impl;

import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.mapper.BizOperateHistoryMapper;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperateHistoryPageDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizOperateHistory;
import com.sankuai.walleops.cloud.triage.pojo.request.OperateHistoryPageRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.OperateHistoryRequest;
import com.sankuai.walleops.cloud.triage.service.OperateHistoryService;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/9/19
 */
@Service
@Slf4j
public class OperateHistoryServiceImpl implements OperateHistoryService {
    @Resource
    private BizOperateHistoryMapper bizOperateHistoryMapper;

    @Resource
    private InformationApiService informationApiService;

    public int addOperateHistory(OperateHistoryRequest request) {
        BizOperateHistory operateHistory = new BizOperateHistory();
        BeanUtils.copyProperties(request, operateHistory);
        return bizOperateHistoryMapper.insertOne(operateHistory);
    }

    @Override
    public OperateHistoryPageDTO queryOperateHistoryList(OperateHistoryPageRequest request) {
        if (StringUtils.isEmpty(request.getVin()) && StringUtils.isEmpty(request.getEventId())) {
            throw new RuntimeException("vin和eventId不能同时为空");
        }

        Map<String, Object> param = new HashMap<>();
        param.put("logSource", CommonConstant.EVENT_SOURCE_FE);
        param.put("startTimestamp", request.getStartTime());
        param.put("endTimestamp", request.getEndTime());
        if (!StringUtils.isEmpty(request.getEventId())) { //eventId字段的优先级比vin字段高
            param.put("bizUniqueKey", request.getEventId());
        } else {
            param.put("vin", request.getVin());
        }
        param.put("page", request.getPage());
        param.put("size", request.getSize());

        log.info("查询信息中心操作记录, param: {}", param);
        OperateHistoryPageDTO pageDTO = informationApiService.queryOperateHistoryList(param);
        log.info("查询信息中心操作记录, result: {}", pageDTO);
        return pageDTO;
    }
}
