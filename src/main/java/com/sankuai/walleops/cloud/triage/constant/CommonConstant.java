package com.sankuai.walleops.cloud.triage.constant;

import java.util.Date;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/8/28
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonConstant {

    public static final String UNKNOWN = "unknown";

    public static final String APP_KEY = "com.sankuai.walleops.cloud.triage";

    public static final int DEFAULT_PAGE_SIZE = 10;

    public static final int DEFAULT_MAX_PAGE_SIZE = 100;

    public static final int DEFAULT_PAGE_NUM = 1;

    public static final String HEAD_SECOND = "000000";

    public static final String TAIL_SECOND = "240000";

    public static final int SECOND_TO_MILLIS = 1000;

    public static final int MINUTE_TO_SECOND = 60;

    public static final int HOUR_TO_MINUTE = 60;

    // 事件查询更新锁
    public static final String EVENT_QU_LOCK = "cloud_triage_event_lock";

    // 事件更新锁
    public static final String EVENT_UPDATE_LOCK = "cloud_triage_event_update_lock";

    public static final int LOCK_EXPIRE_SECONDS = 5;

    // 事件插入锁
    public static final String EVENT_INSERT_LOCK = "cloud_triage_event_insert_lock";

    public static final int INSERT_LOCK_SECONDS = 36000;

    public static final Date DEFAULT_EARLY_DATE = new Date(1000000000000L); // 2001-09

    // 事件来源
    public static final String EVENT_SOURCE_FE = "cloudTriageFE";

    public static final String TRIAGE_NEW_EVENT_CATEGORY = "triage_new_event";

    public static final String TRIAGE_NEW_EVENT_CLIENT_KEY = "client_link";

    public static final String KEY_ACCIDENT_HASH = "accident_hash";

    public static final String OPERATOR_STATUS = "operator_status";

    public static final Integer RESPONSE_SUCCESS = 200;

    /**
     * 零 (Integer)
     */
    public static final Integer ZERO = 0;

    /**
     * 请求云控资源 source, 1 - 代表分诊
     */
    public static final Integer REQUEST_CLOUD_OPERATION_SOURCE = 1;

    /**
     * 请求云控资源 action, 默认为 "call"
     */
    public static final String REQUEST_CLOUD_OPERATION_ACTION = "call";

    /**
     * 取消请求云控资源 action, 默认为 "cancel"
     */
    public static final String CANCEL_CLOUD_OPERATION_ACTION = "cancel";

    /**
     * 调用第三方接口无响应
     */
    public static final String NO_RESPONSE = "请求无响应";

    /**
     * 车辆在线状态 - 数据总线服务 - https://km.sankuai.com/collabpage/2192066535
     */
    public static final Integer VEHICLE_ONLINE_STATUS = 2;

    /**
     * 表示由系统操作时的标识
     */
    public static final String SYSTEM = "system";

    /**
     * ALL(Integer) 表示灰度开关的全量标识
     */
    public static final Integer ALL = 100;

    /**
     * ALL(String) 表示灰度开关的全量标识
     */
    public static final String ALL_STRING = "ALL";

    /**
     * 异常时间表扩展字段里挪车原因字段名称
     */
    public static final String EXT_FIELD_REASON_FOR_MOVING_CARS = "moveCarReason";

    /**
     * 操作日志 - 更新操作类型
     */
    public static final Integer OPERATION_LOG_UPDATE = 200;

    /**
     * Lion配置Key - 事件类型车辆过滤配置
     */
    public static final String LION_KEY_EVENT_VEHICLE_FILTER_CONFIG = "event.vehicle.filter.config";
}
