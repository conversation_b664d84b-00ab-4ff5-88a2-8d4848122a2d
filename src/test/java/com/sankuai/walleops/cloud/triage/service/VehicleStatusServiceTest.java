package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleHistoryStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.request.VideoHistoryStatusRequest;
import com.sankuai.walleops.cloud.triage.service.impl.VehicleStatusServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.mockito.Mockito.any;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class VehicleStatusServiceTest {

    @InjectMocks
    private final VehicleStatusService vehicleStatusService = new VehicleStatusServiceImpl();

    @Mock
    private InformationApiService informationApiService;
    @Mock
    ExternalInterfaceService externalInterfaceService;

    @Test
    public void queryVehicleHistoryStatusTest() {
        VideoHistoryStatusRequest request = new VideoHistoryStatusRequest();
        request.setVin("1");
        request.setStartTime("2022-12-08 10:10:10");
        request.setEndTime("2022-12-08 10:10:30");

        List<VehicleHistoryStatusDTO> historyStatusDTOList = new ArrayList<>();
        when(informationApiService.queryVehicleHistoryStatus(any())).thenReturn(historyStatusDTOList);

        List<VehicleHistoryStatusDTO> statusDTOList =
                vehicleStatusService.queryVehicleHistoryStatus(request);

        assertNotNull(statusDTOList);
        assertEquals(0, statusDTOList.size());
    }

    @Test
    public void test(){
       Object rest =  externalInterfaceService.getEvaluationAlertsReflect();
       System.out.println(rest);
    }
}
