package com.sankuai.walleops.cloud.triage.service.impl;

import com.sankuai.walleeve.utils.AESUtils;
import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.vo.EncryptVinVO;
import com.sankuai.walleops.cloud.triage.service.AESAdminService;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class AESAdminServiceImpl implements AESAdminService {

    /**
     * 车架号加密密钥
     */
    @Value("${vin.encrypt.secret}")
    private String vinEncryptSecret;

    @Resource
    private InformationApiService informationApiService;

    /**
     * 加密车架号列表
     *
     * @param vinList 原始车架号列表
     * @return 加密后的车架号列表，如果加密失败则返回空列表
     */
    @Override
    public List<EncryptVinVO> encryptVinList(List<String> vinList) {
        // 1 车辆列表不能为空
        if (CollectionUtils.isEmpty(vinList)) {
            return new ArrayList<>();
        }
        // 2 加密
        List<String> failedVinList = new ArrayList<>();
        List<EncryptVinVO> encryptVinVOList = vinList.stream().map(vin -> {
                    String encryptVin = encryptVin(vin);
                    if (StringUtils.isBlank(encryptVin)) {
                        failedVinList.add(vin);
                        return null;
                    }
                    return EncryptVinVO.builder().encryptVin(encryptVin).vin(vin).build();
                })
                .filter(Objects::nonNull) // 过滤掉 null
                .collect(Collectors.toList());
        log.info("encryptVinList, failedVinList = {}", failedVinList);
        return encryptVinVOList;
    }

    /**
     * 解密车架号列表
     *
     * @param encryptVinList 加密的车架号列表
     * @return 解密后的车架号列表，如果解密失败则返回空列表
     */
    @Override
    public List<EncryptVinVO> decryptVinList(List<String> encryptVinList) {
        // 1 加密车架号列表不能为空
        if (CollectionUtils.isEmpty(encryptVinList)) {
            return new ArrayList<>();
        }
        // 2 解密
        List<String> failedEncryptVinList = new ArrayList<>();
        List<EncryptVinVO> decryptVinVOList = encryptVinList.stream().map(encryptVin -> {
                    String vin = decryptVin(encryptVin);
                    if (StringUtils.isBlank(vin)) {
                        failedEncryptVinList.add(encryptVin);
                        return null;
                    }
                    return EncryptVinVO.builder().encryptVin(encryptVin).vin(vin).build();
                })
                .filter(Objects::nonNull) // 过滤掉 null
                .collect(Collectors.toList());
        log.info("encryptVinList, failedVinList = {}", failedEncryptVinList);
        return decryptVinVOList;
    }

    /**
     * 解密车架号
     *
     * @param encryptVin 加密的车架号
     * @return 解密后的车架号，如果解密失败则返回 null
     */
    public String decryptVin(String encryptVin) {
        // 检查加密的车架号是否为空
        if (StringUtils.isBlank(encryptVin)) {
            return null;
        }
        try {
            // 使用 AES 算法解密车架号
            return AESUtils.decrypt(encryptVin, vinEncryptSecret);
        } catch (Exception e) {
            // 记录解密错误日志
            log.error("decryptVin# decrypt error", e);
        }
        return null;
    }

    /**
     * 通过加密的车架号获取车辆ID
     *
     * @param encryptVin 加密的车架号
     * @return 车辆ID，如果获取失败则返回 null
     */
    @Override
    public String getVehicleIdByEncryptVin(String encryptVin) {

        // 1 解码vin
        String vin = decryptVin(encryptVin);
        if(StringUtils.isBlank(vin)){
            return null;
        }
        VehicleRealtimeStatusDTO statusDTO = informationApiService.queryVehicleStatusByVin(vin);
        if(Objects.isNull(statusDTO)){
            log.error("informationApiService,queryVehicleStatusByVin 获取{}对应的车牌号信息失败", vin);
            return null;
        }
        return statusDTO.getVehicleId();
    }

    /**
     * 加密车架号
     *
     * @param vin 原始车架号
     * @return 加密后的车架号，如果加密失败则返回 null
     */
    private String encryptVin(String vin) {
        // 检查原始车架号是否为空
        if (StringUtils.isBlank(vin)) {
            return null;
        }
        try {
            // 使用 AES 算法加密车架号
            String encryptVin = AESUtils.encrypt(vin, vinEncryptSecret);
            return URLEncoder.encode(encryptVin, "UTF-8");
        } catch (Exception e) {
            // 记录加密错误日志
            log.error("encryptVin# encrypt error", e);
        }
        return null;
    }




}
