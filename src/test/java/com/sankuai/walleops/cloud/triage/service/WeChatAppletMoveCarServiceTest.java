package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.BaseTest;
import com.sankuai.walleops.cloud.triage.pojo.request.MoveCarRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.annotation.Resource;
import org.junit.Ignore;
import org.junit.Test;
@Ignore
public class WeChatAppletMoveCarServiceTest extends BaseTest {

    @Resource
    private WeChatAppletMoveCarService weChatAppletMoveCarService;

    @Test
    public void checkAndCreateMoveCarOrderTest() throws Exception {
        MoveCarRequest request = new MoveCarRequest();
        request.setMoveCarReason("挪车");
        request.setVehicleId("M5230");
        request.setEventType(32);
        request.setCarPosition("1234");

        String openId = "123456789";

        int threadCount = 5; // 并发线程数
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

        for (int i = 0; i < threadCount; i++) {
            executorService.submit(() -> {
                try {
                    CommonResponse response = weChatAppletMoveCarService.checkAndCreateMoveCarOrder(request, openId);
                    System.out.println("response: "+ response);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await(); // 等待所有线程执行完毕
        executorService.shutdown();
    }
}
