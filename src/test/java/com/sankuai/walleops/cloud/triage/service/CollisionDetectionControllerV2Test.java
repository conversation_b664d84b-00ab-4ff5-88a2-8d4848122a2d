package com.sankuai.walleops.cloud.triage.service;

import com.dianping.lion.client.util.JsonUtils;
import com.sankuai.walleops.cloud.triage.BaseTest;
import com.sankuai.walleops.cloud.triage.component.CommonEventMrmConsumer;
import com.sankuai.walleops.cloud.triage.controller.CollisionDetectionController;
import com.sankuai.walleops.cloud.triage.crane.CollisionHandleTimeoutCheckCrane;
import com.sankuai.walleops.cloud.triage.pojo.dto.PsEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.request.CommonDateTransferRequest;
import com.sankuai.walleops.cloud.triage.thrift.ThriftEventQueryService;
import com.sankuai.walleops.cloud.triage.thrift.vo.CollisionDetectionUpdateRequest;
import java.io.IOException;
import javax.annotation.Resource;
import org.junit.Test;

//@Ignore
public class CollisionDetectionControllerV2Test extends BaseTest {

    @Resource
    private CollisionDetectionController controllerV2;

    @Resource
    private CollisionHandleTimeoutCheckCrane checkCrane;

    @Resource
    private ThriftEventQueryService thriftEventQueryService;

    @Test
    public void controllerV2Test() throws Exception {
        CommonDateTransferRequest request = new CommonDateTransferRequest();

        String data = "{\"content\":{\"evaluation_alert\":106,\"accident_level\":1,\"latitude\":39.216339111328125,\"longitude\":118.96748352050781,\"record_name\":\"20231030_135139_s20-242\"},\"event_id\":\"20250319173953000_accident-detection_s20-242\",\"event_timestamp\":1742375609560,\"event_type\":1005,\"send_timestamp\":1742375609560,\"vehicle_name\":\"s20-173\",\"vin\":\"LMTZSV022NC017593\"}";
        request.setId("");
        request.setTimestamp(System.currentTimeMillis());
        request.setKey("accidentDetect");
        request.setData(data);

        controllerV2.getCollisionDetectionList(request);

    }

    @Test
    public void checkCollisionTimeoutUnHandleTest() throws Exception {
        checkCrane.checkCollisionTimeoutUnHandle();
    }

    @Test
    public void checkCollisionDetectionTimeoutUnCompletedTest() throws Exception {
        checkCrane.checkCollisionDetectionTimeoutUnCompleted();
    }

    @Test
    public void test() {
        thriftEventQueryService.queryRealtimeCollisionDetectionEvent("LA71AUB11R0515974");
    }


    @Test
    public void test2() {
        CollisionDetectionUpdateRequest request = new CollisionDetectionUpdateRequest();
        request.setEventId("20250319183103540_accident-detection_s20-242");
        request.setStatus(1);
        thriftEventQueryService.updateCollisionDetectionStatus(request);
    }

    @Resource
    private CommonEventMrmConsumer commonEventMrmConsumer;

    @Test
    public void test3() {
        String message = "{\"type\":100,\"body\":{\"eventType\":6,\"vin\":\"LMTZSV026NC007701\",\"requestList\":[{\"uid\":\"fccd395993cb348f8729a706c3b3cb81\",\"reasonCode\":6001,\"traceId\":\"s20-173_1750408021072\",\"requestTime\":1750418539000}],\"operateTime\":1750418538870},\"timestamp\":1750418538894}";
        commonEventMrmConsumer.consume(message, null);
    }

    @Resource
    private CollisionDetectionController collisionDetectionController;

    @Test
    public void test4() throws IOException {
        CommonDateTransferRequest dateTransferEqRequest = new CommonDateTransferRequest();
        PsEventDTO psEventDTO = new PsEventDTO();
        psEventDTO.setEventId("20240919183103540_accident-detection_s20-242");
        psEventDTO.setEventType(1005);
        psEventDTO.setVin("HDL401B87P1002292");
        psEventDTO.setEventTimestamp(System.currentTimeMillis());
        dateTransferEqRequest.setKey("accidentDetect");
        dateTransferEqRequest.setData(JsonUtils.toJson(psEventDTO));
        collisionDetectionController.handleCollisionDetection(dateTransferEqRequest);
    }
}
