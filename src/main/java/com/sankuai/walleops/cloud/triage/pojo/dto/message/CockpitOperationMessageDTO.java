package com.sankuai.walleops.cloud.triage.pojo.dto.message;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CockpitOperationMessageDTO {


    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 车架号，required
     */
    private String vin;

    /**
     * 请求列表，optional，主动连入场景下无
     */
    private List<CockpitRequestDetail> requestList;

    /**
     * 坐席类型，required
     */
    private Integer mrmRole;

    /**
     * 坐席编号，required
     */
    private String mrmSeatNo;

    /**
     * 坐席人员mis，required
     */
    private String mrmSeatMisId;

    /**
     * 坐席分配时间，required
     */
    private Long operateTime;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CockpitRequestDetail {

        /**
         * 请求原因，required
         */
        private Integer reasonCode;

        /**
         * 请求traceId，optional
         */
        private String traceId;

    }
}
