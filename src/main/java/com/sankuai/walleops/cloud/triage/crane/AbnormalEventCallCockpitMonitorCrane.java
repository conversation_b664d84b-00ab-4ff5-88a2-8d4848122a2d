package com.sankuai.walleops.cloud.triage.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleops.cloud.triage.adaptor.DXMessageAdaptor;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.NotificationMessageDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.config.AbnormalEventCallCockpitMonitorCraneConfig;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@CraneConfiguration
@Slf4j
public class AbnormalEventCallCockpitMonitorCrane {

    @Resource
    private DXMessageAdaptor dxMessageAdaptor;

    /**
     * 监控长时间未处置，以及长时间未完成的异常事件
     */
    @Resource
    private CloudTriageEventService cloudTriageEventService;

    /**
     * 云分诊车辆详情页链接
     */
    private static final String CSM_URL = "https://walle.sankuai.com/m/csm/event?vin=";

    @ConfigValue(key = "abnormal.event.call.cockpit.monitor.crane.config", defaultValue = "")
    private AbnormalEventCallCockpitMonitorCraneConfig craneConfig;

    @Crane("call.cockpit.monitor.crane")
    public void AbnormalEventCallCockpitMonitorCrane() throws Exception {
        log.info("AbnormalEventCallCockpitMonitorCrane start");
        //1. 检查半小时内所有需要云控处置的case
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        // 查询近场处置
        request.setOperatorType(EventOperatorTypeEnum.COCKPIT.getCode());
        Date startTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, 30);
        request.setStartTime(DatetimeUtil.formatTime(startTime));
        request.setEndTime(DatetimeUtil.formatTime(new Date()));
        List<BizCloudTriageEvent> eventList = cloudTriageEventService.commonQuery(request);
        if (CollectionUtils.isEmpty(eventList)) {
            log.info("AbnormalEventCallCockpitMonitorCrane end, eventList is empty");
            return;
        }
        // 2. 检查超时未处置的异常事件
        eventList.stream().forEach(cloudTriageEvent -> {
            EventStatusEnum statusEnum = EventStatusEnum.getByCode(cloudTriageEvent.getStatus());
            switch (statusEnum) {
                case COMPLETED:
                case CANCELED:
                    // 检查状态是否一致
                    break;
                case INITED:
                    long unHandleTimeGapInMinutes = TimeUnit.MILLISECONDS.toMinutes(
                            DatetimeUtil.diff(new Date(), cloudTriageEvent.getCreateTime()));
                    if (unHandleTimeGapInMinutes > craneConfig.getUnhandledTimeoutMins()) {

                        NotificationMessageDTO messageDTO = NotificationMessageDTO.builder()
                                .vin(cloudTriageEvent.getVin())
                                .vehicleName(cloudTriageEvent.getVehicleId())
                                .status(cloudTriageEvent.getStatus())
                                .eventType(cloudTriageEvent.getEventType())
                                .unUpdateTimeLen(unHandleTimeGapInMinutes)
                                .build();
                        // 向指定群发送消息
                        dxMessageAdaptor.sendDxMessage(craneConfig.getGid(), buildNotificationMessage(messageDTO));
                    }
                    break;
                case HANDLING:
                    long unCompleteTimeGapInMinutes = TimeUnit.MILLISECONDS.toMinutes(
                            DatetimeUtil.diff(new Date(), cloudTriageEvent.getCreateTime()));
                    if (unCompleteTimeGapInMinutes > craneConfig.getHandledTimeoutMins()) {
                        // 向指定群发送消息
                        NotificationMessageDTO messageDTO = NotificationMessageDTO.builder()
                                .vin(cloudTriageEvent.getVin())
                                .vehicleName(cloudTriageEvent.getVehicleId())
                                .status(cloudTriageEvent.getStatus())
                                .eventType(cloudTriageEvent.getEventType())
                                .unUpdateTimeLen(unCompleteTimeGapInMinutes)
                                .build();
                        dxMessageAdaptor.sendDxMessage(craneConfig.getGid(), buildNotificationMessage(messageDTO));
                    }
                    break;
                default:
                    break;
            }
        });

    }

    /**
     * 构建通知消息
     *
     * @param messageDTO
     * @return
     */
    private String buildNotificationMessage(NotificationMessageDTO messageDTO) {
        String eventTypeDesc = Objects.isNull(messageDTO.getEventType()) ? CommonConstant.UNKNOWN
                : EventTypeEnum.getByCode(messageDTO.getEventType()).getMsg();
        String statusDesc = Objects.isNull(messageDTO.getStatus()) ? CommonConstant.UNKNOWN
                : EventStatusEnum.getMsgByCode(messageDTO.getStatus());
        return String.format(
                "【呼叫坐席工单长时间未更新提示】%n" + "车辆号：%s / %s%n" + "事件类型：%s%n" + "工单状态：%s%n"
                        + "链接：[[跳转云分诊|%s%s]]%n"
                        + "未更新时长(分钟)：%s%n",
                messageDTO.getVehicleName(), messageDTO.getVehicleId(), eventTypeDesc, statusDesc
                , CSM_URL, messageDTO.getVin(), messageDTO.getUnUpdateTimeLen());
    }


}
