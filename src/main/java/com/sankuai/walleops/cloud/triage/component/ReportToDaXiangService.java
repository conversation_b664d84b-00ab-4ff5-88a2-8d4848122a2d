package com.sankuai.walleops.cloud.triage.component;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class ReportToDaXiangService {

    @MdpConfig("daXiangServiceHostname")
    String daXiangServiceHostname;

    private String path = "/eve/output/rest/push-message/uids";
    //大象通知到个人

    public void sendToReporter(String content, String recipient) {

        Map<String, Object> param = new HashMap<>();
        param.put("messageType", "text");
        param.put("extension", "");
        param.put("bodyJson", content );
        param.put("misList", Arrays.asList(recipient) );
        //通过 lion 获取域名
        String url = daXiangServiceHostname + path;
        try {
            String response = CommonUtil.doPost(url, param, null);
            log.info("发送大象消息, request: {}, response: {}", param, response);

            JSONObject responseJson = JSONObject.parseObject(response);
            Integer ret = responseJson.getInteger("rescode");
            if(!ret.equals(ResponseCodeEnum.SUCCESS.getCode())) {
                log.info("发送大象消息 failed, response: {}", response);
            }
        } catch (Exception e) {
            log.error("发送大象消息 error", e);
        }
    }


}
