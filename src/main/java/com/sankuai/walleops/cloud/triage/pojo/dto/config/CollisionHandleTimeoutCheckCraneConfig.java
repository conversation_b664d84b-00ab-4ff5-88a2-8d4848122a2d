package com.sankuai.walleops.cloud.triage.pojo.dto.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CollisionHandleTimeoutCheckCraneConfig {

    /**
     * 未处理超时时间（秒）
     */
    private Integer unhandledTimeoutSecS;
    /**
     * 处理超时时间（分钟）
     */
    private Integer handledTimeoutMinS;
    /**
     * 查询范围（分钟）
     */
    private Integer queryRangeMinS;

}
