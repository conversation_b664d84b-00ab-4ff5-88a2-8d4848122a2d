package com.sankuai.walleops.cloud.triage.mapper;

import com.sankuai.walleops.cloud.triage.pojo.entity.BizOperateHistory;
import org.apache.ibatis.annotations.Insert;
import tk.mybatis.mapper.common.Mapper;

/**
 * <AUTHOR>
 * @date 2022/9/19
 */
public interface BizOperateHistoryMapper extends Mapper<BizOperateHistory> {

    @Insert({
            "<script>",
            "insert into biz_operate_history",
            "<trim prefix='(' suffix=')' suffixOverrides=','>",
            "<if test='null != vin and &apos;&apos; != vin'>",
            "vin,",
            "</if>",
            "<if test='null != vehicleName and &apos;&apos; != vehicleName'>",
            "vehicle_name,",
            "</if>",
            "<if test='null != vehicleId and &apos;&apos; != vehicleId'>",
            "vehicle_id,",
            "</if>",
            "<if test='null != operateType'>",
            "operate_type,",
            "</if>",
            "<if test='null != description and &apos;&apos; != description'>",
            "description,",
            "</if>",
            "<if test='null != operateTime'>",
            "operate_time,",
            "</if>",
            "<if test='null != operator and &apos;&apos; != operator'>",
            "operator,",
            "</if>",
            "<if test='null != operateResponse and &apos;&apos; != operateResponse'>",
            "operate_response,",
            "</if>",
            "<if test='null != relatedEventId and &apos;&apos; != relatedEventId'>",
            "related_event_id,",
            "</if>",
            "<if test='null != isDeleted'>",
            "is_deleted,",
            "</if>",
            "<if test='null != createTime'>",
            "create_time,",
            "</if>",
            "<if test='null != updateTime'>",
            "update_time",
            "</if>",
            "</trim>",
            "<trim prefix='values (' suffix=')' suffixOverrides=','>",
            "<if test='null != vin and &apos;&apos; != vin'>",
            "#{vin},",
            "</if>",
            "<if test='null != vehicleName and &apos;&apos; != vehicleName'>",
            "#{vehicleName},",
            "</if>",
            "<if test='null != vehicleId and &apos;&apos; != vehicleId'>",
            "#{vehicleId},",
            "</if>",
            "<if test='null != operateType'>",
            "#{operateType},",
            "</if>",
            "<if test='null != description and &apos;&apos; != description'>",
            "#{description},",
            "</if>",
            "<if test='null != operateTime'>",
            "#{operateTime},",
            "</if>",
            "<if test='null != operator and &apos;&apos; != operator'>",
            "#{operator},",
            "</if>",
            "<if test='null != operateResponse and &apos;&apos; != operateResponse'>",
            "#{operateResponse},",
            "</if>",
            "<if test='null != relatedEventId and &apos;&apos; != relatedEventId'>",
            "#{relatedEventId},",
            "</if>",
            "<if test='null != isDeleted'>",
            "#{isDeleted},",
            "</if>",
            "<if test='null != createTime'>",
            "#{createTime},",
            "</if>",
            "<if test='null != updateTime'>",
            "#{updateTime}",
            "</if>",
            "</trim>",
            "</script>"
    })
    int insertOne(BizOperateHistory history);

}
