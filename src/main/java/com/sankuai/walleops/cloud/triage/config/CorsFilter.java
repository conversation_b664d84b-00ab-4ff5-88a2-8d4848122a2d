package com.sankuai.walleops.cloud.triage.config;

import com.google.common.net.HttpHeaders;
import com.sankuai.security.sdk.SecSdk;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;

/**
 * <AUTHOR>
 * @date 2021/08/26
 */
@Slf4j
@Component
@WebFilter
@Order(value = 1)
public class CorsFilter implements Filter {
    private static final String PARAMS_SEPARATE = ", ";

    @Value("${cors.access.control.maxAge:7200}")
    String corsAccessControlMaxAge;

    @Value("${environment}")
    String environment;

    String[] acceptedDomain = {"*.meituan.com", "*.sankuai.com", "*.zservey.com"};

    @Override
    public void init(FilterConfig filterConfig) {
        // no need to init, do nothing
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {

        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpServletResponse = ((HttpServletResponse) servletResponse);

        String origin = httpServletRequest.getHeader(HttpHeaders.ORIGIN);
        httpServletResponse.addHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, origin);
        httpServletResponse.addHeader("Access-Control-Allow-Credentials", "true");
        httpServletResponse.addHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, getHeaders(httpServletRequest));
        httpServletResponse.addHeader("Access-Control-Allow-Methods", "POST,GET,DELETE,PUT,OPTIONS");
        httpServletResponse.addHeader(HttpHeaders.ACCESS_CONTROL_MAX_AGE, corsAccessControlMaxAge);

        if (HttpMethod.OPTIONS.name().equals(httpServletRequest.getMethod())) {
            httpServletResponse.setStatus(HttpServletResponse.SC_OK);
            return;
        }

        if ("null".equals(origin)) {
            origin = null;
        }

        if ("prod".equals(environment) && !SecSdk.securityCORS(origin, acceptedDomain)) {
            return;
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    private String getHeaders(HttpServletRequest httpServletRequest) {
        StringBuilder params = new StringBuilder();
        String accessControlRequestHeaders =
                httpServletRequest.getHeader(HttpHeaders.ACCESS_CONTROL_REQUEST_HEADERS);
        if (!(accessControlRequestHeaders == null || accessControlRequestHeaders.isEmpty())) {
            params.append(accessControlRequestHeaders).append(PARAMS_SEPARATE);
        }

        Enumeration<String> names = httpServletRequest.getHeaderNames();
        while (names.hasMoreElements()) {
            params.append(names.nextElement()).append(PARAMS_SEPARATE);
        }
        params.setLength(params.length() - PARAMS_SEPARATE.length());
        return params.toString();
    }

    @Override
    public void destroy() {
        // no need of this method, do nothing
    }
}

