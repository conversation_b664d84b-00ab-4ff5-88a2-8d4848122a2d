package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum UacEventCodeEnum {
    UNKNOWN(-1, "unknown", "未知"),
    STAND_STILL(0, "standStill", "停滞不前"),
    ACCIDENT_DETECTION(1, "accidentDetection", "L2H事故检测"),
    VEHICLE_ACTIVATE_FAILURE(2, "vehicleActivateFailure", "信号不佳，移至门口发车"),
    VEHICLE_ACTIVATE_TIMEOUT(3, "vehicleActivateTimeout", "发车超时"),
    ARRIVAL_TIMEOUT(4, "arrivalTimeout", "到达超时"),
    VEHICLE_ACTIVATE_HOME(5, "vehicleActivate-HOME", "发车-HOME"),
    ACCIDENT(6, "accident", "事故"),
    VEHICLE_ACTIVATE_STATION(7, "vehicleActivate-STATION", "发车-停靠点"),
    AUTO_DRIVE_ABNORMAL_QUIT(8, "autoDriveAbnormalQuit", "恢复行驶"),
    ROUTE_DELIVERY_FAILURE(9, "routeDeliveryFailure", "路由下发失败"),
    ROUTE_GENERATION_FAILURE(10, "routeGenerationFailure", "路由生成失败"),
    START_AUTO_DRIVE_FAILURE(11, "startAutoDriveFailure", "启动自动驾驶失败"),
    DOCKING_TIMEOUT(12, "dockingTimeout", "停靠超时"),
    BATTERY_ALARM(14, "batteryAlarm", "电量告警"),
    BATTERY_REPLACE_STOP(16, "batteryReplaceStop", "电池需换电请停车"),
    ROAD_BLOCKAGE_MOVE_VEHICLE(17, "roadBlockageMoveVehicle", "堵路需挪车"),
    AUTO_PARKING_NOT_MEET_EXPECTATION(18, "autoParkingNotMeetExpectation", "自动泊车不符合预期"),
    PARKING_POSITION_NOT_REASONABLE_MOVE_VEHICLE(19, "parkingPositionNotReasonableMoveVehicle",
            "车辆停靠位置不合理需挪车"),
    RIDER_HOLD_GOODS_PARKING(20, "riderHoldGoodsParking", "骑手未取完货需停车"),
    OTHER(21, "other", "其他"),
    ROUTING_ISSUE(22, "routingIssue", "路由问题"),
    TRAFFICCONGESTION_ALERT(23, "trafficCongestionAlert", "堵路告警"),
    COLLISION_DETECTION_LOW(24, "collisionDetectionLow", "L2L事故检测"),
    COLLISION_DETECTION_HIGH(26, "collisionDetectionHigh", "L1事故检测"),
    ORDER_COMPLETED_RETURN(27, "orderCompletedReturn", "订单完成可以返回"),
    VEHICLE_NO_PARK_FOR_LONG_TIME(28, "vehicleNoParkForLongTime", "车辆长时间没有驻车"),
    CLOSE_CLEANING_SENSOR(29, "closeCleaningSensor", "关闭清洁传感器"),
    VIRTUAL_ORDER_DEPARTURE(30, "virtualOrderDeparture", "虚拟单发车"),
    ROAD_TEST_DEPARTURE(31, "roadTestDeparture", "路测发车"),
    PUBLIC_REMOVAL(32, "publicRemoval", "公众挪车"),
    RETROGRADE(40, "retrograde", "占用逆向车道停滞"),
    SPECIAL_AREA_STRANDING(41, "specialAreaStranding", "路口施工区域停滞"),
    DRIVE_ON_TRAFFIC_LINE(42, "driveOnTrafficLine", "非法压线停滞"),

    AUXILIARY_ROAD_TO_MAIN_ROAD(33, "auxiliaryRoadToMaiRoad", "辅路车借道主路"),
    MAIN_ROAD_TO_AUXILIARY_ROAD_RETURN(34, "mainRoadToAuxiliaryRoadReturn", "主路车误入辅路后返回"),
    RESUME_ROUTE_AFTER_RETURN(35, "resumeRouteAfterReturn", "掉头后恢复路由"),
    PULL_OVER_AND_EXIT_CONTROL(36, "pullOverAndExitControl", "靠边停车退控"),
    SCAN_CODE_TO_LIFT_BARRIER(37, "scanCodeToLiftBarrier", "扫码抬杆");

    private Integer code;
    private String name;
    private String description;

    public static Integer getCodeByName(String name) {
        for (UacEventCodeEnum uacEventCodeEnum : UacEventCodeEnum.values()) {
            if (uacEventCodeEnum.getName().equals(name)) {
                return uacEventCodeEnum.getCode();
            }
        }
        return -2;
    }

    public static String getDescriptionByCode(int code) {
        for (UacEventCodeEnum uacEventCodeEnum : UacEventCodeEnum.values()) {
            if (uacEventCodeEnum.getCode() == code) {
                return uacEventCodeEnum.getDescription();
            }
        }
       return "未知";
    }

}
