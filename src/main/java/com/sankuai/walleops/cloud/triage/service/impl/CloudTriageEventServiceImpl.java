package com.sankuai.walleops.cloud.triage.service.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.walleops.cloud.triage.component.LockService;
import com.sankuai.walleops.cloud.triage.component.OutputEventMsgProducer;
import com.sankuai.walleops.cloud.triage.component.ReportMoveCarEventProducer;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.MrmCalledStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.OrderOperationTypeEnum;
import com.sankuai.walleops.cloud.triage.mapper.BizCloudTriageEventMapper;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventDetailQueryDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventGroupQueryDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventGroupResultDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventMessageDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.LastEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.po.LastEventPO;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.CockpitHandleService;
import com.sankuai.walleops.cloud.triage.thrift.vo.EventDetailVO;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2022/8/24
 */
@Service
@Slf4j
public class CloudTriageEventServiceImpl implements CloudTriageEventService {
    @Resource
    private BizCloudTriageEventMapper bizCloudTriageEventMapper;

    @Resource
    private LockService lockService;

    @Resource
    private OutputEventMsgProducer outputEventMsgProducer;

    @Resource
    private ReportMoveCarEventProducer reportMoveCarEventProducer;

    @Resource
    private CockpitHandleService cockpitHandleService;

    /**
     * mq输出事件类型列表
     */
    @MdpConfig("output.mq.event.type.set")
    private HashSet<Integer> sendMQEventTypeSet;


    @MdpConfig("query.latest.collision.detection.time.length")
    public Integer queryTimeLength;

    @MdpConfig("moveCar.report.logic.migrate.gray")
    private HashSet<String> grayVehicleIdList;

    @Override
    public int save(BizCloudTriageEvent event) {
        boolean result = lockService.lock(CommonConstant.EVENT_INSERT_LOCK, event.getEventId(),
                CommonConstant.INSERT_LOCK_SECONDS, 5);
        if (!result) {
            log.error("save event lock failed, eventId: {}", event.getEventId());
            return 0;
        }
        // 在写入数据的同时，发送mafka消息
        // 兼容vhr = 1的车辆上报的碰撞检测事件，该事件会被直接取消
        EventStatusEnum statusEnum = EventStatusEnum.getByCode(event.getStatus());
        createAndSendMQ(event, Objects.isNull(statusEnum) ?
                EventStatusEnum.INITED : statusEnum, "");

        return bizCloudTriageEventMapper.insertOne(event);
    }

    /**
     * 异常事件状态变更检查和更新
     *
     * @param request
     */
    @Override
    public void statusCheckAndUpdate(CloudTriageEventUpdateRequest request) {
        String eventId = request.getEventId();
        // 1 状态判断 --  如果事件的状态已经是终态，则不需要进行状态更新
        BizCloudTriageEvent event =
                bizCloudTriageEventMapper.queryByUniqueKey(eventId, null);

        if (Objects.isNull(event)) {
            log.error(String.format("event is null, eventId = %s", eventId), new IllegalArgumentException());
            return;
        }

        if (EventStatusEnum.isCompleted(event.getStatus())) {
            log.info("event status is completed, eventId: {}", eventId);
            return;
        }
        // 2 加锁更新
        try {
            if (!lockService.lock(CommonConstant.EVENT_QU_LOCK, request.getEventId(),
                    CommonConstant.LOCK_EXPIRE_SECONDS)) {
                return;
            }
            // 上锁之后再加一次状态判断
            BizCloudTriageEvent temp =
                    bizCloudTriageEventMapper.queryByUniqueKey(eventId, null);
            if (Objects.isNull(temp) || EventStatusEnum.isCompleted(temp.getStatus())) {
                log.info("event status is completed, eventId: {}", eventId);
                return;
            }
            queryThenUpdate(request);
        } catch (Exception e) {
            log.error("queryThenUpdate error: {}", request, e);
        } finally {
            lockService.unLock(CommonConstant.EVENT_QU_LOCK, eventId);
        }
    }


    @Override
    public int update(CloudTriageEventUpdateRequest request) {
        if (request.getId() == null && StringUtils.isEmpty(request.getEventId())) {
            return 0;
        }
        String eventId = request.getEventId();
        if (StringUtils.isEmpty(eventId)) {
            BizCloudTriageEvent eventInDb =
                    bizCloudTriageEventMapper.queryByUniqueKey(null, request.getId());
            eventId = eventInDb.getEventId();
        }
        if (!lockService.lock(CommonConstant.EVENT_QU_LOCK, eventId, CommonConstant.LOCK_EXPIRE_SECONDS)) {
            return 0;
        }
        try {
            return queryThenUpdate(request);
        } catch (Exception e) {
            log.error("queryThenUpdate error: {}", request, e);
        } finally {
            lockService.unLock(CommonConstant.EVENT_QU_LOCK, eventId);
        }
        return 0;
    }

    public int queryThenUpdate(CloudTriageEventUpdateRequest request) {
        BizCloudTriageEvent eventInDb =
                bizCloudTriageEventMapper.queryByUniqueKey(request.getEventId(), request.getId());
        if (eventInDb == null) {
            return 0;
        }
        Integer currentStatus = eventInDb.getStatus();
        Date currentOperateStart = eventInDb.getOperateStartTime();
        Date currentOperateEnd = eventInDb.getOperateEndTime();
        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
        BeanUtils.copyProperties(request, cloudTriageEvent);
        Integer status = cloudTriageEvent.getStatus();
        Date operateStartTime = cloudTriageEvent.getOperateStartTime();

        if (EventStatusEnum.INITED.getCode().equals(currentStatus) &&
                EventStatusEnum.HANDLING.getCode().equals(status) &&
                currentOperateStart.before(CommonConstant.DEFAULT_EARLY_DATE) &&
                operateStartTime == null) {
            cloudTriageEvent.setOperateStartTime(new Date());
        }

        Date operateEndTime = cloudTriageEvent.getOperateEndTime();
        // 如果数据库中是未完成的且需要更新为完成，且数据库中的结束处理时间为空，且传入的也未空，则需要设置一下
        Date currentTime = new Date();
        if (currentStatus < EventStatusEnum.COMPLETED.getCode() &&
                EventStatusEnum.COMPLETED.getCode().equals(status) &&
                currentOperateEnd.before(CommonConstant.DEFAULT_EARLY_DATE) &&
                operateEndTime == null) {
            if (EventStatusEnum.INITED.getCode().equals(currentStatus)) {
                // 如果是从未处理直接变到处理完成，则需要设置一下处理开始时间
                cloudTriageEvent.setOperateStartTime(currentTime);
            }
            cloudTriageEvent.setOperateEndTime(currentTime);
        }
        if (currentStatus < EventStatusEnum.COMPLETED.getCode() &&
                (EventStatusEnum.COMPLETED.getCode().equals(status) ||
                        EventStatusEnum.CANCELED.getCode().equals(status))) {
            cloudTriageEvent.setRecoveryTime(currentTime);
        }

        // 判断事件是否为已完成状态 -- 且处于呼叫状态中
        if (EventStatusEnum.isCompleted(request.getStatus()) && Objects.equals(eventInDb.getMrmCalled(),
                MrmCalledStatusEnum.CALLING.getCode())) {
            log.info("cancel cloud operation, eventId: {}", eventInDb.getEventId());
            cancelCloudOperation(eventInDb);
            cloudTriageEvent.setMrmCalled(MrmCalledStatusEnum.CANCEL.getCode());

        }
        return bizCloudTriageEventMapper.updateOne(cloudTriageEvent);
    }

    @Override
    public int updateOnly(BizCloudTriageEvent cloudTriageEvent) {
        return bizCloudTriageEventMapper.updateOne(cloudTriageEvent);
    }

    @Override
    public int upsertAccident(BizCloudTriageEvent cloudTriageEvent) {
        return bizCloudTriageEventMapper.upsertAccident(cloudTriageEvent);
    }

    @Override
    public List<BizCloudTriageEvent> commonQuery(CloudTriageEventQueryPageRequest request) {
        return bizCloudTriageEventMapper.commonQuery(request);
    }

    @Override
    public List<BizCloudTriageEvent> pageQuery(CloudTriageEventQueryPageRequest request) {
        return bizCloudTriageEventMapper.pageQuery(request);
    }

    @Override
    public List<BizCloudTriageEvent> pageQueryGroup(CloudTriageEventQueryPageRequest request) {
        return bizCloudTriageEventMapper.pageQueryGroup(request);
    }

    @Override
    public int count(CloudTriageEventQueryPageRequest request) {
        return bizCloudTriageEventMapper.count(request);
    }

    @Override
    public int countGroup(CloudTriageEventQueryPageRequest request) {
        return bizCloudTriageEventMapper.countGroup(request);
    }

    @Override
    public Long getMaxId(CloudTriageEventQueryPageRequest request) {
        return bizCloudTriageEventMapper.getMaxId(request);
    }

    @Override
    public Long getMaxIdGroup(CloudTriageEventQueryPageRequest request) {
        return bizCloudTriageEventMapper.getMaxIdGroup(request);
    }

    @Override
    public List<EventGroupResultDTO> groupUnprocessedEvent(EventGroupQueryDTO request) {
        return bizCloudTriageEventMapper.groupUnprocessedEvent(request);
    }
    public BizCloudTriageEvent queryAllByUniqueKey(String eventId, Long id) {
        return bizCloudTriageEventMapper.queryAllByUniqueKey(eventId, id);
    }

    public List<BizCloudTriageEvent> queryEventTypeByIds(List<Long> ids) {
        return bizCloudTriageEventMapper.queryEventTypeByIds(ids);
    }

    public BizCloudTriageEvent queryByUniqueKey(String eventId, Long id) {
        return bizCloudTriageEventMapper.queryByUniqueKey(eventId, id);
    }

    public BizCloudTriageEvent queryByInformationId(String informationId) {
        return bizCloudTriageEventMapper.queryByInformationId(informationId);
    }

    public BizCloudTriageEvent queryEventDetailById(Long id) {
        return bizCloudTriageEventMapper.queryEventDetailById(id);
    }

    public List<LastEventDTO> queryLastEvent(List<String> vinList, Long startTime,
            Long endTime, Integer eventType, Integer operatorType) {
        String startTimeStr = DateUtil.format(new Date(startTime * 1000), DateUtil.YMD_HMS_SSS_UNSIGNED);
        String endTimeStr = DateUtil.format(new Date(endTime * 1000), DateUtil.YMD_HMS_SSS_UNSIGNED);
        List<LastEventPO> lastEventPOList =
                bizCloudTriageEventMapper.queryLastEvent(vinList, startTimeStr, endTimeStr, eventType, operatorType);

        if (CollectionUtils.isEmpty(lastEventPOList)) {
            return Collections.EMPTY_LIST;
        }

        Map<String, LastEventPO> lastEventMap = lastEventPOList.stream().collect(
                Collectors.groupingBy(LastEventPO::getVin,
                        Collectors.collectingAndThen(
                                Collectors.reducing(
                                        (a, b) -> a.getEventTime().compareTo(b.getEventTime()) >= 0 ? a : b),
                                Optional::get))
        );

        List<LastEventDTO> lastEventDTOList = new ArrayList<>();
        lastEventMap.values().stream().forEach(item -> {
            LastEventDTO lastEventDTO = new LastEventDTO();
            lastEventDTO.setEventId(item.getEventId());
            lastEventDTO.setEventType(item.getEventType());
            lastEventDTO.setEventTime(item.getEventTime().getTime() / 1000);
            lastEventDTO.setVin(item.getVin());
            lastEventDTO.setStatus(item.getStatus());
            lastEventDTOList.add(lastEventDTO);
        });

        return lastEventDTOList;
    }

    /**
     * 查询指定车辆在特定时间范围内的最新碰撞检测事件
     *
     * @param vin 车辆识别码
     * @return EventDetailVO 事件详情视图对象
     */
    public EventDetailVO queryLatestCollisionDetectionEvent(String vin) {
        // 获取碰撞检测类型列表
        List<Integer> eventTypeList = EventTypeEnum.getCollisionDetectionTypeList();

        // 设置查询时间范围，从当前时间往前推 queryTimeLength 分钟
        Date startTime = DateUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, queryTimeLength);
        Date endTime = new Date();

        // 设置查询条件
        EventDetailQueryDTO queryDTO = new EventDetailQueryDTO();
        queryDTO.setVin(vin);
        queryDTO.setEventTypeList(eventTypeList);
        queryDTO.setStartTime(DateUtil.format(startTime));
        queryDTO.setEndTime(DateUtil.format(endTime));


        // 查询符合条件的事件列表
        List<BizCloudTriageEvent> eventList = bizCloudTriageEventMapper.queryEvents(queryDTO);
        log.info("queryEvents, queryDTO = {}, eventList = {}", queryDTO, eventList);

        // 如果事件列表为空，返回 null
        if (CollectionUtils.isEmpty(eventList)) {
            return null;
        }
        // 将第一个事件转换为 EventDetailVO 对象并返回
        return convertToEventDetailVO(eventList.get(0));
    }

    @Override
    public BizCloudTriageEvent queryLatestEventDetailByUniqueKey(String eventId, Date minCreateTime) {
        //模糊查询
        return bizCloudTriageEventMapper.queryLatestEventDetailByUniqueKey(eventId, minCreateTime);
    }

    /**
     * 根据操作类型和更新时间范围查询事件列表
     *
     * @param operationType 操作类型
     * @param statusList 状态列表
     * @param maxCreateTime 最大更新时间
     * @param minCreateTime 最小更新时间
     * @return 符合条件的事件列表
     */
    @Override
    public List<BizCloudTriageEvent> queryByUpdateTimeRangeAndOperationType(Integer operationType,
            List<Integer> statusList, Date maxCreateTime, Date minCreateTime, Integer operatorType) {
        return bizCloudTriageEventMapper.queryByCreateTimeRangeAndOperationType(operationType, statusList,
                maxCreateTime, minCreateTime, operatorType);
    }

    /**
     * 根据操作类型、状态和操作开始时间范围查询事件列表
     *
     * @param operationType 操作类型
     * @param status 事件的状态
     * @param maxOperateStartTime 操作开始时间的最大值
     * @param minOperateStartTime 操作开始时间的最小值
     * @return 符合条件的事件列表
     */
    @Override
    public List<BizCloudTriageEvent> queryByOperateStartTimeRangeAndOperationType(Integer operationType, Integer status,
            Date maxOperateStartTime, Date minOperateStartTime, Integer operatorType) {
        return bizCloudTriageEventMapper.queryByOperateStartTimeRangeAndOperationType(operationType, status,
                maxOperateStartTime, minOperateStartTime, operatorType);
    }

    /**
     * 更新事件表且记录操作变更流水(无需事务，当状态更新成功之后记录流水即可)
     * @param cloudTriageEvent
     */
    @Override
    public Boolean updateBizCloudTriageEvent(BizCloudTriageEvent cloudTriageEvent) {
        if(Objects.isNull(cloudTriageEvent)){
            log.info("updateEventAndRecordLog# cloudTriageEvent is null");
            return false;
        }
        // 1 更新工单状态
        String eventId = cloudTriageEvent.getEventId();
        boolean lockAcquired = false;
        try {
            // 尝试加锁更新工单状态
            lockAcquired = lockService.lock(CommonConstant.EVENT_QU_LOCK, eventId, CommonConstant.LOCK_EXPIRE_SECONDS);
            if (!lockAcquired) {
                log.info("[updateBizCloudTriageEvent] 加锁失败， eventID:{}", eventId);
                return false;
            }

            int updatedItem = updateOnly(cloudTriageEvent);
            if (updatedItem != 0) {
                log.info("[updateBizCloudTriageEvent] event updated, cloudTriageEvent: {}", cloudTriageEvent);
                return true;
            }
        } catch (Exception e) {
            log.error("[updateBizCloudTriageEvent] event update failed, eventID: {}. ", eventId, e);
        } finally {
            // 只有在成功获取锁之后才尝试释放锁
            if (lockAcquired) {
                lockService.unLock(CommonConstant.EVENT_QU_LOCK, eventId);
            }
        }
        return false;
    }

    /**
     * 根据时间、vin、状态查询事件列表
     *
     * @param vin           车辆识别码
     * @param eventTypeList 事件类型列表
     * @param statusList    状态列表
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param operatorType  处置方
     * @return
     */
    @Override
    public List<BizCloudTriageEvent> queryEventsByTimeVinStatus(String vin, List<Integer> eventTypeList,

            List<Integer> statusList, String startTime, String endTime, Integer operatorType) {
        return bizCloudTriageEventMapper.queryEventsByTimeVinStatus(vin, eventTypeList, statusList, startTime, endTime,
                operatorType);
    }

    /**
     * 获取未处理和未完成的工单列表
     *
     * @param minCreateTime       工单创建时间的最小值
     * @param maxCreateTime       工单创建时间的最大值
     * @param minOperateStartTime 工单操作开始时间的最小值
     * @param maxOperateStartTime 工单操作开始时间的最大值
     * @return 未处理和未完成的工单列表
     */
    public List<BizCloudTriageEvent> getUnprocessedAndUncompletedEvents(Date minCreateTime, Date maxCreateTime,
            Date minOperateStartTime, Date maxOperateStartTime) {
        // 根据创建时间、未处理状态查询一段范围内未处理的工单
        List<BizCloudTriageEvent> unprocessedEventList = queryByUpdateTimeRangeAndOperationType(
                OrderOperationTypeEnum.AUTOMATIC_CALL_CLOUD_CONTROL.getType(),
                Arrays.asList(EventStatusEnum.INITED.getCode()),
                maxCreateTime,
                minCreateTime,
                EventOperatorTypeEnum.CLOUD_SECURITY.getCode());
        log.info("{} - {}, unprocessedEventList = {}", DateUtil.format(minCreateTime), DateUtil.format(maxCreateTime),
                unprocessedEventList);

        // 根据操作时间、处理中状态查询一段时间内未完成的工单
        List<BizCloudTriageEvent> unComplentedEventList = queryByOperateStartTimeRangeAndOperationType(
                OrderOperationTypeEnum.AUTOMATIC_CALL_CLOUD_CONTROL.getType(),
                EventStatusEnum.HANDLING.getCode(),
                maxOperateStartTime,
                minOperateStartTime,
                EventOperatorTypeEnum.CLOUD_SECURITY.getCode());
        log.info("{} - {}, uncompletedEventList = {}", DateUtil.format(minOperateStartTime),
                DateUtil.format(maxOperateStartTime),
                unComplentedEventList);

        List<BizCloudTriageEvent> result = new ArrayList<>();
        result.addAll(unprocessedEventList);
        result.addAll(unComplentedEventList);

        return result;
    }

    /**
     * 创建并发送消息到消息队列
     *
     * @param bizCloudTriageEvent 云调度事件对象
     * @param statusEnum 事件状态枚举
     * @param operator 操作人
     */
    public void createAndSendMQ(BizCloudTriageEvent bizCloudTriageEvent, EventStatusEnum statusEnum, String operator){

        // 1 对于非状态变更的更新操作进行过滤
        if(Objects.isNull(statusEnum)){
            log.info("sendEventMsgToMQ, statusEnum is null");
            return;
        }
        // 2 异常事件类型不能为空
        Integer eventType = bizCloudTriageEvent.getEventType();
        if (Objects.isNull(eventType)) {
            log.error("createAndSendMQ error, eventType is null");
            return;
        }
        // 3 根据指定的异常事件类型输出
        if (!CollectionUtils.isEmpty(sendMQEventTypeSet) && sendMQEventTypeSet.contains(eventType)) {
            sendEventMsgToMQ(bizCloudTriageEvent, statusEnum, operator);
        }

        // 4 输出扫码挪车事件到风控服务
        if (EventTypeEnum.PUBLIC_REMOVAL.getCode().equals(eventType)) {
            // TODO: 避免在灰度期间重复发送消息，这里需要使用车辆灰度列表进行拦截
            if (!isInGray(bizCloudTriageEvent.getVehicleId())) {
                reportMoveCarEventProducer.reportMoveCarEvent(bizCloudTriageEvent, statusEnum);
            }
        }
    }

    /**
     * 获取指定车辆在指定时间发生的碰撞检测事件的警报信息
     *
     * @param vin       车辆VIN码
     * @param eventTime 事件发生时间
     * @return 碰撞检测事件的警报信息，如果没有找到事件则返回null
     */
    @Override
    public String getCollisionDetectionAlert(String vin, Date eventTime) {

        // 获取碰撞检测类型列表
        List<Integer> eventTypeList = EventTypeEnum.getCollisionDetectionTypeList();

        // 设置查询时间范围
        Date startTime = DateUtil.getHeadOfToday(eventTime);
        Date endTime = DateUtil.getAfterTime(startTime, TimeUnit.DAYS, 1);
        // 设置查询条件
        EventDetailQueryDTO queryDTO = new EventDetailQueryDTO();
        queryDTO.setVin(vin);
        queryDTO.setEventTypeList(eventTypeList);
        queryDTO.setStartTime(DateUtil.format(startTime));
        queryDTO.setEndTime(DateUtil.format(endTime));
        queryDTO.setEventTime(DateUtil.format(eventTime));

        List<BizCloudTriageEvent> eventList = bizCloudTriageEventMapper.queryEvents(queryDTO);
        log.info("queryEvents, queryDTO = {}, eventList = {}", queryDTO, eventList);
        if (CollectionUtils.isEmpty(eventList)) {
            log.error(String.format("车辆 %s 查不到在 %s 发生的碰撞检测事件", vin, eventTime));
            return null;
        }
        // 解析并返回碰撞标签
        return parseAlert(eventList.get(0));
    }

    /**
     * 根据时间范围查询事件列表
     *
     * @param startTime
     * @param endTime
     * @param eventTypeList
     * @param operatorType
     * @return
     */
    @Override
    public List<BizCloudTriageEvent> queryByTimeRange(Date startTime, Date endTime, List<Integer> eventTypeList,
            Integer operatorType) {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            return new ArrayList<>();
        }
        List<BizCloudTriageEvent> eventList = bizCloudTriageEventMapper.queryByTimeRange(startTime, endTime,
                eventTypeList, operatorType);
        log.info("queryByTimeRange, startTime = {}, endTime = {}, eventList = {}", startTime, endTime, eventList);
        if (CollectionUtils.isEmpty(eventList)) {
            return new ArrayList<>();
        }
        return eventList;
    }

    /**
     * 发送事件消息到消息队列
     *
     * @param bizCloudTriageEvent 云调度事件对象
     * @param statusEnum          事件状态枚举
     * @param operator            操作人
     */
    private void sendEventMsgToMQ(BizCloudTriageEvent bizCloudTriageEvent, EventStatusEnum statusEnum,
            String operator) {
        // 构建事件消息DTO对象
        EventMessageDTO eventMessageDTO = new EventMessageDTO();
        eventMessageDTO.setEventId(bizCloudTriageEvent.getEventId());
        eventMessageDTO.setEventType(bizCloudTriageEvent.getEventType());
        eventMessageDTO.setTimestamp(new Date().getTime());
        eventMessageDTO.setVin(bizCloudTriageEvent.getVin());
        eventMessageDTO.setStatus(statusEnum.getCode());

        // 填充扩展字段
        eventMessageDTO.setContext(EventMessageDTO.contextDTO.builder()
                .statusDes(statusEnum.getMsg())
                .eventTypeDesc(EventTypeEnum.getByCode(bizCloudTriageEvent.getEventType()).getMsg())
                .operator(operator)
                .build());
        // 发送消息到消息队列
        outputEventMsgProducer.sendMessage(eventMessageDTO);
    }

    /**
     * 解析并返回事件中的碰撞检测标签
     *
     * @param event 异常事件实体
     * @return 碰撞检测标签字符串，如果事件为空或事件ID为空，则返回空字符串
     */
    private String parseAlert(BizCloudTriageEvent event) {
        if (Objects.isNull(event) || StringUtils.isBlank(event.getEventId())) {
            return null;
        }
        return JacksonUtils.getAsString(event.getRemark(), "alert");
    }


    /**
     * 将 BizCloudTriageEvent 对象转换为 EventDetailVO 对象
     *
     * @param cloudTriageEvent 云调度事件对象
     * @return 转换后的 EventDetailVO 对象，如果输入为空则返回 null
     */
    private EventDetailVO convertToEventDetailVO(BizCloudTriageEvent cloudTriageEvent) {
        // 检查输入对象是否为空
        if (Objects.isNull(cloudTriageEvent)) {
            return null;
        }

        // 创建 EventDetailVO 对象
        EventDetailVO eventDetailVO = new EventDetailVO();

        // 设置事件ID
        eventDetailVO.setEventId(cloudTriageEvent.getEventId());

        // 设置车辆ID
        eventDetailVO.setVehicleId(cloudTriageEvent.getVehicleId());

        // 设置事件状态
        eventDetailVO.setStatus(cloudTriageEvent.getStatus());

        // 设置事件状态描述
        eventDetailVO.setStatusDes(EventStatusEnum.getMsgByCode(cloudTriageEvent.getStatus()));

        // 设置事件类型
        eventDetailVO.setEventType(cloudTriageEvent.getEventType());
        eventDetailVO.setEventTypeDes(EventTypeEnum.getByCode(cloudTriageEvent.getEventType()).getMsg());

        // 设置车辆识别码
        eventDetailVO.setVin(cloudTriageEvent.getVin());

        // 将事件时间转换为UTC时间格式
        eventDetailVO.setEventTime(cloudTriageEvent.getEventTime().getTime());

        return eventDetailVO;
    }

    /**
     * 判断车辆是否在灰度范围内
     *
     * @param vehicleId
     * @return
     */
    private Boolean isInGray(String vehicleId) {
        // 1 检查是否打开灰度开关
        if (CollectionUtils.isEmpty(grayVehicleIdList) || StringUtils.isBlank(vehicleId)) {
            return false;
        }
        // 2 检查是否全量发布，或者异常事件类型命中灰度开关
        return (grayVehicleIdList.contains(CommonConstant.ALL_STRING)
                //必须要转大写
                || grayVehicleIdList.contains(vehicleId.toUpperCase()));
    }

    /**
     * 取消呼叫坐席
     *
     * @param bizCloudTriageEvent
     */
    private void cancelCloudOperation(BizCloudTriageEvent bizCloudTriageEvent) {
        try {
            // 根据异常事件类型决定取消呼叫
            if (EventTypeEnum.getCollisionDetectionTypeList().contains(bizCloudTriageEvent.getEventType())) {
                // 如果是碰撞检测，查询当前是否存在未检核的,且数量等于 1（因为当前事件还没更新）
                List<BizCloudTriageEvent> eventList = cockpitHandleService.queryUnCheckCollisionDetectionEventList(
                        bizCloudTriageEvent.getVin());
                if (CollectionUtils.isEmpty(eventList) || eventList.size() > 1) {
                    log.info("cancelCloudOperation,  UnCheckCollisionDetectionEventList > 1, = {}", eventList);
                    return;
                }
            }
            cockpitHandleService.requestCloudOperation(bizCloudTriageEvent.getVin(), bizCloudTriageEvent.getEventType(),
                    CommonConstant.CANCEL_CLOUD_OPERATION_ACTION);
        } catch (Exception e) {
            log.error("cancelCloudOperation error: {}", bizCloudTriageEvent, e);
        }

    }


}
