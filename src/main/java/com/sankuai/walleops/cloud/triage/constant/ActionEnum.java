package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ActionEnum {

    CREATE_ORDER(EventStatusEnum.INITED.getCode(),EventStatusEnum.INITED.getCode(),"创建工单"),
    CLOUD_OPERATION_CONNECTED(EventStatusEnum.INITED.getCode(), EventStatusEnum.HANDLING.getCode(), "云辅助/云代驾已连车"),
    CLOUD_OPERATION_EXIT(EventStatusEnum.HANDLING.getCode(), EventStatusEnum.COMPLETED.getCode(), "云辅助/云代驾已结束任务"),
    N_MINUTES_UNPROCESSED(EventStatusEnum.INITED.getCode(), EventStatusEnum.CANCELED.getCode(), "%s分钟内未查询到云控连车"),
    N_MINUTES_UNEDITED(EventStatusEnum.HANDLING.getCode(), EventStatusEnum.CANCELED.getCode(), "%s分钟内未查询到云控退出控车");

    private final Integer oldStatus;
    private final Integer newStatus;
    private final String action;

    /**
     * 根据新旧状态确定执行动作
     *
     * @param oldStatus
     * @param newStatus
     * @return
     */
    public static String determineAction(Integer oldStatus, Integer newStatus) {
        for (ActionEnum actionEnum : ActionEnum.values()) {
            if (actionEnum.getOldStatus() == oldStatus && actionEnum.getNewStatus() == newStatus) {
                return actionEnum.getAction();
            }
        }
        return CommonConstant.UNKNOWN;
    }
}
