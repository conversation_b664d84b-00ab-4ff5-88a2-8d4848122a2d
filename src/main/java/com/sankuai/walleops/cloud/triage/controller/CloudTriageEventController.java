package com.sankuai.walleops.cloud.triage.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.uac.sdk.entity.UacPermissionEntity;
import com.sankuai.meituan.uac.sdk.entity.page.UacPageInfo;
import com.sankuai.meituan.uac.sdk.entity.page.UacPageList;
import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.component.ReportToDaXiangService;
import com.sankuai.walleops.cloud.triage.component.pike.PikeServer;
import com.sankuai.walleops.cloud.triage.constant.CharConstant;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.GroupStrategyConfig;
import com.sankuai.walleops.cloud.triage.constant.GroupStrategyMap;
import com.sankuai.walleops.cloud.triage.constant.ManualReportEventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.constant.UacEventCodeEnum;
import com.sankuai.walleops.cloud.triage.constant.VehicleTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventGroupQueryDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventGroupResultDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.LastEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.OrderResponseDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.RemarkDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.RemoteResponseDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.StatusCheckResultDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.TripNodeResponseDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.EventUpdateRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.OperateHistoryRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.OrderRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.PageRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.RangeTimePageRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.TripNodeRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.VehicleStatusRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.pojo.response.PageResponse;
import com.sankuai.walleops.cloud.triage.pojo.vo.CloudTriageEventVO;
import com.sankuai.walleops.cloud.triage.pojo.vo.VehicleRealtimeStatusVO;
import com.sankuai.walleops.cloud.triage.service.AbnormalOrderReportService;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.OperateHistoryService;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import com.sankuai.walleops.cloud.triage.util.DepartureFailedUtil;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import com.sankuai.walleops.cloud.triage.util.RequestContext;
import com.sankuai.walleops.cloud.triage.util.exception.ParamInputErrorException;
import com.sankuai.walleops.cloud.triage.util.exception.RemoteErrorException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/8/23
 */
@RestController
@RequestMapping(value = "/event")
@Slf4j
public class CloudTriageEventController {

    // 组合过滤逻辑相关变量
    List<List<Integer>> index_List = GroupStrategyConfig.getCombineEventTypeList();
    HashMap<Integer, List<String>> eventType_purpose_map = GroupStrategyMap.getIndexPurposeMap();
    HashMap<String, Integer> purpose_index_map = GroupStrategyMap.getPurposeIndexMap();

    @MdpConfig("query.event.timeout.hour:24")
    private int queryEventTimeoutHour = 24;

    /**
     * uac获取用户权限列表的分页大小配置，最大不超过500
     */
    @MdpConfig("uac.page.info.pageSize")
    private Integer pageSize = 200;

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private InformationApiService informationApiService;

    @Resource
    private PikeServer pikeServer;

    @Autowired
    UacAuthRemoteService uacAuthRemoteService;

    @Resource
    private ReportToDaXiangService reportToDaXiang;

    @Resource
    private AbnormalOrderReportService abnormalOrderReportService;

    @Resource
    private OperateHistoryService operateHistoryService;

    // 测试车辆E10-01的vin为LK6CACE16NG600042
    @MdpConfig("filter.test.vehicle.list:[\"LK6CACE16NG600042\"]")
    private ArrayList<String> filterTestVinList;

    @MdpConfig("query_vin_list_max_size: 20")
    private Integer queryVinListMaxSize = 20;

    @MdpConfig("query_time_max_range: 86400")
    private Integer queryTimeMaxRange = 86400;

    @MdpConfig("is.filter.human.report.event.by.vhr")
    Boolean isFilterHumanReportEventByVHR = true;


    @MethodDoc(
            displayName = "人工上报异常工单入口",
            parameters = {
                    @ParamDoc(name = "vin", description = "车架号", type = String.class),
                    @ParamDoc(name = "eventType", description = "异常事件类型", type = Integer.class),
                    @ParamDoc(name = "isCheckVehicleStatus", description = "是否检查车辆状态", type = Boolean.class)},
            returnValueDescription = "请求响应结果",
            responseParams = {
                    @ParamDoc(name = "ret", description = "响应码", type = Integer.class, example = {"0"}),
                    @ParamDoc(name = "msg", description = "响应信息", example = {"ok"}),
                    @ParamDoc(name = "data", description = "null", typeName = "null")})
    @PostMapping(value = "/insert")
    public CommonResponse insert(@RequestBody CloudTriageEventUpdateRequest request) {
        log.info("insert CloudTriageEvent, request: {}", request);
        try {
            // 1 入参校验
            String vin = request.getVin();
            InputCheckUtil.isNotBlank(vin, "vin不能为空");
            InputCheckUtil.isNotNull(request.getEventType(), "异常事件类型不能为空");

            // 2 根据车辆vhr进行工单过滤
            VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = informationApiService.queryVehicleStatusByVin(vin);
            if (isFilterHumanReportEventByVHR && vehicleRealtimeStatusDTO != null
                    && vehicleRealtimeStatusDTO.getIsVhrMultiple() != null
                    && vehicleRealtimeStatusDTO.getIsVhrMultiple().equals(CommonConstant.ZERO)) {
                log.info("report data failed, isVhrMultiple = {}, CloudTriageEventUpdateRequest = {}",
                        vehicleRealtimeStatusDTO.getIsVhrMultiple(), request);
                return CommonResponse.failed("vhr = 1的车辆不允许上报异常");
            }
            // 3 校验车辆、云控状态
            if (request.getIsCheckVehicleStatus() != null && request.getIsCheckVehicleStatus()) {
                StatusCheckResultDTO statusCheckResultDTO = abnormalOrderReportService
                        .validateVehicleStatusAndGenerateResult(vin);
                if (statusCheckResultDTO.getResult().equals(false)) {
                    log.info("validateVehicleStatusAndGenerateResult# vin = {},statusCheckResultDTO = {}",
                            vin, statusCheckResultDTO);
                    return CommonResponse.reconfirm(statusCheckResultDTO.getNoticeTxt());
                }
            }

            // 4 工单信息入库前填充
            Integer result = abnormalOrderReportService.insertEventOrder(request, vehicleRealtimeStatusDTO);
            if (result > CommonConstant.ZERO) {
                // 通知客户端有新事件
                pikeServer.notifyNewEvent();
                return CommonResponse.success();
            }
        } catch (RemoteErrorException e){
            log.error("RemoteErrorException# error", e);
            return CommonResponse.failed(e.getMessage());
        } catch (ParamInputErrorException e) {
            log.error("ParamInputErrorException# error", e);
            return CommonResponse.failed(e.getMessage());
        } catch (Exception e) {
            log.error("systemException# error", e);
        }

        return CommonResponse.failed("系统错误");
    }

    @PostMapping(value = "/update")
    public CommonResponse update(@RequestBody CloudTriageEventUpdateRequest request) throws JsonProcessingException {
        log.info("update CloudTriageEvent, request: {}", request);
        if (StringUtils.isEmpty(request.getOperator())) {
            User user = UserUtils.getUser();
            request.setOperator(user != null ? user.getLogin() : null);
        }

        BizCloudTriageEvent result = cloudTriageEventService.queryAllByUniqueKey(request.getEventId(), request.getId());
        log.info("update queryByUniqueKey, result: {}", result);
        int update = cloudTriageEventService.update(request);
        log.info("update CloudTriageEvent, result: update={}", update);

        if (update > 0) {
            // 通知客户端有新事件
            pikeServer.notifyNewEvent();
            // 大象通知到上报人
            if (result != null && result.getReporter() != null) {
                // 获取车辆名
                String vehicleName = abnormalOrderReportService.getVehicleNameFromEventId(request.getEventId());

                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                String content = String.format(
                        "{\"text\":\"您的异常工单状态更新,  \n 车辆号：%s/%s, \n 事件类型：%s, \n 事件描述：%s, \n 工单状态: %s，\n 操作时间：%s，\n 操作人: %s\"}",
                        vehicleName, result.getVehicleId(), EventTypeEnum.getByCode(result.getEventType()).getMsg(),
                        "", EventStatusEnum.getMsgByCode(request.getStatus()), sdf.format(new Date()),
                        request.getOperator());

                reportToDaXiang.sendToReporter(content, result.getReporter());
            }

            // 创建mq并发送消息
            // 只对状态发生变化的更新操作进行输出，防止重复发送mq以及修改车辆历史异常工单状态对 当前车辆状态发生影响
            if (!Objects.isNull(result) && !Objects.isNull(request.getStatus())
                    && request.getStatus() != result.getStatus()) {
                cloudTriageEventService.createAndSendMQ(result, EventStatusEnum.getByCode(request.getStatus()),
                        result.getOperator());
            }

            return CommonResponse.success();
        }
        return CommonResponse.failed();
    }

    /**
     * 云安全事件列表查询 (云安全)
     *
     * @param request
     * @return
     */
    @GetMapping(value = "/list")
    public CommonResponse queryEvent(CloudTriageEventQueryPageRequest request) {
        log.info("/event/list, request: {}", request);
        // 1 检查查询的时间范围
        if (!checkStartEndTime(request)) {
            CommonResponse response = new CommonResponse();
            response.setRet(ResponseCodeEnum.BAD_REQUEST.getCode());
            response.setMsg("开始时间和结束时间之间超过两个自然日");
            return response;
        }
        // todo: 云安全指定接口,需要指定操作类型进行查询
        request.setOperatorType(EventOperatorTypeEnum.CLOUD_SECURITY.getCode());

        // 如果request中包含COREDUMP事件类型，增加AUTODRIVE_ABNORMAL_EXIT事件。
        extendCoredumpType(request);
        // 2 设置查询范围
        buildRangeTimeParam(request);

        // 判断用户是否具有权限
        boolean isPermisssion = false;
        // 3 判断是否是分诊值班角色，并进行组合逻辑过滤
        boolean[] isGroup = {false};
        try {
            isPermisssion = setRequestUac(request, isGroup);
        } catch (Exception e) {
            log.error("setRequestUac failure ,request is {}, error is {}", request, e);
        }
        if (!isPermisssion) {
            return new PageResponse();
        }
        log.info("/event/list, handled request : {}", request);

        // 取消数据库的分页查询，在查完之后手动进行分页
        Integer pageNum = request.getPage();
        Integer pageSize = request.getSize();
        request.setPage(null);
        request.setSize(null);

        List<BizCloudTriageEvent> eventList = new ArrayList<>();
        if (request.getGroupEventType() == null && request.getGroupPurpose() == null) {
            eventList = cloudTriageEventService.pageQuery(request);
        } else {
            eventList = cloudTriageEventService.pageQueryGroup(request);
        }
        if (CollectionUtils.isEmpty(eventList)) {
            log.info("/event/list, eventList is null");
            return new PageResponse();
        }
        // 仅针对分诊角色进行异常事件过滤
        if(isGroup[0]){
            eventList = abnormalOrderReportService.filterEvent(eventList);
        }

        List<CloudTriageEventVO> eventVOList = eventList.stream()
                .filter(item -> !filterTestVinList.contains(item.getVin())).map(item -> {
                    CloudTriageEventVO vo = new CloudTriageEventVO();
                    BeanUtils.copyProperties(item, vo);
                    // 如果不是已完成且不是已取消
                    if (item.getStatus() < EventStatusEnum.COMPLETED.getCode()) {
                        long durationMillis = System.currentTimeMillis() - item.getEventTime().getTime();
                        int secondToMillis = CommonConstant.MINUTE_TO_SECOND * CommonConstant.SECOND_TO_MILLIS;
                        long durationMinute = durationMillis % secondToMillis == 0 ? durationMillis / secondToMillis
                                : durationMillis / secondToMillis + 1;
                        vo.setDurationMinute(durationMinute);
                    }
                    vo.setEventDesc(EventTypeEnum.getByCode(item.getEventType()).getMsg());
                    if (StringUtils.isEmpty(vo.getVehicleName())) {
                        String[] eventIdParts = vo.getEventId().split(CharConstant.CHAR_XH);
                        vo.setVehicleName(eventIdParts[eventIdParts.length - 1]);
                    }
                    fillSolution(vo);

                    // 在solution字段的前面拼接站点信息，用中文逗号分隔。
                    String stationName = "";
                    // String eventDes = "";
                    JSONObject remarkJson = JSON.parseObject(item.getRemark());
                    if (remarkJson != null && item.getEventType() == EventTypeEnum.ARRIVE_TIMEOUT.getCode()) {
                        if (!StringUtils.isEmpty(remarkJson.getString("next_trip_node_name"))) {
                            stationName = remarkJson.getString("next_trip_node_name");
                        }
                    } else if (remarkJson != null) {
                        if (!StringUtils.isEmpty(remarkJson.getString("trip_node_name"))) {
                            stationName = remarkJson.getString("trip_node_name");
                        }
                    }

                    String solution = vo.getSolution();
                    if (StringUtils.isEmpty(solution) && !StringUtils.isEmpty(stationName)) {
                        vo.setSolution(stationName);
                    } else if (!StringUtils.isEmpty(solution) && !StringUtils.isEmpty(stationName)) {
                        vo.setSolution(stationName + "，" + solution);
                    }

                    // 将 AUTODRIVE_ABNORMAL_EXIT 转换为 COREDUMP 事件，返回给前端。
                    if (item.getEventType().equals(EventTypeEnum.AUTODRIVE_ABNORMAL_EXIT.getCode())
                            || item.getEventType().equals(EventTypeEnum.AUTODRIVE_PROCESS_ABNORMAL_EXIT.getCode())) {
                        vo.setEventType(EventTypeEnum.COREDUMP.getCode());
                    }
                    return vo;
                }).collect(Collectors.toList());

        List<CloudTriageEventVO> resEventVOList = new ArrayList<>();
        int count = 0;
        Long maxId = 0L;
        if(!eventVOList.isEmpty()){
            count = eventVOList.size();
            maxId = eventVOList.get(eventVOList.size() - 1).getId();
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, eventVOList.size());
            resEventVOList = eventVOList.subList(startIndex, endIndex);
        }
        PageResponse pageResponse = null;
        try {
            pageResponse = buildPageResponse(request, resEventVOList, count, maxId);
        } catch (Exception e) {
            log.error("generate pageResponse is failed");
        }
        log.info("[CloudTriageEventController#queryEvent] api /event/list, request: {}, response: {}", request,
                pageResponse);
        return pageResponse;
    }

    /**
     * 查询车辆状态信息列表(云安全)
     *
     * @param request
     * @return
     */
    @GetMapping(value = "/vehicle/status")
    public CommonResponse queryVehicleStatusInfoList(VehicleStatusRequest request) {
        log.info("/event/vehicle/status, request: {}", request);

        Map<String, Object> param = CommonUtil.beanToMap(request);
        // 默认只查中车 -> 查询全部车型
        param.put("vehicleType", VehicleTypeEnum.ALL_VEHICLE.getCode());

        // 查询车辆实时状态信息列表
        List<VehicleRealtimeStatusDTO> vehicleRealtimeStatusDTOS = informationApiService
                .callVehicleStatusService(param);
        if (vehicleRealtimeStatusDTOS == null) {
            return CommonResponse.success();
        }

        //  构建时间范围参数
        buildRangeTimeParam(request);
        EventGroupQueryDTO eventGroupQueryDTO = new EventGroupQueryDTO();
        BeanUtils.copyProperties(request, eventGroupQueryDTO);
        //  按 车辆VIN分组，查询并计算每个 VIN 对应的事件数量和最新事件 ID
        // todo: 云安全指定接口,需要指定操作类型进行查询
        eventGroupQueryDTO.setOperatorType(EventOperatorTypeEnum.CLOUD_SECURITY.getCode());
        List<EventGroupResultDTO> eventGroupResultDTOS = cloudTriageEventService
                .groupUnprocessedEvent(eventGroupQueryDTO);

        // TODO：流失计算中使用了过滤方法，但是实际上并没有进行过滤
        List<Long> ids = new ArrayList<>();
        // 构建车辆vin 与最新异常事件ID的映射关系
        Map<String, Integer> vinMapGroupResult = eventGroupResultDTOS.stream().filter(item -> {
            ids.add(item.getLatestId());
            return true;
        }).collect(Collectors.toMap(EventGroupResultDTO::getVin, EventGroupResultDTO::getEventCount));
        Map<String, Integer> vinMapLatestEventType = new HashMap<>();

        // 如果车辆存在最新的异常事件ID（两天内），则维护车辆与其最新异常事件类型之前映射关系
        if (!ids.isEmpty()) {
            try {
                List<BizCloudTriageEvent> eventList = cloudTriageEventService.queryEventTypeByIds(ids);
                vinMapLatestEventType.putAll(eventList.stream()
                        .collect(Collectors.toMap(BizCloudTriageEvent::getVin, BizCloudTriageEvent::getEventType)));
            } catch (Exception e) {
                log.info("queryEventTypeByIds is error, ids = {}", ids, e);
            }
        }

        List<VehicleRealtimeStatusVO> voList = vehicleRealtimeStatusDTOS.stream().map(item -> {
            // 将上文中查询到的车辆实时状态以及映射关系维护到接口输出中去
            VehicleRealtimeStatusVO vehicleRealtimeStatusVO = new VehicleRealtimeStatusVO();
            BeanUtils.copyProperties(item, vehicleRealtimeStatusVO);
            if (vinMapGroupResult.containsKey(item.getVin())) {
                vehicleRealtimeStatusVO.setEventCount(vinMapGroupResult.get(item.getVin()));
            }
            if (vinMapLatestEventType.containsKey(item.getVin())) {
                vehicleRealtimeStatusVO.setLatestEventType(vinMapLatestEventType.get(item.getVin()));
            }
            Long updateTimestamp = vehicleRealtimeStatusVO.getUpdateTimestamp();
            if (updateTimestamp != null) {
                vehicleRealtimeStatusVO.setUpdateTime(new Date(updateTimestamp));
            }
            vehicleRealtimeStatusVO.setPlace(item.getPark());
            vehicleRealtimeStatusVO.setPlaceDesc(item.getParkDesc());

            return vehicleRealtimeStatusVO;
        }).filter(item -> {
            // 根据查询参数中的用车目的和场地信息进行过滤
            String requestPurpose = request.getPurpose();
            String requestPlace = request.getPlace();
            if (StringUtils.isEmpty(requestPurpose) && StringUtils.isEmpty(requestPlace)) {
                return true;
            }
            String purpose = item.getPurpose();
            String place = item.getPlace();
            if (!StringUtils.isEmpty(requestPurpose) && !StringUtils.isEmpty(requestPlace)) {
                return requestPurpose.equals(purpose) && requestPlace.equals(place);
            }

            if (!StringUtils.isEmpty(requestPurpose)) {
                return requestPurpose.equals(purpose);
            }
            return requestPlace.equals(place);
        }).sorted((a, b) -> {
            //首先根据最新事件类型排序，将碰撞检测事件（COLLISION_DETECTION）优先。
            //然后根据事件数量降序排序。
            //如果事件数量相同或为空，则保持原有顺序。
            if (!EventTypeEnum.COLLISION_DETECTION.getCode().equals(a.getLatestEventType())
                    && EventTypeEnum.COLLISION_DETECTION.getCode().equals(b.getLatestEventType())) {
                return 1;
            } else if (EventTypeEnum.COLLISION_DETECTION.getCode().equals(a.getLatestEventType())
                    && !EventTypeEnum.COLLISION_DETECTION.getCode().equals(b.getLatestEventType())) {
                return -1;
            }
            if (a.getEventCount() == null && b.getEventCount() != null) {
                return 1;
            } else if (a.getEventCount() != null && b.getEventCount() == null) {
                return -1;
            }
            if (a.getEventCount() != null && b.getEventCount() != null) {
                return b.getEventCount().compareTo(a.getEventCount());
            }
            return 0;
        }).collect(Collectors.toList());

        CommonResponse response = CommonResponse.success(voList);

        return response;
    }

    @GetMapping(value = "/trip_node/list")
    public PageResponse queryTripNodeList(TripNodeRequest request) {
        log.info("/event/trip_node/list, request: {}", request);

        buildPageParam(request);
        Map<String, Object> param = CommonUtil.beanToMap(request);
        setUserRelativeInfo(param);
        TripNodeResponseDTO tripNodeResponseDTO = informationApiService.callTripNodeList(param);
        if (tripNodeResponseDTO == null) {
            return PageResponse.failed();
        }

        PageResponse response = buildRemotePageResponse(tripNodeResponseDTO, tripNodeResponseDTO.getData(),
                request.getPage(), request.getSize());
        log.info("/event/trip_node/list, response: {}", response);

        return response;
    }

    @GetMapping("/order/list")
    public PageResponse queryOrderList(OrderRequest request) {
        log.info("/event/order/list, request: {}", request);
        buildPageParam(request);
        Map<String, Object> param = CommonUtil.beanToMap(request);
        setUserRelativeInfo(param);
        OrderResponseDTO orderResponseDTO = informationApiService.callOrderList(param);

        if (orderResponseDTO == null) {
            log.info("/event/order/list, response: failed");
            return PageResponse.failed();
        }

        PageResponse response = buildRemotePageResponse(orderResponseDTO, orderResponseDTO.getData(), request.getPage(),
                request.getSize());

        log.info("/event/order/list, response: {}", response);

        return response;
    }


    @GetMapping("/detail")
    public CommonResponse queryEventDetail(@RequestParam("id") Long id) {
        if (id == null || id == 0) {
            return CommonResponse.failed();
        }

        BizCloudTriageEvent eventDd = cloudTriageEventService.queryEventDetailById(id);

        CloudTriageEventVO vo = new CloudTriageEventVO();
        BeanUtils.copyProperties(eventDd, vo);
        // 如果不是已完成且不是已取消
        if (eventDd.getStatus() < EventStatusEnum.COMPLETED.getCode()) {
            long durationMillis = System.currentTimeMillis() - eventDd.getEventTime().getTime();
            int secondToMillis = CommonConstant.MINUTE_TO_SECOND * CommonConstant.SECOND_TO_MILLIS;
            long durationMinute = durationMillis % secondToMillis == 0 ? durationMillis / secondToMillis
                    : durationMillis / secondToMillis + 1;
            vo.setDurationMinute(durationMinute);
        }

        vo.setEventDesc(EventTypeEnum.getByCode(eventDd.getEventType()).getMsg());

        if (StringUtils.isEmpty(vo.getVehicleName())) {
            String[] eventIdParts = vo.getEventId().split(CharConstant.CHAR_XH);
            vo.setVehicleName(eventIdParts[eventIdParts.length - 1]);
        }
        fillSolution(vo);

        // 在solution字段的前面拼接站点信息，用中文逗号分隔。
        String stationName = "";
        String eventDes = "";
        JSONObject remarkJson = JSON.parseObject(eventDd.getRemark());
        if (remarkJson != null && eventDd.getEventType() == EventTypeEnum.ARRIVE_TIMEOUT.getCode()) {
            if (!StringUtils.isEmpty(remarkJson.getString("next_trip_node_name"))) {
                stationName = remarkJson.getString("next_trip_node_name");
            }
        } else if (remarkJson != null) {
            if (!StringUtils.isEmpty(remarkJson.getString("trip_node_name"))) {
                stationName = remarkJson.getString("trip_node_name");
            }
        }

        if (remarkJson != null && !StringUtils.isEmpty(remarkJson.getString("eventDes"))) {
            eventDes = remarkJson.getString("eventDes");
        }

        String solution = vo.getSolution();
        if (StringUtils.isEmpty(solution) && !StringUtils.isEmpty(stationName)) {
            vo.setSolution(stationName);
        } else if (!StringUtils.isEmpty(solution) && !StringUtils.isEmpty(stationName)) {
            vo.setSolution(stationName + "，" + solution);
        }

        solution = vo.getSolution();
        if (StringUtils.isEmpty(solution) && !StringUtils.isEmpty(eventDes)) {
            vo.setSolution(eventDes);
        } else if (!StringUtils.isEmpty(solution) && !StringUtils.isEmpty(eventDes)) {
            vo.setSolution(solution + "， " + eventDes);
        }

        // 将 AUTODRIVE_ABNORMAL_EXIT 转换为 COREDUMP 事件，返回给前端。
        if (eventDd.getEventType().equals(EventTypeEnum.AUTODRIVE_ABNORMAL_EXIT.getCode())) {
            vo.setEventType(EventTypeEnum.COREDUMP.getCode());
        }

        CommonResponse response = new CommonResponse();
        response.setRet(ResponseCodeEnum.SUCCESS.getCode());
        response.setMsg("成功");
        response.setData(Arrays.asList(vo));

        return response;
    }

    /**
     * 查询最近事件(云安全使用)
     *
     * @param vinList
     * @param startTime
     * @param endTime
     * @param eventType
     * @return
     */
    @GetMapping("/last")
    public CommonResponse queryLastEvent(@RequestParam("vinList") List<String> vinList,
            @RequestParam("startTime") Long startTime, @RequestParam("endTime") Long endTime,
            @RequestParam("eventType") Integer eventType) {
        log.info("/event/last, request parameters, vinList: {}, startTime: {}, endTime: {}, eventType: {}", vinList,
                startTime, endTime, eventType);

        if (CollectionUtils.isEmpty(vinList) || vinList.size() > queryVinListMaxSize) {
            log.info("/event/last, vinList is empty or size>20, vinList: {}", vinList);
            return CommonResponse.failed("vinList is empty or size>20");
        }

        if (startTime == null) {
            log.info("/event/last, startTime is null");
            return CommonResponse.failed("startTime is null");
        }

        if (endTime == null) {
            log.info("/event/last, endTime is null");
            return CommonResponse.failed("endTime is null");
        }

        if (eventType == null) {
            log.info("/event/last, eventType is null");
            return CommonResponse.failed("eventType is null");
        }

        if (endTime - startTime > queryTimeMaxRange) {
            log.info("/event/list, endTime - startTime > 86400s");
            return CommonResponse.failed("endTime - startTime > 86400s");
        }
        // todo: 云安全指定接口,需要指定操作类型进行查询
        List<LastEventDTO> lastEventList = cloudTriageEventService.queryLastEvent(vinList, startTime, endTime,
                eventType, EventOperatorTypeEnum.CLOUD_SECURITY.getCode());
        log.info("/event/last, response: {}", lastEventList);

        return CommonResponse.success(lastEventList);
    }

    @MethodDoc(displayName = "查询人工上报可见的异常事件列表", description = "查询人工上报可见的异常事件列表", parameters = {
            // 入参为空
    }, returnValueDescription = "请求响应结果",
            responseParams = {@ParamDoc(name = "ret", description = "响应码", type = Integer.class, example = {"0"}),
                    @ParamDoc(name = "msg", description = "响应信息", example = {"ok"}),
                    @ParamDoc(name = "data", description = "人工可见的异常事件列表", typeName = "Map<Integer, String>")}
    )
    @GetMapping("/getEventReportList")
    public CommonResponse getEventReportList() {
        Map<Integer, String> enumMap = Arrays.stream(ManualReportEventTypeEnum.values())
                .collect(Collectors.toMap(ManualReportEventTypeEnum::getCode, ManualReportEventTypeEnum::getMsg,
                        (oldValue, newValue) -> oldValue, TreeMap::new));
        return CommonResponse.success(enumMap);
    }


    @MethodDoc(displayName = "更新异常事件状态", description = "提供给安全中心使用的状态更新接口", parameters = {
            // 入参为空
    }, returnValueDescription = "请求响应结果",
            responseParams = {@ParamDoc(name = "ret", description = "响应码", type = Integer.class, example = {"0"}),
                    @ParamDoc(name = "msg", description = "响应信息", example = {"ok"}),
                    @ParamDoc(name = "data", description = "人工可见的异常事件列表", typeName = "Map<Integer, String>")}
    )
    @PostMapping("/eventStatus/update")
    public CommonResponse updateEventStatus(@RequestBody EventUpdateRequest request) {
        try {
            // 1 参数校验
            InputCheckUtil.isNotBlank(request.getEventId(), "事件ID不能为空");
            InputCheckUtil.isNotBlank(request.getOperator(), "操作人不能为空");
            InputCheckUtil.isNotNull(request.getStatus(), "事件状态不能为空");

            // 2 检查当前异常事件是否存在
            BizCloudTriageEvent result = cloudTriageEventService.queryAllByUniqueKey(request.getEventId(), null);
            InputCheckUtil.isNotNull(result, "事件不存在");

            log.info("update queryByUniqueKey, result: {}", result);

            // 3 更新事件数据
            CloudTriageEventUpdateRequest updateRequest = new CloudTriageEventUpdateRequest();
            updateRequest.setEventId(request.getEventId());
            updateRequest.setStatus(request.getStatus());
            updateRequest.setOperator(request.getOperator());
            int update = cloudTriageEventService.update(updateRequest);
            log.info("update CloudTriageEvent, result: update={}", update);
            if (update <= 0) {
                return CommonResponse.failed("更新失败");
            }
            // 添加操作日志
            addOperateHistory(request, result);
            // 通知客户端有新事件
            pikeServer.notifyNewEvent();

            // 创建mq并发送消息
            // 只对状态发生变化的更新操作进行输出，防止重复发送mq以及修改车辆历史异常工单状态对 当前车辆状态发生影响
            if (!Objects.isNull(result) && !Objects.isNull(request.getStatus())
                    && request.getStatus() != result.getStatus()) {
                cloudTriageEventService.createAndSendMQ(result, EventStatusEnum.getByCode(request.getStatus()),
                        result.getOperator());
            }
            return CommonResponse.success();
        } catch (ParamInputErrorException e) {
            return CommonResponse.failed(e.getMessage());
        } catch (Exception e) {
            log.error("updateEvent error", e);
            return CommonResponse.failed("system error");
        }
    }

    /**
     * 添加操作记录
     *
     * @param request
     * @param result
     */
    private void addOperateHistory(EventUpdateRequest request, BizCloudTriageEvent result) {
        OperateHistoryRequest historyRequest = new OperateHistoryRequest();
        historyRequest.setOperator(request.getOperator());
        historyRequest.setRelatedEventId(request.getEventId());
        historyRequest.setOperateType(CommonConstant.OPERATION_LOG_UPDATE);
        historyRequest.setVin(result.getVin());
        String description =
                EventStatusEnum.getMsgByCode(result.getStatus()) + " -> " + EventStatusEnum.getMsgByCode(
                        request.getStatus());
        historyRequest.setDescription(description);
        String eventId = result.getEventId();
        historyRequest.setVehicleName(eventId.split("_")[eventId.split("_").length - 1]);
        historyRequest.setVehicleId(result.getVehicleId());
        historyRequest.setOperateTime(new Date());
        try {
            operateHistoryService.addOperateHistory(historyRequest);
        } catch (Exception e) {
            log.error("addOperateHistory error", e);
        }

    }


    public PageResponse buildRemotePageResponse(RemoteResponseDTO remoteResponseDTO, List<?> data, int page, int size) {
        PageResponse response = PageResponse.success();
        response.setTotalPage(remoteResponseDTO.getTotalPage());
        response.setTotalSize(remoteResponseDTO.getTotalSize());
        response.setData(data);
        response.setPage(page);
        response.setSize(size);
        return response;
    }


    private boolean setRequestUac(CloudTriageEventQueryPageRequest request, boolean[] isGroup) {
        User user = null;
        try {
            user = UserUtils.getUser();
        } catch (Exception e) {
            log.error("getUser is failure", e);
            return false;
        }

        List<Integer> userPermissionList = new ArrayList<>();
        try {
            // 设置分页信息
            UacPageInfo uacPageInfo = new UacPageInfo();
            uacPageInfo.setPageSize(pageSize);
            UacPageList<UacPermissionEntity> uacPageList = uacAuthRemoteService
                    .getUserPermissions(String.valueOf(user.getId()), uacPageInfo);
            for (UacPermissionEntity entity : uacPageList.getPageList()) {
                // 检查该角色是否需要进行分组
                if (Objects.equals(entity.getCode(), "group")) {
                    isGroup[0] = true;
                } else {
                    Integer temp = UacEventCodeEnum.getCodeByName(entity.getCode());
                    // 根据 UacEventCodeEnum 定义，-2 为不存在的 code
                    if (temp != -2) {
                        userPermissionList.add(temp);
                    }
                }
            }
        } catch (Exception e) {
            log.error("permission/query, getUserPermissions is failed, user is {} , uid = {}", user.getLogin(),
                    user.getId(), e);
        }
        if (userPermissionList.isEmpty()) {
            log.info("[CloudTriageEventController#queryEvent] api /event/list, user : {} is not permission",
                    user.getLogin());
            return false;
        }
        List<String> temp_group1_purpose = new ArrayList<String>();
        List<String> temp_group2_purpose = new ArrayList<String>();
        List<Integer> temp_group1_eventType = new ArrayList<Integer>();
        List<Integer> temp_group2_eventType = new ArrayList<Integer>();
        // 需要分组的角色
        if (isGroup[0]) {
            log.info("user : {} is need to make a difference", user.getLogin());
            if (request.getPurpose() != null && request.getEventType() != null) {
                /*
                 * 当两个纬度都存在时，会出现三种情况
                 * 1）存在两个组合
                 * 2）存在一个组合
                 * 3）不存在组合
                 */
            } else if (request.getPurpose() != null && request.getPurpose().size() > 0) {
                /*
                 * 条件查询（只有目的） ——》 需要判断是否所有的目的属于一个组合
                 * 遍历条件，将目的分成两组，每组添加目的对应的 事件类型
                 */
                for (String s : request.getPurpose()) {
                    // 获取目的对应的 事件列表编号
                    if (!purpose_index_map.containsKey(s)) {
                        log.info("purpose_index_map {} is not exist ! ", s);
                        continue;
                    }
                    Integer index = purpose_index_map.get(s);
                    if (index == 0) {
                        temp_group1_purpose.add(s);
                        for (Integer i : index_List.get(index)) {
                            temp_group1_eventType.add(i);
                        }
                    } else {
                        temp_group2_purpose.add(s);
                        for (Integer i : index_List.get(index)) {
                            temp_group2_eventType.add(i);
                        }
                    }

                }
                /*
                 * 当两个用车目的列表全部存在时，说明此时有两个组合
                 */
                if (temp_group2_purpose.size() != 0 && temp_group1_purpose.size() != 0) {
                    request.setGroupPurpose(temp_group2_purpose);
                    request.setGroupEventType(temp_group2_eventType);
                    request.setPurpose(temp_group1_purpose);
                    request.setEventType(temp_group1_eventType);
                } else {
                    /*
                     * 否则只有一个组合存在，那么此时只需要补充该目的对应的事件类型即可
                     */
                    if (temp_group1_purpose.size() != 0) {
                        request.setEventType(temp_group1_eventType);
                    } else {
                        request.setEventType(temp_group2_eventType);
                    }

                }
            } else if (request.getEventType() != null && request.getEventType().size() > 0) {
                /*
                 * 条件查询 (只有事件类型) -> 需要判断是否所有的事件类型属于一个组合
                 */
                for (Integer i : request.getEventType()) {
                    if (!eventType_purpose_map.containsKey(i)) {
                        log.info("eventType_purpose_map {} is not exist ! ", i);
                        continue;
                    }
                    // 遍历事件类型，并获取每个事件类型对应的目的列表
                    for (String s : eventType_purpose_map.get(i)) {
                        // 遍历目的列表，根据目的确定分组
                        if (!purpose_index_map.containsKey(s)) {
                            log.info("purpose_index_map {} is not exist ! ", s);
                            continue;
                        }
                        Integer index = purpose_index_map.get(s);
                        if (index == 0) {
                            temp_group1_purpose.add(s);
                            temp_group1_eventType.add(i);
                        } else {
                            temp_group2_purpose.add(s);
                            temp_group2_eventType.add(i);
                        }

                    }
                }

                if (temp_group1_eventType.size() != 0 && temp_group2_eventType.size() != 0) {
                    request.setGroupPurpose(temp_group2_purpose);
                    request.setGroupEventType(temp_group2_eventType);
                    request.setPurpose(temp_group1_purpose);
                    request.setEventType(temp_group1_eventType);
                } else {
                    if (temp_group1_purpose.size() != 0) {
                        request.setPurpose(temp_group1_purpose);
                    } else {
                        request.setPurpose(temp_group2_purpose);
                    }
                }

            } else {
                request.setEventType(GroupStrategyConfig.GROUP1_EVENTTYPE);
                request.setPurpose(GroupStrategyConfig.GROUP1_PURPOSE);
                request.setGroupEventType(GroupStrategyConfig.GROUP2_EVENT_TYPE);
                request.setGroupPurpose(GroupStrategyConfig.GROUP2_PURPOSE);
            }

        } else {   // 对应不需要分组的角色
            if (request.getEventType() == null) {
                request.setEventType(userPermissionList);
            }
        }

        return true;
    }

    /**
     * 校验开始时间和结束时间之间相差是否大于2个自然日， 如果小于等于2个自然日，校验通过。 如果大于2个自然日，返回失败。
     */
    private boolean checkStartEndTime(CloudTriageEventQueryPageRequest request) {
        // 当未设置startTime/endTime，会对startTime/endTime设置默认值，因此此处校验通过。
        if (StringUtils.isEmpty(request.getStartTime()) || StringUtils.isEmpty(request.getEndTime())) {
            return true;
        }

        // startTime/endTime的格式为：YYYY-MM-DD HH:MM:SS
        long startTimeMs = DateUtil.parseDate(request.getStartTime()).getTime();
        long endTimeMs = DateUtil.parseDate(request.getEndTime()).getTime();

        long twoDaysMs = 2 * 24 * 60 * 60 * 1000;
        if (endTimeMs - startTimeMs > twoDaysMs) {
            return false;
        }

        return true;
    }

    /**
     * 如果request中包含COREDUMP事件类型，增加AUTODRIVE_ABNORMAL_EXIT事件。
     */
    private void extendCoredumpType(CloudTriageEventQueryPageRequest request) {
        List<Integer> eventTypeList = request.getEventType();
        if (!CollectionUtils.isEmpty(eventTypeList) && eventTypeList.contains(EventTypeEnum.COREDUMP.getCode())) {
            eventTypeList.add(EventTypeEnum.AUTODRIVE_ABNORMAL_EXIT.getCode());
            eventTypeList.add(EventTypeEnum.AUTODRIVE_PROCESS_ABNORMAL_EXIT.getCode());
        }
    }

    private void fillSolution(CloudTriageEventVO vo) {
        String remark = vo.getRemark();
        if (!StringUtils.isEmpty(remark)) {
            JSONObject jsonObject = JSON.parseObject(remark);
            if (jsonObject.containsKey("eventDes")) {
                String eventDes = jsonObject.get("eventDes").toString();
                vo.setSolution(eventDes);
            } else if (jsonObject.containsKey("code")) {
                String code = jsonObject.get("code").toString();
                String msg = jsonObject.containsKey("message") ? jsonObject.getString("message") : "";
                String solution = code + CharConstant.CHAR_CH_DD
                        + DepartureFailedUtil.getSolutionByCodeAndMsg(code, msg);
                vo.setSolution(solution);
            } else if (jsonObject.containsKey("power_warn_desc")
                    && vo.getEventType().equals(EventTypeEnum.POWER_WARNING.getCode())) {
                String powerWarnDesc = jsonObject.getString("power_warn_desc");
                if (powerWarnDesc.contains("67")) {
                    powerWarnDesc = "电量67%，无订单下发换电路由，有订单忽略异常";
                } else if (powerWarnDesc.contains("40")) {
                    powerWarnDesc = "电量40%，无订单下发换电路由，有订单呼叫云控停车等待救援";
                }

                vo.setSolution(powerWarnDesc);
            } else if (jsonObject.containsKey("reportSourceDesc")
                    && vo.getEventType().equals(EventTypeEnum.ACCIDENT.getCode())) {
                String reportSource = jsonObject.getString("reportSourceDesc");
                vo.setSolution(reportSource);
            }
        }

        String solutionDesc = vo.getSolution();
        if (vo.getEventType().equals(EventTypeEnum.COREDUMP.getCode())) {
            solutionDesc = StringUtils.isEmpty(solutionDesc) ? "启动onboard+启动自动驾驶"
                    : "启动onboard+启动自动驾驶" + "，" + solutionDesc;
        } else if (vo.getEventType().equals(EventTypeEnum.AUTODRIVE_ABNORMAL_EXIT.getCode())) {
            solutionDesc = StringUtils.isEmpty(solutionDesc) ? "启动自动驾驶" : "启动自动驾驶" + "，" + solutionDesc;
        } else if (vo.getEventType().equals(EventTypeEnum.AUTODRIVE_PROCESS_ABNORMAL_EXIT.getCode())) {
            solutionDesc = StringUtils.isEmpty(solutionDesc) ? "启动onboard+启动自动驾驶"
                    : "启动onboard+启动自动驾驶" + "，" + solutionDesc;
        } else if (vo.getEventType().equals(EventTypeEnum.PUBLIC_REMOVAL.getCode())) {
            RemarkDTO remarkDTO = JacksonUtils.from(remark, RemarkDTO.class);
            if (remarkDTO != null) {
                if (!StringUtils.isEmpty(remarkDTO.getVhrDesc())) {
                    solutionDesc = remarkDTO.getVhrDesc() + CharConstant.CHAR_DD + "云控" + remarkDTO.getCloudSecurity()
                            + CharConstant.CHAR_DD + remarkDTO.getMoveCarReason();
                } else {
                    solutionDesc = remarkDTO.getMoveCarReason();
                }
            }
        }
        vo.setSolution(solutionDesc);
    }

    private PageResponse buildPageResponse(PageRequest request, List<?> resultList, int count, long maxId) {
        log.info("PageRequest = {}", request);
        Integer size = null;
        Integer page = null;
        if (request == null) {
            size = CommonConstant.DEFAULT_PAGE_SIZE;
            page = CommonConstant.DEFAULT_PAGE_NUM;
        } else {
            size = request.getSize();
            page = request.getPage();
        }
        size = size == null ? CommonConstant.DEFAULT_PAGE_SIZE : size;
        page = page == null ? CommonConstant.DEFAULT_PAGE_NUM : page;
        log.info("buildPageResponse, size = {}, page = {}", size, page);

        int totalPage = count % size == 0 ? count / size : count / size + 1;
        PageResponse pageResponse = PageResponse.success(resultList);
        pageResponse.setPage(page);
        pageResponse.setSize(size);
        pageResponse.setTotalSize(count);
        pageResponse.setTotalPage(totalPage);
        pageResponse.setMaxId(maxId);
        return pageResponse;
    }

    private void buildPageParam(PageRequest request) {
        Integer page = request.getPage();
        page = page == null || page < 1 ? CommonConstant.DEFAULT_PAGE_NUM : page;
        request.setPage(page);

        Integer size = request.getSize();
        if (size == null || size < 1) {
            size = CommonConstant.DEFAULT_PAGE_SIZE;
        } else if (size > CommonConstant.DEFAULT_MAX_PAGE_SIZE) {
            size = CommonConstant.DEFAULT_MAX_PAGE_SIZE;
        }
        request.setSize(size);
    }

    /**
     * 构建时间范围参数 如果查询的开始事件为空，则设置默认值：当前时间 -24 小时 如果查询的结束事件为空，则设置默认值：当前时间
     *
     * @param request
     */
    private void buildRangeTimeParam(RangeTimePageRequest request) {
        buildPageParam(request);

        String startTime = request.getStartTime();
        if (startTime == null) {
            int secondsBefore = queryEventTimeoutHour * CommonConstant.HOUR_TO_MINUTE * CommonConstant.MINUTE_TO_SECOND;
            startTime = DateUtil.format(DateUtil.getSecondsPreviousTime(secondsBefore), DateUtil.YMD_HMS_SSS_UNSIGNED);
        } else {
            startTime = DateUtil.replaceTimeChar(startTime, true);
        }
        request.setStartTime(startTime);
        String endTime = request.getEndTime();
        if (endTime == null) {
            endTime = DateUtil.format(new Date(), DateUtil.YMD_HMS_UNSIGNED);
        } else {
            endTime = DateUtil.replaceTimeChar(endTime, false);
        }
        request.setEndTime(endTime);
    }

    private void setUserRelativeInfo(Map<String, Object> param) {
        String misId = UserUtils.getUser().getLogin();
        param.put("misId", misId);
        param.put("clientIp", RequestContext.getIp());
    }
}
