package com.sankuai.walleops.cloud.triage.crane;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.MrmCalledStatusEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.GrayConfigDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.CockpitHandleService;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.transaction.annotation.Transactional;

@CraneConfiguration
@Slf4j
public class AbnormalEventCallCockpitCrane {

    @Resource
    private CockpitHandleService cockpitHandleService;

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @ConfigValue(key = "call.cockpit.config", defaultValue = "")
    private GrayConfigDTO grayConfigDTO;

    /**
     * 检测异常事件是否需要发起云控呼叫
     */
    @Crane("check.event.call.cockpit.crane")
    public void checkAbnormalEventCallCockpit() throws Exception {
        log.info("checkAbnormalEventCallCockpit start");

        // 1、 获取lion配置
        InputCheckUtil.isNotNull(grayConfigDTO, "callCockpitCraneDTOHashMap is null");

        // 2、 获取需要发起呼叫的异常事件列表
        List<BizCloudTriageEvent> abnormalEventList = getAbnormalEventList();
        if (CollectionUtils.isEmpty(abnormalEventList)) {
            log.info("checkAbnormalEventCallCockpit end, no abnormal event need to call cockpit");
            return;
        }

        // 3、按照异常事件类型拆分成多个列表, 并逐分组逐车辆呼叫云控
        Map<Integer, List<BizCloudTriageEvent>> abnormalEventMap = abnormalEventList.stream()
                .collect(Collectors.groupingBy(BizCloudTriageEvent::getEventType));
        for (Map.Entry<Integer, List<BizCloudTriageEvent>> entry : abnormalEventMap.entrySet()) {
            List<BizCloudTriageEvent> eventList = entry.getValue();
            eventList.stream().forEach(cloudTriageEvent -> {
                try {
                    // 更新呼叫字段，并发起呼叫
                    ((AbnormalEventCallCockpitCrane) AopContext.currentProxy()).updateEventAndCallCockpit(
                            cloudTriageEvent);
                } catch (Exception e) {
                    log.error("updateEventAndCallCockpit error, event:{}", cloudTriageEvent, e);
                }
            });
        }
    }

    /**
     * 更新事件呼叫状态，并发起呼叫
     *
     * @param event
     * @throws Exception
     */
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public void updateEventAndCallCockpit(BizCloudTriageEvent event) throws Exception {
        // 1、更新事件呼叫状态
        event.setMrmCalled(MrmCalledStatusEnum.CALLING.getCode());
        Boolean result = cloudTriageEventService.updateBizCloudTriageEvent(event);
        // 2、发起呼叫
        if (result) {
            cockpitHandleService.requestCloudOperation(event.getVin(), event.getEventType(),
                    CommonConstant.REQUEST_CLOUD_OPERATION_ACTION);
        }
    }

    /**
     * 获取需要发起呼叫的异常事件列表
     *
     * @return
     */
    private List<BizCloudTriageEvent> getAbnormalEventList() {
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        // 未发起呼叫
        request.setMrmCalled(MrmCalledStatusEnum.NO_CALL.getCode());
        // 已创建/未处置
        request.setStatus(EventStatusEnum.INITED.getCode());
        // operator_type = 3 (云辅助处置)
        request.setOperatorType(EventOperatorTypeEnum.COCKPIT.getCode());
        // 设置查询时间
        // fixme: 修改时间支持配置
        Date startTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, 30);
        request.setStartTime(DatetimeUtil.formatTime(startTime));
        request.setEndTime(DatetimeUtil.formatTime(new Date()));
        return cloudTriageEventService.commonQuery(request);
    }

}
