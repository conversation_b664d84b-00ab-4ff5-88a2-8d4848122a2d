package com.sankuai.walleops.cloud.triage.pojo.dto.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AbnormalEventCallCockpitMonitorCraneConfig {

    /**
     * 未处理超时时间（分钟）
     */
    private Integer unhandledTimeoutMins;
    /**
     * 已处理超时时间（分钟）
     */
    private Integer handledTimeoutMins;
    /**
     * 群ID
     */
    private long gid;

}
