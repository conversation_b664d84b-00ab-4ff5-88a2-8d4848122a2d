package com.sankuai.walleops.cloud.triage.pojo.dto;

import com.sankuai.walleops.cloud.triage.constant.CharConstant;
import lombok.Data;
import scala.collection.mutable.StringBuilder;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@Data
public class InformationDTO {
    private String informationId;

    // 1:规则异常，2:风险事件，3:事故，4:故障
    private Integer informationType;

    private String informationTypeDesc;

    // 1001:停滞不前，1002:发车超时，1003:停靠超时，2001:碰撞检测
    private Integer informationCode;

    private String informationDesc;

    private Long informationTimestamp;

    private String data;

    public String printMsg() {
        return new StringBuilder(informationId).append(CharConstant.CHAR_DD)
                .append(informationType).append(CharConstant.CHAR_DD)
                .append(informationTypeDesc).append(CharConstant.CHAR_DD)
                .append(informationCode).append(CharConstant.CHAR_DD)
                .append(informationDesc).append(CharConstant.CHAR_DD)
                .append(informationTimestamp).append(CharConstant.CHAR_DD)
                .toString();
    }
}
