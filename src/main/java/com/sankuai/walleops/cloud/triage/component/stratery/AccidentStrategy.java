package com.sankuai.walleops.cloud.triage.component.stratery;

import com.alibaba.fastjson.JSON;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.AccidentDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/25
 */
@Slf4j
@Component("accident")
public class AccidentStrategy extends EventStrategy {
    @Override
    public BizCloudTriageEvent transferEvent(InformationDTO information, EventTypeEnum eventTypeEnum) {
        AccidentDTO accidentDTO;
        try {
            accidentDTO = JSON.parseObject(information.getData(), AccidentDTO.class);
        } catch (Exception e) {
            log.error("information data parse error: {}", information.getData(), e);
            return null;
        }

        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
        Date eventTime = new Date(accidentDTO.getAccidentTimestamp());
        cloudTriageEvent.setEventTime(eventTime);

        String eventId = CommonUtil.generateEventId(eventTime, eventTypeEnum.getEventName(),
                accidentDTO.getVehicleName());
        cloudTriageEvent.setEventId(eventId);
        cloudTriageEvent.setEventType(eventTypeEnum.getCode());
        cloudTriageEvent.setVin(accidentDTO.getVin());
        cloudTriageEvent.setVehicleId(accidentDTO.getVehicleId());
        cloudTriageEvent.setRecordName(accidentDTO.getRecordName());
        cloudTriageEvent.setRemark(accidentDTO.getExtraInfo());
        String locationGps = accidentDTO.getLocationGps();
        if (StringUtils.isNotBlank(locationGps)) {
            String[] locationGpsArr = locationGps.split(",");
            if (locationGpsArr.length > 1) {
                cloudTriageEvent.setLongitude(locationGpsArr[0]);
                cloudTriageEvent.setLatitude(locationGpsArr[1]);
            }
        }

        return cloudTriageEvent;
    }
}
