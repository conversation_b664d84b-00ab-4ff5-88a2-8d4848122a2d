package com.sankuai.walleops.cloud.triage.config;

import static javax.servlet.DispatcherType.FORWARD;
import static javax.servlet.DispatcherType.REQUEST;

import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.sankuai.it.sso.sdk.spring.FilterFactoryBean;
import com.sankuai.meituan.uac.sdk.filter.UacFilterFactoryBean;
import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import com.sankuai.meituan.uac.sdk.service.UacResourceRemoteService;
import com.sankuai.meituan.uac.sdk.service.UacRoleRemoteService;
import javax.servlet.Filter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

/**
 * <AUTHOR>
 * @date 2021/08/26
 */
@Slf4j
@Configuration
public class SSOFilterConfiguration {

    //    @Value("${uac.appKey}")
    String appKey;

    //    @Value("${uac.secret}")
    String appSecret;

    //    @Value("${uac.host}")
    String uacHost;

    String ssoSecret;

    String clientId;

    SSOFilterConfiguration() {
        ConfigRepository config = Lion.getConfigRepository();
        appKey = config.get("uacAppkey");
        appSecret = config.get("uacAppSecret");
        uacHost = config.get("uacHost");

        // System.out.println(("appkey   "+appKey+"   "+ "appSecret  "+appSecret+"  uacHost  "+uacHost));
        ssoSecret = config.get("ssoSecret");
        clientId = config.get("clientId");
    }
    //    private static final String KMS_CLIENT_SSO_SECRET = "sso.client.secret";
    //
    //    private static final String KMS_CLIENT_SSO_CLIENT_ID = "sso.client.id";

    private static final String MONITOR_ALIVE = "/monitor/alive";

    @Bean
    public UacAuthRemoteService uacAuthRemoteService() {
        UacAuthRemoteService uacAuthRemoteService = new UacAuthRemoteService();
        uacAuthRemoteService.setUacHost(this.uacHost);
        uacAuthRemoteService.setUacClientId(this.appKey);
        uacAuthRemoteService.setUacSecret(this.appSecret);
        return uacAuthRemoteService;
    }

    @Bean
    public UacResourceRemoteService uacResourceRemoteService() {
        UacResourceRemoteService uacResourceRemoteService = new UacResourceRemoteService();
        uacResourceRemoteService.setUacHost(this.uacHost);
        uacResourceRemoteService.setUacClientId(this.appKey);
        uacResourceRemoteService.setUacSecret(this.appSecret);
        return uacResourceRemoteService;
    }

    @Bean
    public UacRoleRemoteService uacRoleRemoteService() {
        UacRoleRemoteService uacRoleRemoteService = new UacRoleRemoteService();
        uacRoleRemoteService.setUacHost(this.uacHost);
        uacRoleRemoteService.setUacClientId(this.appKey);
        uacRoleRemoteService.setUacSecret(this.appSecret);
        return uacRoleRemoteService;
    }

    @Bean
    public FilterRegistrationBean uacFilter() {
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        filter.setTargetBeanName("uacFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setDispatcherTypes(FORWARD);
        registration.setName("uacFilter");

        //注意顺序应设置在SSO filter之后
        registration.setOrder(2);
        return registration;
    }

    /**
     * uacFilterBean配置
     */
    @Bean
    public UacFilterFactoryBean uacFilterBean() {
        UacFilterFactoryBean filterFactoryBean = new UacFilterFactoryBean();

        //必须配置，以下二者需先到开放平台(http://open.sankuai.com)申请接入UAC后颁发，对应企平开放平台的AppKey和AppSecret
        filterFactoryBean.setAppKey(appKey);
        filterFactoryBean.setSecret(appSecret);

        //接入的UAC HOST地址，线下为：http://uac.it.beta.sankuai.com 线上为：http://uac.vip.sankuai.com
        filterFactoryBean.setHost(uacHost);

        //------------------------以下配置为可选配置--------------------------------------

        //表示需要经过UAC鉴权的uri，多个之间以','分割,支持Ant风格路径表达式。
        //includedUriList中的uri必须经过SSO进行过滤，否则UAC无法获取当前登录人信息进行鉴权。
        filterFactoryBean.setIncludedUriList("/api/**");

        //表示不需要经过UAC鉴权的uri。两者都配置的情况下，excludedUriList会失效，includedUriList优先级更高。
        //        filterFactoryBean.setExcludedUriList("/octo/checkAlive/**,/pass/**,/api/**");

        //UAC授权失败后返回的错误信息
        filterFactoryBean.setAuthFailedResponse(
                "{\"status\":501,\"message\":\"您没有权限访问,请通过uac开通\",\"version\":\"1.0.0-SNAPSHOT\",\"data\":{\"message\":\"您没有权限访问\"}}");

        //是否关闭UAC日志功能（URI鉴权过程打印一些日志，方便调试），true表示关闭，默认为false，即开启日志功能。
        filterFactoryBean.setLogClosed(true);
        return filterFactoryBean;
    }

    @Bean
    public FilterRegistrationBean<Filter> mtFilter(@Qualifier("mtFilterBean") Filter mtFilterBean) {
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
        registration.setFilter(mtFilterBean);
        registration.addUrlPatterns("/*");
        registration.setDispatcherTypes(REQUEST);
        registration.setName("mtFilter");
        registration.setOrder(2);

        return registration;
    }

    @Bean
    public FilterFactoryBean mtFilterBean() {
        FilterFactoryBean filterFactoryBean = new FilterFactoryBean();

        filterFactoryBean.setClientId(clientId);
        filterFactoryBean.setSecret(ssoSecret);
        filterFactoryBean.setExcludedUriList(MONITOR_ALIVE
                + ",/**/ba,/**/collisionDetection,/**/latestEventDetail/v2,/**/output/api/moveCarEvent/report,"
                + "/**/output/api/moveCarEvent/query,/**/output/api/venus/getToken,/**/output/api/getVehicleId,"
                + "/**/collisionDetectionAlert/get,/**/eventStatus/update");

        //可不配置，默认预留/sso/logout作为登出地址，业务方可以直接使用该uri
        filterFactoryBean.setLogoutUri("/logout");

        return filterFactoryBean;
    }
}
