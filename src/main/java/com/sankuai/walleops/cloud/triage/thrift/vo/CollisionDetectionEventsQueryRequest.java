package com.sankuai.walleops.cloud.triage.thrift.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "碰撞检测事件详情查询参数")
public class CollisionDetectionEventsQueryRequest {

    @ThriftField(1)
    @FieldDoc(description = "查询开始时间")
    private Long startTime;

    @ThriftField(2)
    @FieldDoc(description = "查询结束时间")
    private Long endTime;

    @ThriftField(3)
    @FieldDoc(description = "页码")
    private Integer pageNum;

    @ThriftField(4)
    @FieldDoc(description = "每页大小")
    private Integer pageSize;
}
