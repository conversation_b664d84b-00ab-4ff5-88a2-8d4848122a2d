package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum UacOperateCodeEnum {
    BOOT_ONBOARD("bootOnboard", 0),
    CALL_CLOUD_VALET_COCKPIT("callCloudValetCockpit", 1),
    CREATE_ORDER("createOrder", 2),
    START_AUTO_DRIVE("startAutoDrive", 3),
    VOICE_BROADCAST("voiceBroadcast", 4),
    VOICE_CHANNEL("voiceChannel", 5),
    CAR_VIDEO("carVideo", 6),
    SWITCH_NOSE_CABIN("switchNoseCabin", 7),
    SWITCH_REAR_CABIN("switchRearCabin", 8);

    private String name;
    private Integer code;

    public static Integer getCodeByName(String name) {
        for (UacOperateCodeEnum uacOperateCodeEnum : UacOperateCodeEnum.values()) {
            if (uacOperateCodeEnum.getName().equals(name)) {
                return uacOperateCodeEnum.getCode();
            }
        }
        return -1;
    }


}
