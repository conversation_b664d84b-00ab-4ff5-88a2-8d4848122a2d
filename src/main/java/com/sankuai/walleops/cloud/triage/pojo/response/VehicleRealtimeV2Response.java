package com.sankuai.walleops.cloud.triage.pojo.response;

import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusV2;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 车辆实时状态查询 v2 接口响应类
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VehicleRealtimeV2Response {
    
    /**
     * 响应码，0表示成功
     */
    private Integer code;
    
    /**
     * 响应消息，如服务不可用等异常信息
     */
    private String message;
    
    /**
     * 状态数据列表
     */
    private List<VehicleStatusV2> data;
    

}
