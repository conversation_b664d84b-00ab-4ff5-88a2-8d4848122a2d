package com.sankuai.walleops.cloud.triage.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class EventDTO {
    @JSONField(name = "event_id")
    private String eventId;

    @JSONField(name = "event_type")
    private Integer eventType;

    @JSONField(name = "event_timestamp")
    private Long eventTimestamp;

    @JSONField(name = "send_timestamp")
    private Long sendTimestamp;

    @JSONField(name = "vin")
    private String vin;

    @JSONField(name = "vehicle_name")
    private String vehicleName;

    @JSONField(name = "vehicle_id")
    private String vehicleId;

    @JSONField(name = "content")
    private Object content;
}
