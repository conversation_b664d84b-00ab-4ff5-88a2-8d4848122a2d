package com.sankuai.walleops.cloud.triage.util;

import com.alibaba.fastjson.JSON;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.oceanus.http.client.apache.OceanusHttpProcessor;
import com.sankuai.walleops.cloud.triage.constant.CharConstant;
import com.sankuai.walleops.cloud.triage.pojo.dto.DataChangeDiffDTO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpEntity;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.cglib.beans.BeanMap;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonUtil {
    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";

    public static String getNumberString(Double num) {
        return String.format("%.14f", num);
    }

    public static String generateEventId(Date eventTime, String eventName, String vehicleName) {
        return new StringBuilder(DateUtil.format(eventTime, DateUtil.YMD_HMS_SSS_UNSIGNED))
                .append(CharConstant.CHAR_XH).append(eventName)
                .append(CharConstant.CHAR_XH).append(vehicleName)
                .toString();

    }

    public static String getDate() {
        SimpleDateFormat df = new SimpleDateFormat("E, dd MMM yyyy HH:mm:ss z", Locale.UK);
        df.setTimeZone(new java.util.SimpleTimeZone(0, "GMT"));
        return df.format(new Date());
    }

    public static String getAuthorization(String uri, String method, String date,
                                          String clientId, String clientSecret) {
        String stringToSign = method + " " + uri + "\n" + date;
        String signature = getSignature(stringToSign, clientSecret);
        return "MWS" + " " + clientId + ":" + signature;
    }

    //重载 getAuthorization 用于生成新接口的 Authorization
    public static String getAuthorization(String uri, String method, String date,
                                          String clientId, String vin, String type, String clientSecret) {
        String stringToSign = method + " " + uri + "\n" + date+" "+vin+" "+type;

        System.out.println("stringToSign: "+stringToSign);
        System.out.println("clientSecret: "+clientSecret);
        String signature = getSignature(stringToSign, clientSecret);
        return "MWS" + " " + clientId + ":" + signature;
    }

    public static String getSignature(String data, String clientSecret) {
        String result;
        try {
            SecretKeySpec signingKey = new SecretKeySpec(clientSecret.getBytes(), HMAC_SHA1_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            result = Base64.encodeBase64String(rawHmac);
        } catch (Exception e) {
            log.error("Failed to generate HMAC", e);
            throw new IllegalStateException("Failed to generate HMAC :" + e.getMessage());
        }
        return result;
    }

    public static String doPost(String url, Map<String, Object> paramMap, Map<String, String> headers){
        return doPost(url, JSON.toJSONString(paramMap), headers);
    }

    public static String doPost(String url, String paramStr, Map<String, String> headers){
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse httpResponse = null;
        String result = null;
        // 创建httpPost远程连接实例
        // 配置请求参数实例
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(35000)// 设置连接主机服务超时时间
                .setConnectionRequestTimeout(35000)// 设置连接请求超时时间
                .setSocketTimeout(60000)// 设置读取数据连接超时时间
                .build();
        // 为httpPost实例设置配置
        httpPost.setConfig(requestConfig);
        // 设置请求头
        httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
        httpPost.setHeader("Accept", "application/json");
        if (headers != null) {
            headers.forEach(httpPost::addHeader);
        }
        // 封装post请求参数
        try {
            httpPost.setEntity(new StringEntity(paramStr, "utf-8"));
            // httpClient对象执行post请求,并返回响应参数对象

            httpResponse = httpClient.execute(httpPost);
            // 从响应对象中获取响应内容
            HttpEntity entity = httpResponse.getEntity();
            result = EntityUtils.toString(entity);
        } catch (IOException e) {
            log.error("call {} error，param: {}", url, paramStr, e);
        } finally {
            // 关闭资源
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    log.error("close resource error", e);
                }
            }
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error("close resource error", e);
            }
        }
        return result;
    }
    /**
     * 提交post请求 /autonomous-driving/rmanage/stream
     **/
    public static String doPostToSpiderVideoPath(String url, Map<String, Object> paramMap, Map<String, String> headerParam) {

        String authorization = getAuthorization(headerParam.get("path"), new HttpPost().getMethod(), getDate(), headerParam.get("clientId"), headerParam.get("clientSecret"));
        Map<String, String> headers = new HashMap<>();
        headers.put("Date", getDate());
        headers.put("Authorization", authorization);
        return doPost(url, JSON.toJSONString(paramMap), headers);
    }
    /**
     * 提交post请求 /api/customer/v1/vehicle/cmd
     **/
    public static String doPostToSpiderCmdPath(String url, Map<String, Object> paramMap, Map<String, String> headerParam) {
        //获取当下的时间
        SimpleDateFormat sdf = new SimpleDateFormat("ddMMyyyyHH:mm:ss'GMT'");
        String date =  sdf.format(new Date());
        String authorization = getAuthorization(headerParam.get("path"), new HttpPost().getMethod(), date, headerParam.get("clientId"), headerParam.get("vin"), headerParam.get("type"), headerParam.get("clientSecret"));
        Map<String, String> headers = new HashMap<>();
        headers.put("Date", date);
        headers.put("Authorization", authorization);
        return doPost(url, JSON.toJSONString(paramMap), headers);
    }
    /**
     * 查询提交的spark任务的状态
     *
     * @param url
     * @return
     */
    public static String doGet(String url, Map<String, Object> param) {
        StringBuilder sb = new StringBuilder(url).append(CharConstant.CHAR_WH);
        if (param != null && !param.isEmpty()) {
            param.forEach((key, value) -> {
                if (value instanceof String) {
                    try {
                        sb.append(key).append(CharConstant.CHAR_DY)
                                .append(URLEncoder.encode(value.toString(), "UTF-8"));
                    } catch (UnsupportedEncodingException e) {
                        log.error("URLEncoder error: {}", value, e);
                    }
                } else if (value instanceof List) {
                    List<String> list = (List<String>) value;
                    String valueStr = String.join(CharConstant.CHAR_DD, list);
                    sb.append(key).append(CharConstant.CHAR_DY).append(valueStr);
                } else {
                    sb.append(key).append(CharConstant.CHAR_DY).append(value);
                }
                sb.append(CharConstant.CHAR_AND);
            });
        }
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(sb.substring(0, sb.length() - 1));
        CloseableHttpResponse httpResponse = null;
        String result = null;
        // 配置请求参数实例
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(35000)// 设置连接主机服务超时时间
                .setConnectionRequestTimeout(35000)// 设置连接请求超时时间
                .setSocketTimeout(60000)// 设置读取数据连接超时时间
                .build();
        httpGet.setConfig(requestConfig);
        try {

            httpResponse = httpClient.execute(httpGet);
            // 从响应对象中获取响应内容
            HttpEntity entity = httpResponse.getEntity();
            result = EntityUtils.toString(entity);
        } catch (IOException e) {
            log.error("query error，url： {}", url, e);
        } finally {
            // 关闭资源
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    log.error("close resource error", e);
                }
            }
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error("close resource error", e);
            }
        }
        return result;
    }

    public static String doGet(String url, Map<String, Object> param,Map<String, String> headers ) {
        StringBuilder sb = new StringBuilder(url).append(CharConstant.CHAR_WH);
        if (param != null && !param.isEmpty()) {
            param.forEach((key, value) -> {
                if (value instanceof String) {
                    try {
                        sb.append(key).append(CharConstant.CHAR_DY)
                                .append(URLEncoder.encode(value.toString(), "UTF-8"));
                    } catch (UnsupportedEncodingException e) {
                        log.error("URLEncoder error: {}", value, e);
                    }
                } else if (value instanceof List) {
                    List<String> list = (List<String>) value;
                    String valueStr = String.join(CharConstant.CHAR_DD, list);
                    sb.append(key).append(CharConstant.CHAR_DY).append(valueStr);
                } else {
                    sb.append(key).append(CharConstant.CHAR_DY).append(value);
                }
                sb.append(CharConstant.CHAR_AND);
            });
        }
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(sb.substring(0, sb.length() - 1));
        CloseableHttpResponse httpResponse = null;
        String result = null;
        // 配置请求参数实例
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(35000)// 设置连接主机服务超时时间
                .setConnectionRequestTimeout(35000)// 设置连接请求超时时间
                .setSocketTimeout(60000)// 设置读取数据连接超时时间
                .build();
        httpGet.setConfig(requestConfig);
        httpGet.addHeader("Content-Type", "application/json");
        headers.forEach(httpGet::addHeader);
        try {

            httpResponse = httpClient.execute(httpGet);
            // 从响应对象中获取响应内容
            HttpEntity entity = httpResponse.getEntity();
            result = EntityUtils.toString(entity);
        } catch (IOException e) {
            log.error("query error，url： {}", url, e);
        } finally {
            // 关闭资源
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    log.error("close resource error", e);
                }
            }
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error("close resource error", e);
            }
        }
        return result;
    }

    public static String doPut(String url, Map<String, Object> paramMap, Map<String, String> headers) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPut httpPut = new HttpPut(url);
        CloseableHttpResponse httpResponse = null;
        String result = null;
        // 创建httpPost远程连接实例
        // 配置请求参数实例
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(35000)// 设置连接主机服务超时时间
                .setConnectionRequestTimeout(35000)// 设置连接请求超时时间
                .setSocketTimeout(60000)// 设置读取数据连接超时时间
                .build();
        // 为httpPost实例设置配置
        httpPut.setConfig(requestConfig);
        // 设置请求头
        httpPut.addHeader("Content-Type", "application/json");
        headers.forEach(httpPut::addHeader);
        // 封装post请求参数
        try {
            httpPut.setEntity(new StringEntity(JSON.toJSONString(paramMap), StandardCharsets.UTF_8));
            // httpClient对象执行post请求,并返回响应参数对象

            httpResponse = httpClient.execute(httpPut);
            // 从响应对象中获取响应内容
            HttpEntity entity = httpResponse.getEntity();
            result = EntityUtils.toString(entity);
            log.info("Dx doPut result:{}", result);
        } catch (IOException e) {
            log.error("call {} error，param: {}", url, paramMap.toString(), e);
        } finally {
            // 关闭资源
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    log.error("close resource error", e);
                }
            }
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error("close resource error", e);
            }
        }
        return result;
    }

    public static <T> Map<String, Object> beanToMap(T t) {
        Map<String, Object> param = new HashMap<>();
        BeanMap beanMap = BeanMap.create(t);
        for (Object key : beanMap.keySet()) {
            Object value = beanMap.get(key);
            if (value != null) {
                param.put(key.toString(), value);
            }
        }
        return param;
    }

    public static String getUserIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (!(null == ip || "".equals(ip.trim()) || "null".equalsIgnoreCase(ip.trim())) && ip.contains(",")) {
            ip = ip.split(",")[0];
        }
        return ip;
    }

    /**
     *  封装 access 鉴权的 post 请求方法
     *
     * @param url  请求路经
     * @param paramStr  请求体参数
     * @param headers 请求头参数
     * @return
     */
    public static String doPostWithAccessAuth(String url, String paramStr,  Map<String, String> headers) {

        CloseableHttpClient httpClient = (CloseableHttpClient) createHttpClient();
        HttpPost httpPost = new HttpPost(url);

        CloseableHttpResponse httpResponse = null;
        String result = null;
        // 创建httpPost远程连接实例
        // 配置请求参数实例
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(35000)// 设置连接主机服务超时时间
                .setConnectionRequestTimeout(35000)// 设置连接请求超时时间
                .setSocketTimeout(60000)// 设置读取数据连接超时时间
                .build();
        // 为httpPost实例设置配置
        httpPost.setConfig(requestConfig);
        // 设置请求头
        httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
        httpPost.setHeader("Accept", "application/json");
        if (headers != null) {
            headers.forEach(httpPost::addHeader);
        }
        // 封装post请求参数
        try {
            httpPost.setEntity(new StringEntity(paramStr, "utf-8"));
            // httpClient对象执行post请求,并返回响应参数对象

            httpResponse =  httpClient.execute(httpPost);
            // 从响应对象中获取响应内容
            HttpEntity entity = httpResponse.getEntity();
            result = EntityUtils.toString(entity);
        } catch (IOException e) {
            log.error("call {} error，param: {}", url, paramStr, e);
        } finally {
            // 关闭资源
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    log.error("close resource error", e);
                }
            }
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error("close resource error", e);
            }
        }
        return result;
    }

    /**
     * Access 客户端接入
     * @return HttpClient
     */
    public static HttpClient createHttpClient(){
        // 1. 创建ApacheHttpProcessor 注意设置服务端appkey
        // *注意：不要每次调用都创建一次，保持OceanusHttpProcessor全局单例！！
        OceanusHttpProcessor oceanusHttpProcessor = DataBusOceanusHttpProcessorSingleton.getInstance();

        // 2. 将OceanHttpProcessor设置到创建的httpclient中
        HttpClientBuilder httpClientBuilder = HttpClients.custom();
        httpClientBuilder.addInterceptorFirst((HttpRequestInterceptor) oceanusHttpProcessor);
        HttpClient httpClient = httpClientBuilder.build();

        return httpClient;
    }

    /**
     * 用于使用BA鉴权URL的POST请求
     *
     * @param url                 请求 url
     * @param paramStr            请求体参数
     * @param headerParam         请求头参数
     * @return                    响应结果
     */
    public static String doPostBA(String url, String paramStr, Map<String, String> headerParam) {
        String dateBa = getDate();
        String authorization = getAuthorization(headerParam.get("path"),
                new HttpPost().getMethod(),dateBa,
                headerParam.get("clientId"),
                headerParam.get("clientSecret"));
        Map<String, String> headers = new HashMap<>();
        headers.put("Date", dateBa);
        headers.put("Authorization", authorization);

        return doPost(url, paramStr, headers);
    }

    /**
     * 组装新旧数据DIFF
     *
     * @param oldObj
     * @param newObj
     * @return
     */
    public static String getChangeDiff(Object oldObj, Object newObj) {
        return JsonUtil.toJson(DataChangeDiffDTO.builder().oldData(oldObj).newData(newObj).build());
    }

}
