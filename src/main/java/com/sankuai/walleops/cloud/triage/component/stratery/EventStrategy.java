package com.sankuai.walleops.cloud.triage.component.stratery;

import com.alibaba.fastjson.JSON;
import com.sankuai.walleops.cloud.triage.component.LockService;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.*;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import com.sankuai.walleops.cloud.triage.util.SpringBeanUtil;
import com.sankuai.walleops.cloud.triage.util.exception.ParamInputErrorException;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/25
 */
@Slf4j
public class EventStrategy {
    public static final Integer RECOVER_STATUS = 2;

    private static final String DEFAULT_OPERATOR = "system";

    //数据格式转化函数
    public BizCloudTriageEvent transferEvent(EventDTO eventDTO, EventTypeEnum eventTypeEnum) throws ParamInputErrorException {
        return new BizCloudTriageEvent();
    }

    public BizCloudTriageEvent transferEvent(InformationDTO information, EventTypeEnum eventTypeEnum) {
        DepartureEventDTO eventDTO;
        try {
            eventDTO = JSON.parseObject(information.getData(), DepartureEventDTO.class);
        } catch (Exception e) {
            log.error("information data parse error: {}", information.getData(), e);
            return null;
        }

        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
        Date eventTime = new Date(eventDTO.getEventTimestamp());
        cloudTriageEvent.setEventTime(eventTime);

        String eventId = CommonUtil.generateEventId(eventTime, eventTypeEnum.getEventName(),
                eventDTO.getVehicleName());
        cloudTriageEvent.setEventId(eventId);
        cloudTriageEvent.setEventType(eventTypeEnum.getCode());
        cloudTriageEvent.setVin(eventDTO.getVin());
        cloudTriageEvent.setVehicleId(eventDTO.getVehicleId());
        cloudTriageEvent.setLongitude(eventDTO.getLongitude() == null ? "" : eventDTO.getLongitude().toString());
        cloudTriageEvent.setLatitude(eventDTO.getLatitude() == null ? "" : eventDTO.getLatitude().toString());
        cloudTriageEvent.setRemark(eventDTO.getContent());
        return cloudTriageEvent;
    }

    // 异常恢复
    public final boolean exceptionRecover(String eventId, Integer status) {

        if (!RECOVER_STATUS.equals(status)) {
            return false;
        }
        LockService lockService = SpringBeanUtil.getBean(LockService.class);
        if (!lockService.lock(CommonConstant.EVENT_QU_LOCK, eventId, CommonConstant.LOCK_EXPIRE_SECONDS)) {
            return true;
        }

        CloudTriageEventService cloudTriageEventService = SpringBeanUtil.getBean(CloudTriageEventService.class);
        try {
            // 如果是还未处理直接恢复了，则需要取消
            BizCloudTriageEvent eventInDb = cloudTriageEventService.queryByUniqueKey(eventId, null);
            if (eventInDb == null) {
                log.error("exception recover，but not found in db: {}", eventId);
                return true;
            }
            // 如果是进行中以上的状态，则无需自动取消，而是人工已介入
            BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
            Date recoveryTime = new Date();
            cloudTriageEvent.setRecoveryTime(recoveryTime);
            if (!EventStatusEnum.COMPLETED.getCode().equals(eventInDb.getStatus()) &&
                    !EventStatusEnum.CANCELED.getCode().equals(eventInDb.getStatus())) {
                cloudTriageEvent.setStatus(EventStatusEnum.CANCELED.getCode());
                cloudTriageEvent.setOperator(DEFAULT_OPERATOR);

                if (eventInDb.getOperateStartTime().before(CommonConstant.DEFAULT_EARLY_DATE)) {
                    cloudTriageEvent.setOperateStartTime(recoveryTime);
                }

                cloudTriageEvent.setOperateEndTime(recoveryTime);
            }

            cloudTriageEvent.setEventId(eventId);
            cloudTriageEvent.setId(eventInDb.getId());

            cloudTriageEventService.updateOnly(cloudTriageEvent);
        } catch (Exception e) {
            log.error("exceptionRecover error, eventId: {}", eventId, e);
        } finally {
            lockService.unLock(CommonConstant.EVENT_QU_LOCK, eventId);
        }
        return true;
    }

    // 超时恢复
    public final boolean timeoutRecover(String informationId, Integer status) {
        CloudTriageEventService cloudTriageEventService = SpringBeanUtil.getBean(CloudTriageEventService.class);
        if (!RECOVER_STATUS.equals(status)) {
            return false;
        }
        LockService lockService = SpringBeanUtil.getBean(LockService.class);
        if (!lockService.lock(CommonConstant.EVENT_QU_LOCK, informationId, CommonConstant.LOCK_EXPIRE_SECONDS)) {
            return true;
        }
        try {
            // 如果是还未处理直接恢复了，则需要取消
            BizCloudTriageEvent eventInDb = cloudTriageEventService.queryByInformationId(informationId);
            if (eventInDb == null) {
                log.error("timeout recover，but not found in db: {}", informationId);
                return true;
            }
            // 如果是进行中以上的状态，则无需自动取消，而是人工已介入
            BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
            Date recoveryTime = new Date();
            cloudTriageEvent.setRecoveryTime(recoveryTime);
            if (!EventStatusEnum.COMPLETED.getCode().equals(eventInDb.getStatus()) &&
                    !EventStatusEnum.CANCELED.getCode().equals(eventInDb.getStatus())) {
                cloudTriageEvent.setStatus(EventStatusEnum.CANCELED.getCode());
                cloudTriageEvent.setOperator(DEFAULT_OPERATOR);

                if (eventInDb.getOperateStartTime().before(CommonConstant.DEFAULT_EARLY_DATE)) {
                    cloudTriageEvent.setOperateStartTime(recoveryTime);
                }

                cloudTriageEvent.setOperateEndTime(recoveryTime);
            }

            cloudTriageEvent.setEventId(eventInDb.getEventId());
            cloudTriageEvent.setId(eventInDb.getId());

            cloudTriageEventService.updateOnly(cloudTriageEvent);
        } catch (Exception e) {
            log.error("exceptionRecover error, eventId: {}", informationId, e);
        } finally {
            lockService.unLock(CommonConstant.EVENT_QU_LOCK, informationId);
        }
        return true;
    }

    public final BizCloudTriageEvent generateDepartureExceptionEvent(DepartureExceptionEventDTO eventDTO,
                                                                EventTypeEnum eventTypeEnum,
                                                                Date eventTime) {
        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
        String eventId = CommonUtil.generateEventId(eventTime, eventTypeEnum.getEventName(),
                eventDTO.getVehicleName());
        cloudTriageEvent.setEventTime(eventTime);
        cloudTriageEvent.setEventId(eventId);
        cloudTriageEvent.setEventType(eventTypeEnum.getCode());
        cloudTriageEvent.setVin(eventDTO.getVin());
        cloudTriageEvent.setVehicleId(eventDTO.getVehicleId());
        cloudTriageEvent.setLongitude(eventDTO.getLongitude() == null ? "" : eventDTO.getLongitude().toString());
        cloudTriageEvent.setLatitude(eventDTO.getLatitude() == null ? "" : eventDTO.getLatitude().toString());
        return cloudTriageEvent;
    }
}
