package com.sankuai.walleops.cloud.triage.component;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class LockService {

    @Resource
    @Qualifier(value = "redisClient")
    private RedisStoreClient redisStoreClient;

    private static final int MAX_RETRY = 200;

    /**
     * 加锁
     *
     * @param expireSeconds 过期时间，单位秒
     * @return true:加锁成功，false，加锁失败
     */
    public boolean lock(String lockType, String id, int expireSeconds, int tryTimes) {
        StoreKey storeKey = new StoreKey(lockType, id);
        int i = 0;
        while (i < tryTimes) {
            boolean status = lock(storeKey, expireSeconds);
            if (status) {
                return true;
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                log.error("lock InterruptedException", e);
            }
            i++;
        }

        log.warn("failed to get lock for area {}", id);
        return false;
    }


    /**
     * 加锁
     *
     * @param expireSeconds 过期时间，单位秒
     * @return true:加锁成功，false，加锁失败
     */
    public boolean lock(String lockType, String id, int expireSeconds) {
        return lock(lockType, id, expireSeconds, MAX_RETRY);
    }


    public boolean lock(StoreKey storeKey, int expireSeconds) {
        return redisStoreClient.setnx(storeKey, 0, expireSeconds);
    }

    public void unLock(StoreKey storeKey) {
        try {
            redisStoreClient.delete(storeKey);
        } catch (Exception e) {
            log.error("redisStoreClient delete failed", e);
        }
    }

    public void unLock(String lockType, String id) {
        StoreKey storeKey = new StoreKey(lockType, id);
        unLock(storeKey);
    }
}
