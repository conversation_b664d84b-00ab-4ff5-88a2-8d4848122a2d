package com.sankuai.walleops.cloud.triage.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/9/19
 */
@Data
@Table(name = "biz_operate_history")
public class BizOperateHistory {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 车辆id
     */
    private String vehicleId;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 操作内容描述
     */
    private String description;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作响应结果
     */
    private String operateResponse;

    /**
     * 关联的事件id
     */
    private String relatedEventId;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private int isDeleted;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_time
     */
    private Date updateTime;
}
