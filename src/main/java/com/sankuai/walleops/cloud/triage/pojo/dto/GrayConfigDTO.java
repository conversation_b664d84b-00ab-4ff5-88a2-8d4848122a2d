package com.sankuai.walleops.cloud.triage.pojo.dto;

import static com.sankuai.walleops.cloud.triage.constant.CommonConstant.ALL_STRING;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class GrayConfigDTO {

    private Map<String, GrayConfigItem> configs = new HashMap<>();

    // 添加这个方法来处理动态的 key-value 对
    @JsonAnySetter
    public void setConfig(String key, GrayConfigItem value) {
        this.configs.put(key, value);
    }

    @Data
    public static class GrayConfigItem {

        private Integer reason;
        private List<String> grayPurposeList;

        /**
         * 判断是否处于灰度范围内
         *
         * @param purpose
         * @return
         */
        public boolean isInGrayScope(String purpose) {
            return grayPurposeList.contains(ALL_STRING) || grayPurposeList.contains(purpose);
        }
    }
}