package com.sankuai.walleops.cloud.triage.component.stratery;

import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component("routing_generate_failed")
@Slf4j
public class RoutingGenerateFailedStrategy extends DepartureFailedStrategy {

    @Override
    public BizCloudTriageEvent transferEvent(InformationDTO information, EventTypeEnum eventTypeEnum) {
        return super.transferEvent(information, eventTypeEnum);
    }
}
