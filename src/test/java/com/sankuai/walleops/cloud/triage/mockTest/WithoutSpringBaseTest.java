package com.sankuai.walleops.cloud.triage.mockTest;

import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.walleops.cloud.triage.component.LockService;
import com.sankuai.walleops.cloud.triage.mapper.BizCloudTriageEventMapper;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.MockitoRule;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;

/**
 * 不启动Spring的测试基类，基于 Mockito & PowerMock 的测试类，屏蔽了各种基础美团语境下的组件；(mafka/crane/logcenter/...)
 *
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(MockitoJUnitRunner.Silent.class)
@PowerMockIgnore({"com.fasterxml.*",
        "com.dianping.cat.*",
        "com.dianping.pigeon.*",
        "com.meituan.scribe.*",
        "com.meituan.service.*",
        "com.meituan.service.inf.kms.*",
        "com.meituan.mtrace.*",
        "com.sankuai.inf.*",
        "com.sun.*",
        "javax.*",
        "javax.management.*",
        "javax.net.ssl.*",
        "org.apache.logging.*",
        "org.apache.*",
        "org.slf4j.*",
        "org.w3c.*",
        "org.xml.*",
        "sun.*"})
@Ignore
@PrepareForTest({TraceExecutors.class})
@SuppressStaticInitializationFor({"com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices"})
public abstract class WithoutSpringBaseTest {

    @Rule
    public MockitoRule rule = MockitoJUnit.rule();

    @Mock
    LockService lockService;

    @Mock
    public BizCloudTriageEventMapper bizCloudTriageEventMapper;

    public void mockRedisLock() {
        //redis锁mock
        Mockito.doReturn(true).when(lockService)
                .lock(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt());
        Mockito.doNothing().when(lockService)
                .unLock(Mockito.anyString(), Mockito.anyString());

    }
}
