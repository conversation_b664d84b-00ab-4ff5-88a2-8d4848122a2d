package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;

import java.util.HashMap;
import java.util.List;

public class GroupStrategyMap {
    /*
        返回 hashmap ，该map用于根据用车目的获取分组信息
     */
    public static HashMap<String,Integer> getPurposeIndexMap() {
        HashMap<String,Integer> res = new HashMap<String,Integer>();

        for(String s: GroupStrategyConfig.GROUP1_PURPOSE){
            res.put(s, 0);
        }
        for(String s: GroupStrategyConfig.GROUP2_PURPOSE){
            res.put(s, 1);
        }

        return res;
    }
    /*
          返回 hashmap ，该map用于根据事件类型获取用车目的
      */
    public static HashMap<Integer,List<String>> getIndexPurposeMap() {
        HashMap<Integer,List<String>> res = new HashMap<Integer,List<String>>();

        for(Integer i: GroupStrategyConfig.GROUP1_EVENTTYPE){
            res.put(i, GroupStrategyConfig.GROUP1_PURPOSE);
        }

        List<String> purposeList = GroupStrategyConfig.getMergedPurposeList();
        for(Integer i: GroupStrategyConfig.GROUP2_EVENT_TYPE){
            if(res.containsKey(i)){
                res.put(i, purposeList);
            }
            else{
                res.put(i, GroupStrategyConfig.GROUP2_PURPOSE);
            }
        }
        return res;
    }


}
