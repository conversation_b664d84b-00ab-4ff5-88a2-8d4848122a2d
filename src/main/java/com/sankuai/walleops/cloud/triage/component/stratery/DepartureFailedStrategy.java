package com.sankuai.walleops.cloud.triage.component.stratery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.DepartureFailedEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/25
 */
@Component("departure_failed")
@Slf4j
public class DepartureFailedStrategy extends EventStrategy{
    @Override
    public BizCloudTriageEvent transferEvent(InformationDTO information, EventTypeEnum eventTypeEnum) {
        DepartureFailedEventDTO eventDTO;
        try {
            eventDTO = JSON.parseObject(information.getData(), DepartureFailedEventDTO.class);
        } catch (Exception e) {
            log.error("information data parse error: {}", information.getData(), e);
            return null;
        }

        String message = eventDTO.getMessage() == null ? "" : eventDTO.getMessage();
        String code = eventDTO.getCode() == null ? "" : eventDTO.getCode();

        if (timeoutRecover(information.getInformationId(), eventDTO.getStatus())) {
            return null;
        }

        BizCloudTriageEvent cloudTriageEvent = generateDepartureExceptionEvent(eventDTO,
                eventTypeEnum, new Date(eventDTO.getEventTimestamp()));

        Date departureTime = new Date(eventDTO.getDepartureTimestamp());
        String departureTimeStr = DateUtil.format(departureTime, DateUtil.YMD_HMS_SSS_UNSIGNED);
        String content = eventDTO.getContent();
        JSONObject contentJson = StringUtils.isBlank(content) ? new JSONObject() : JSON.parseObject(content);
        contentJson.put("code", code);
        contentJson.put("message", message);
        contentJson.put("departureTime", departureTimeStr);
        cloudTriageEvent.setRemark(contentJson.toJSONString());

        return cloudTriageEvent;
    }
}
