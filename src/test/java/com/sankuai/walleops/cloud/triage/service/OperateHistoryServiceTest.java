package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperateHistoryPageDTO;
import com.sankuai.walleops.cloud.triage.pojo.request.OperateHistoryPageRequest;
import com.sankuai.walleops.cloud.triage.service.impl.OperateHistoryServiceImpl;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;
import static org.mockito.Mockito.any;
import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class OperateHistoryServiceTest {

    @InjectMocks
    private final OperateHistoryService operateHistoryService = new OperateHistoryServiceImpl();

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @Test
    public void queryOperateHistoryListTest() {
        //测试vin和eventId同时为空的情况，此种情况会抛出RuntimeException
        OperateHistoryPageRequest requestException = new OperateHistoryPageRequest();
        requestException.setVin("");
        requestException.setEventId("");
        requestException.setPage(1);
        requestException.setSize(10);

        expectedException.expect(RuntimeException.class);
        expectedException.expectMessage("vin和eventId不能同时为空");

        OperateHistoryPageDTO pageDTO = operateHistoryService.queryOperateHistoryList(requestException);

        assertNotNull(pageDTO);
    }
}
