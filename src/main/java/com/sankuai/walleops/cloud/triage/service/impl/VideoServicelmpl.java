package com.sankuai.walleops.cloud.triage.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.pojo.dto.VideoDTO;
import com.sankuai.walleops.cloud.triage.pojo.response.VideoReponse;
import com.sankuai.walleops.cloud.triage.service.VideoService;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
@Service
@Slf4j
public class VideoServicelmpl implements VideoService {

    @MdpConfig("spiderHostName")
    String HostName;
    @MdpConfig("spider.clientId")
    String clientId;
    @MdpConfig("spider.clientSecret")
    String clientSecret;
    private static final String path = "/autonomous-driving/rmanage/stream";

    @Override
    public VideoReponse getHDVideo(VideoDTO videoDTO) {

        String url = HostName + path;
        Map<String, Object> requestParam = CommonUtil.beanToMap(videoDTO);
        //BA鉴权参数
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("clientId", clientId);
        headerParam.put("clientSecret", clientSecret);
        headerParam.put("path", path);

        VideoReponse videoReponse = new VideoReponse();
        try {
            String response = CommonUtil.doPostToSpiderVideoPath(url, requestParam, headerParam);
            JSONObject responseJson = JSONObject.parseObject(response);
            Gson gson = new Gson();
            videoReponse = gson.fromJson(responseJson.getString("data"), VideoReponse.class);
            log.info("getHDVideo, requestParam = {}, responseParam = {}", requestParam, videoReponse);
        } catch (Exception e) {
            log.error("getHDVideo is failed!", e);
        }

        return videoReponse;
    }
}
