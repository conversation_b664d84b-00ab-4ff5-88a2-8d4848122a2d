package com.sankuai.walleops.cloud.triage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
@EnableTransactionManagement
@EnableAspectJAutoProxy(exposeProxy = true)
public class ApplicationLoader {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ApplicationLoader.class);
        application.setAdditionalProfiles(MdpContextUtils.getHostEnvStr());
        application.run(args);

        printStartSuccessMsg();
    }

    private static void printStartSuccessMsg() {
        Logger logger = LoggerFactory.getLogger(ApplicationLoader.class);
        String successMsg ="\n" +
                "       _____              _____                                                  \n" +
                "_________  /______ _________  /_   ___________  _________________________________\n" +
                "__  ___/  __/  __ `/_  ___/  __/   __  ___/  / / /  ___/  ___/  _ \\_  ___/_  ___/\n" +
                "_(__  )/ /_ / /_/ /_  /   / /_     _(__  )/ /_/ // /__ / /__ /  __/(__  )_(__  ) \n" +
                "/____/ \\__/ \\__,_/ /_/    \\__/     /____/ \\__,_/ \\___/ \\___/ \\___//____/ /____/  \n";
        logger.info(successMsg);
    }
}


