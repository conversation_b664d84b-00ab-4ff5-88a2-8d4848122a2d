package com.sankuai.walleops.cloud.triage.component.stratery;

import com.alibaba.fastjson.JSON;
import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.constant.CharConstant;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.VehicleTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.CollisionDetectionEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.GeographicPoint;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import com.sankuai.walleops.cloud.triage.util.GeographicUtils;
import deps.redis.clients.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/10/25
 */
@Slf4j
@Component("collision_detection")
public class CollisionDetectionStrategy extends EventStrategy {

    @Resource
    private InformationApiService informationApiService;

    @Override
    public BizCloudTriageEvent transferEvent(InformationDTO information, EventTypeEnum eventTypeEnum) {
        CollisionDetectionEventDTO eventDTO;
        try {
            eventDTO = JSON.parseObject(information.getData(), CollisionDetectionEventDTO.class);
        } catch (Exception e) {
            log.error("information data parse error: {}", information.getData(), e);
            return null;
        }

        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
        Date eventTime = new Date(eventDTO.getEventTimestamp());
        cloudTriageEvent.setEventTime(eventTime);

        String[] originEventIdParts = eventDTO.getEventId().split(CharConstant.CHAR_XH);
        String eventId = CommonUtil.generateEventId(eventTime, eventTypeEnum.getEventName(),
                originEventIdParts[originEventIdParts.length - 1]);
        cloudTriageEvent.setEventId(eventId);
        cloudTriageEvent.setEventType(eventTypeEnum.getCode());
        cloudTriageEvent.setRecordName(eventDTO.getRecordName());
        cloudTriageEvent.setVin(eventDTO.getVin());
        cloudTriageEvent.setVehicleId(eventDTO.getVehicleId());

        Double utmX = eventDTO.getUtmX();
        Double utmY = eventDTO.getUtmY();
        Integer utmZone = eventDTO.getUtmZone();
        if(utmX != null && utmX != null && utmZone != null) {
            GeographicPoint geographicPoint =
                    GeographicUtils.utmXyToLatLon(utmX, utmY, utmZone, false);
            cloudTriageEvent.setLongitude(CommonUtil.getNumberString(geographicPoint.getLongitude()));
            cloudTriageEvent.setLatitude(CommonUtil.getNumberString(geographicPoint.getLatitude()));
        }

        // 如果是VHR=1的车，设置 事件状态=取消状态，处置人=system
        Map<String, Object> param = new HashMap<>();
        param.put("vinList", Arrays.asList(cloudTriageEvent.getVin()));
        List<VehicleRealtimeStatusDTO> vehicleRealtimeStatusDTOS =
                informationApiService.callVehicleStatusService(param);
        if(CollectionUtils.isNotEmpty(vehicleRealtimeStatusDTOS)) {
            VehicleRealtimeStatusDTO vehicleStatus = vehicleRealtimeStatusDTOS.get(0);
            Integer isVhrMultiple = vehicleStatus.getIsVhrMultiple();
            if(isVhrMultiple != null && isVhrMultiple.intValue() == 0) { // VHR<=1
                cloudTriageEvent.setOperator("system");
                cloudTriageEvent.setStatus(3);
            }
        }

        return cloudTriageEvent;
    }
}
