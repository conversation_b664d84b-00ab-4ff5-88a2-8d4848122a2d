package com.sankuai.walleops.cloud.triage.component.stratery;

import com.alibaba.fastjson.JSON;
import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.PsEventEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventContentDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.CockpitHandleService;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import com.sankuai.walleops.cloud.triage.util.exception.ParamInputErrorException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component("trafficCongestion-alert")
@Slf4j
public class TrafficJamStrategy extends EventStrategy {

    @Resource
    CloudTriageEventService cloudTriageEventService;

    @Resource
    private InformationApiService informationApiService;

    @Resource
    private CockpitHandleService cockpitHandleService;

    @Override
    public BizCloudTriageEvent transferEvent(EventDTO eventDTO, EventTypeEnum eventTypeEnum)
            throws ParamInputErrorException {

        //1 首先解析出扩展字段
        EventContentDTO eventContentDTO = null;
        try {
            eventContentDTO = JSON.parseObject(eventDTO.getContent().toString(), EventContentDTO.class);
            InputCheckUtil.isNotNull(eventContentDTO, "eventContentDTO is null");
        } catch (Exception e) {
            log.error("parse cloud eventContentDTO error: {}", eventDTO.getContent(), e);
            return null;
        }
        Boolean needHandle = eventContentDTO.getNeedHandle();
        //2 数据格式转化
        BizCloudTriageEvent bizCloudTriageEvent = PsEventDTOToBizCloudTriageEvent(eventDTO, eventContentDTO);
        //  设置额外信息
        addExtraInfo(bizCloudTriageEvent);
        // todo 需要在数据格式转化的时候设置处置人类型,即在命中灰度/全量的时候设置处置人类型为坐席，否则保持状态
        if (cockpitHandleService.isInGray(eventTypeEnum.getCode(), bizCloudTriageEvent.getPurpose())) {
            log.info("traffic jam is in gray, bizCloudTriageEvent = {}, needHandle = {}", bizCloudTriageEvent,
                    needHandle);
            bizCloudTriageEvent.setOperatorType(EventOperatorTypeEnum.COCKPIT.getCode());
        }
        //3 判断该异常事件是否需要关注
        if (!setAutoCancel(needHandle, bizCloudTriageEvent, eventDTO)) {
            return null;
        }
        return bizCloudTriageEvent;
    }

    //解析保障系统的数据，转化成入库数据格式
    public  BizCloudTriageEvent PsEventDTOToBizCloudTriageEvent(EventDTO eventDTO, EventContentDTO  eventContentDTO) throws ParamInputErrorException {
        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
        String eventId = eventDTO.getEventId();
        InputCheckUtil.isNotBlank(eventId,"eventId is blank");
        cloudTriageEvent.setEventId(eventId);

        Integer eventType = PsEventEnum.getTypeByCode(eventDTO.getEventType());
        InputCheckUtil.isNotNull(eventType,"eventType is null");
        cloudTriageEvent.setEventType(eventType);

        cloudTriageEvent.setEventTime( new Date(eventDTO.getEventTimestamp()));
        cloudTriageEvent.setVehicleId(StringUtils.isBlank(eventDTO.getVehicleId())? "unknown":eventDTO.getVehicleId());


        String vin = eventDTO.getVin();
        InputCheckUtil.isNotBlank(vin,"vin is blank");
        cloudTriageEvent.setVin(vin);

        //使用 eventId 拼接 informationId 字符
        String informationId = eventId +"_"+ vin;
        cloudTriageEvent.setInformationId(informationId);

        //todo：这里的经纬度是 UTM 坐标系下的坐标转换为经纬度坐标系下的坐标
        String latitude = Double.toString(eventContentDTO.getLatitude() == null? 0.0 : eventContentDTO.getLatitude());
        cloudTriageEvent.setLatitude(latitude);
        String longitude = Double.toString(eventContentDTO.getLongitude() == null? 0.0: eventContentDTO.getLongitude());
        cloudTriageEvent.setLongitude(longitude);
        cloudTriageEvent.setRecordName(eventContentDTO.getRecordName() == null? "" : eventContentDTO.getRecordName());

        log.info("PsEventDTOToBizCloudTriageEvent,cloudTriageEvent = {}", cloudTriageEvent);
        return cloudTriageEvent;
    }

    //将指定的扎堆事件设置为取消状态，并判断是否需要插入新数据
    public  Boolean setAutoCancel(Boolean needHandle, BizCloudTriageEvent bizCloudTriageEvent, EventDTO eventDTO) {
        try {
            // 设置模糊查询的指定查询范围
            Date minCreateTime = DateUtil.getBeforeTime(new Date(), TimeUnit.HOURS, 12);
            BizCloudTriageEvent historyBizCloudTriageEvent = cloudTriageEventService.queryLatestEventDetailByUniqueKey(
                    bizCloudTriageEvent.getEventId(), minCreateTime);
            //1 需要处置
            if (needHandle) {
                //  1） 是否有相同eventId前缀的数据（同一扎堆事件)
                if (historyBizCloudTriageEvent != null) {
                    //  判断是否是重复数据,重复则直接忽略
                    if (bizCloudTriageEvent.getVin().equals(historyBizCloudTriageEvent.getVin())) {
                        log.info("traffic jam is repeat, bizCloudTriageEvent = {}, needHandle = {}",
                                bizCloudTriageEvent, needHandle);
                        return false;
                    }
                    //  同一事件不重复,且无人处理，则将历史事件设置为取消，重新生成新的事件
                    else {
                        if (historyBizCloudTriageEvent.getStatus().equals(EventStatusEnum.INITED.getCode())) {
                            log.info("traffic jam is repeat,but vin is diff, bizCloudTriageEvent = {}, needHandle = {}",
                                    bizCloudTriageEvent, needHandle);
                            CloudTriageEventUpdateRequest cloudTriageEventUpdateRequest = new CloudTriageEventUpdateRequest();
                            cloudTriageEventUpdateRequest.setId(historyBizCloudTriageEvent.getId());
                            cloudTriageEventUpdateRequest.setStatus(EventStatusEnum.CANCELED.getCode());
                            cloudTriageEventUpdateRequest.setOperator("system");
                            cloudTriageEventService.update(cloudTriageEventUpdateRequest);
                        }
                    }
                }
                //  2) 否则需要插入一条新数据，只需设置 eventId
                String vehicleName = eventDTO.getVehicleName() == null? bizCloudTriageEvent.getVehicleId(): eventDTO.getVehicleName();
                bizCloudTriageEvent.setEventId(bizCloudTriageEvent.getEventId()+"_"+ vehicleName);
            }
            //2 不需要处置,那么一定不需要插入新事件
            else{
                //只有存在同一辆车的扎堆事件，且分诊人员未处理的状态才需要设置为取消状态
                if(historyBizCloudTriageEvent != null
                        && bizCloudTriageEvent.getVin().equals(historyBizCloudTriageEvent.getVin())
                        && historyBizCloudTriageEvent.getStatus().equals(EventStatusEnum.INITED.getCode())
                ) {
                    log.info("traffic jam is cancel, bizCloudTriageEvent = {}, needHandle = {}", bizCloudTriageEvent,
                            needHandle);
                    CloudTriageEventUpdateRequest cloudTriageEventUpdateRequest = new CloudTriageEventUpdateRequest();
                    cloudTriageEventUpdateRequest.setId(historyBizCloudTriageEvent.getId());
                    cloudTriageEventUpdateRequest.setStatus(EventStatusEnum.CANCELED.getCode());
                    cloudTriageEventUpdateRequest.setOperator("system");
                    cloudTriageEventService.update(cloudTriageEventUpdateRequest);
                }
                return false;
            }
        } catch (Exception e) {
            log.error("setAutoCancel is failed,bizCloudTriageEvent = {}, error is {}", bizCloudTriageEvent, e);
        }
        return true;
    }

    /**
     * 添加额外信息
     *
     * @param cloudTriageEvent
     */
    private void addExtraInfo(BizCloudTriageEvent cloudTriageEvent) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("vinList", cloudTriageEvent.getVin());
            List<VehicleRealtimeStatusDTO> resultList = informationApiService.callVehicleStatusService(param);
            if (!resultList.isEmpty()) {
                VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = resultList.get(0);
                //添加用车目的 以及 停车地点
                cloudTriageEvent.setPurpose(vehicleRealtimeStatusDTO.getPurpose());
                cloudTriageEvent.setPlace(vehicleRealtimeStatusDTO.getPark());
            }
        } catch (Exception e) {
            log.error("query information purpose and place error: {}", cloudTriageEvent);
        }
    }

}
