package com.sankuai.walleops.cloud.triage.adaptor;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.walleops.cloud.triage.pojo.request.MoveCarRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.pojo.response.ReportMoveCarEventResponse;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ReportMoveCarServiceAdaptor {

    /**
     * 上报挪车事件接口
     */
    private static final String REPORT_MOVE_CAR_EVENT = "/api/risk/moveCarEvent/report";


    /**
     * 查询挪车事件接口
     */
    private static final String QUERY_MOVE_CAR_EVENT = "/api/risk/moveCarEvent/query";

    @Value("${report.moveCar.service.host.name}")
    private String reportMoveCarServiceHostName;


    /**
     * 上报挪车事件
     *
     * @param request
     * @param token
     * @return
     * @throws Exception
     */
    public CommonResponse reportMoveCarEvent(MoveCarRequest request, String token) throws Exception {
        String url = reportMoveCarServiceHostName + REPORT_MOVE_CAR_EVENT;
        try {

            Map<String, Object> paramMap = JacksonUtils.from(JacksonUtils.to(request), Map.class);
            paramMap.put("token", token);
            String response = CommonUtil.doPost(url, paramMap, null);
            log.info("reportMoveCarEvent, response = {}", response);
            ReportMoveCarEventResponse eventResponse = JacksonUtils.from(response,
                    ReportMoveCarEventResponse.class);
            return CommonResponse.builder().ret(eventResponse.getCode()).msg(eventResponse.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("reportMoveCarEvent failed!", e);
            throw new Exception(e);
        }
    }

    /**
     * 查询挪车事件
     *
     * @param vehicleId
     * @param token
     * @return
     * @throws Exception
     */
    public CommonResponse queryMoveCarEvent(String vehicleId, String token) throws Exception {
        String url = reportMoveCarServiceHostName + QUERY_MOVE_CAR_EVENT;
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("vehicleId", vehicleId);
            paramMap.put("token", token);
            String response = CommonUtil.doPost(url, paramMap, null);
            log.info("queryMoveCarEvent, response = {}", response);
            ReportMoveCarEventResponse eventResponse = JacksonUtils.from(response,
                    ReportMoveCarEventResponse.class);
            return CommonResponse.builder().ret(eventResponse.getCode()).msg(eventResponse.getMessage())
                    .data(eventResponse.getData())
                    .build();
        } catch (Exception e) {
            log.error("queryMoveCarEvent failed!", e);
            throw new Exception(e);
        }
    }


}
