package com.sankuai.walleops.cloud.triage.service.impl;

import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleHistoryStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.request.VideoHistoryStatusRequest;
import com.sankuai.walleops.cloud.triage.service.VehicleStatusService;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VehicleStatusServiceImpl implements VehicleStatusService {

    @Resource
    private InformationApiService informationApiService;

    @Override
    public List<VehicleHistoryStatusDTO> queryVehicleHistoryStatus(VideoHistoryStatusRequest request) {
        Date startDate = DateUtil.parseDate(request.getStartTime());
        Date endDate = DateUtil.parseDate(request.getEndTime());
        if(startDate == null || endDate == null) {
            throw new RuntimeException("时间格式不对");
        }

        Map<String, Object> param = new HashMap<>();
        param.put("vin", request.getVin());
        param.put("startTimestamp", startDate.getTime());
        param.put("endTimestamp", endDate.getTime());

        log.info("查询状态回放数据-信息中心, param: {}", param);
        List<VehicleHistoryStatusDTO> historyStatusDTOList =
                informationApiService.queryVehicleHistoryStatus(param)
                        .stream().filter(Objects::nonNull).collect(Collectors.toList());
        log.info("查询状态回放数据-信息中心, result: {}", historyStatusDTOList);

        return historyStatusDTOList;
    }
    
    
}
