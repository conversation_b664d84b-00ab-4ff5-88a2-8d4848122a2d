package com.sankuai.walleops.cloud.triage.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: operation_change_log
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OperationChangeLog {
    /**
     *   字段: id
     *   说明: 变更ID
     */
    private Long id;

    /**
     *   字段: related_id
     *   说明: 关联ID
     */
    private Long relatedId;

    /**
     *   字段: change_type
     *   说明: 变更类型
     */
    private Integer changeType;

    /**
     *   字段: operator
     *   说明: 变更人员
     */
    private String operator;

    /**
     *   字段: change_desc
     *   说明: 变更描述(扩展字段)
     */
    private String changeDesc;

    /**
     *   字段: change_time
     *   说明: 变更时间
     */
    private Date changeTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: is_deleted
     *   说明: 是否删除[0-否｜1-是]
     */
    private Integer isDeleted;

    /**
     *   字段: status
     *   说明: 状态, 0表示未处理，1表示进行中，2表示完成，3表示取消
     */
    private Integer status;

    /**
     *   字段: change_content
     *   说明: 变更内容
     */
    private String changeContent;
}