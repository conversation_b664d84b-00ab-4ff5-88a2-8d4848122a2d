package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@AllArgsConstructor
@Getter
public enum InformationTypeEnum {
    VEHICLE_STANDSTILL(1001, "车辆停滞不前"),
    DEPARTURE_FAILED(1002, "发车失败"),
    DEPARTURE_TIMEOUT(1003, "发车超时"),
    ARRIVE_TIMEOUT(1004, "到达超时"),
    ROUTING_SEND_FAILED(1005, "路由下发失败"),
    ROUTING_GENERATE_FAILED(1006,  "路由生成失败"),
    START_AUTO_FAILED(1007,  "启动自动驾驶失败"),
    DOCKING_TIMEOUT(1008, "停靠超时"),
    COLLISION_DETECTION(2001, "碰撞检测"),
    POWER_WARNING(2002, "电量告警"),
    DEPARTURE_HOME(3001, "发车-HOME"),
    DEPARTURE_STATION(3002, "发车-停靠点"),
    ACCIDENT(4001, "事故"),
    COREDUMP(5001, "coredump"),
    AUTODRIVE_ABNORMAL_EXIT(5002, "自动驾驶异常退出"),
    AUTODRIVE_PROCESS_ABNORMAL_EXIT(5004, "自动驾驶程序异常退出")
    ;

    private final Integer code;
    private final String msg;

    public static InformationTypeEnum getByCode(Integer code) {
        for (InformationTypeEnum value : InformationTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
