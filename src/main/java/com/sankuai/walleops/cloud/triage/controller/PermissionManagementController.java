package com.sankuai.walleops.cloud.triage.controller;


import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.uac.sdk.entity.UacPermissionEntity;
import com.sankuai.meituan.uac.sdk.entity.menu.MenuNode;
import com.sankuai.meituan.uac.sdk.entity.menu.UserMenu;
import com.sankuai.meituan.uac.sdk.entity.page.UacPageInfo;
import com.sankuai.meituan.uac.sdk.entity.page.UacPageList;
import com.sankuai.walleops.cloud.triage.constant.UacEventCodeEnum;
import com.sankuai.walleops.cloud.triage.constant.UacOperateCodeEnum;
import com.sankuai.walleops.cloud.triage.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import com.sankuai.meituan.uac.sdk.service.UacRoleRemoteService;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.walleops.cloud.triage.service.OperatorStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.*;


@RestController
@Slf4j
public class PermissionManagementController {

    public class ResData {
        public int code;
        public String msg;
        public Object data;
        public Long rows;
    }
    @Resource
    private OperatorStatusService operatorStatusService;

    @Autowired
    UacAuthRemoteService uacAuthRemoteService;
    @Autowired
    UacRoleRemoteService uacRoleRemoteService;
    @MdpConfig("operator.status.client.id")
    private String operatorStatusClientId;

    /**
     * uac获取用户权限列表的分页大小配置，最大不超过500
     */
    @MdpConfig("uac.page.info.pageSize")
    private Integer pageSize = 200;

    //根据请求获取资源名称，判断是否具有访问权限, 查询权限管理
    @RequestMapping(path = {"/permission/query"}, method = RequestMethod.GET)
    public ResData queryPermissionManage(HttpServletRequest request) {
        Cookie ssoCookie = Arrays.stream(request.getCookies())
                .filter(item -> item.getName().contains(operatorStatusClientId + "_ssoid"))
                .findAny().orElse(null);
        if(ssoCookie == null) {
            log.info("permission/query, ssoCookie is null");
            return null;
        }
        User user = UserUtils.getUser(ssoCookie.getValue());
        if(user == null){
            log.info("permission/query, user is null");
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        try{
            UacPageInfo uacPageInfo = new UacPageInfo();
            uacPageInfo.setPageSize(pageSize);
            UacPageList<UacPermissionEntity> uacPageList = uacAuthRemoteService.getUserPermissions(String.valueOf(user.getId()), uacPageInfo);
            log.info("getUserPermissions,misId = {}, uacPageList = {}", user.getLogin(), uacPageList.getPageList());
            for(UacPermissionEntity entity: uacPageList.getPageList()) {
                Integer temp = UacEventCodeEnum.getCodeByName(entity.getCode());
                //根据 UacCodeEnum 定义，-2 为不存在的 code
                if( temp == -2){
                    continue;
                }
                jsonObject.put( temp.toString() , UacEventCodeEnum.getDescriptionByCode(temp) );
            }
        }
        catch (Exception e){
            log.error("permission/query, getUserPermissions is failed, user = {}, uid = {}", user.getLogin(), user.getId(),e);
        }

        ResData req = new ResData();
        req.data = jsonObject;
        req.code = 0;
        log.info("queryPermissionManage,misId = {}, ResData = {}", user.getLogin(), req);
        return req;

    }


    //根据请求获取资源名称，判断是否具有访问权限, 查询权限管理
    @RequestMapping(path = {"/permission/operate"}, method = RequestMethod.GET)
    public ResData operatePermissionManage(HttpServletRequest request) {
        Cookie ssoCookie = Arrays.stream(request.getCookies())
                .filter(item -> item.getName().contains(operatorStatusClientId + "_ssoid"))
                .findAny().orElse(null);
        if(ssoCookie == null) {
            log.info("permission/operate, ssoCookie is null");
            return null;
        }
        User user = UserUtils.getUser(ssoCookie.getValue());
        if(user == null){
            log.info("permission/operate, user is null");
            return null;
        }
        boolean[] arr = new boolean[10];
        Arrays.fill(arr, true);

        try{
            UacPageInfo uacPageInfo = new UacPageInfo();
            uacPageInfo.setPageSize(pageSize);
            UacPageList<UacPermissionEntity> uacPageList = uacAuthRemoteService.getUserPermissions(String.valueOf(user.getId()), uacPageInfo);
            log.info("getUserPermissions,misId = {}, uacPageList = {}", user.getLogin(), uacPageList.getPageList());
            for(UacPermissionEntity entity: uacPageList.getPageList()) {
                Integer temp = UacOperateCodeEnum.getCodeByName(entity.getCode());
                //根据 UacOperateCodeEnum 定义，-1 为不存在的 code
                if(temp == -1){
                    log.info("{} is not exist ! ", entity.getCode());
                    continue;
                }
                arr[temp] = false;
            }
        }
        catch (Exception e){
            log.error("permission/operate, getUserPermissions is failed, user = {}, uid = {}", user.getLogin(), user.getId(),e);
        }

        ResData req = new ResData();
        req.data = arr;
        req.code = 0;
        log.info("operatePermissionManage,misId = {}, ResData = {}", user.getLogin(), req);
        return req;

    }

    private String getLogin(HttpServletRequest request) {
        Cookie ssoCookie = Arrays.stream(request.getCookies())
                .filter(item -> item.getName().contains(operatorStatusClientId + "_ssoid"))
                .findAny().orElse(null);
        if(ssoCookie == null) {
            return null;
        }

        User user = UserUtils.getUser(ssoCookie.getValue());
        if(user == null) {
            return null;
        }

        return user.getLogin();
    }
}
