package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AccidentLevelEnum {
    ACCIDENT_DEFAULT(0, "accident-default",-1), //0 表示非碰撞检测事件
    ACCIDENT_L1(1, "accident-level1", 26),   //层级1更改为 事故检测-高置信
    ACCIDENT_L2_HIGH(2, "accident-level2-high",1),  //层级2保持为事故检测
    ACCIDENT_L2_LOW(3, "accident-level2-low",24),  //层级3新增事故检测low
    ACCIDENT_L3(4, "accident-level3",-1);      //-1 标志未知事件

    private final Integer level;
    private final String desc;
    private final Integer eventType;

    public static Integer getEventTypeByLevel(Integer level) {
        for (AccidentLevelEnum accidentLevel : AccidentLevelEnum.values()) {
            if (accidentLevel.getLevel().equals(level)) {
                return accidentLevel.getEventType();
            }
        }
        return -1;
    }
}
