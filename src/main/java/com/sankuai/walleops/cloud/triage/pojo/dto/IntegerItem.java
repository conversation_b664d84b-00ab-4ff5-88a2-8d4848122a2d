package com.sankuai.walleops.cloud.triage.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 整型数据项，用于表示带有状态信息的整型值
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IntegerItem {
    
    /**
     * 是否存在该数据
     */
    private Boolean present;
    
    /**
     * 具体的整型值
     */
    private Integer value;
    
    /**
     * 时间戳（毫秒）
     */
    private Long timestamp;
    
    /**
     * 描述信息
     */
    private String description;
}
