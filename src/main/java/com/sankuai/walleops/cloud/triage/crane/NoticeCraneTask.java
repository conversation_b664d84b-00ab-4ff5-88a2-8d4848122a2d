package com.sankuai.walleops.cloud.triage.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.mapper.BizCloudTriageEventMapper;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.proxy.SquirrelProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@CraneConfiguration
@Slf4j
public class NoticeCraneTask {

    @Resource
    private SquirrelProxy squirrelProxy;

    @Resource
    private BizCloudTriageEventMapper bizCloudTriageEventMapper;

    @MdpConfig("accident.exceed.minutes:5")
    private Long accidentExceedMinutes;

    @Resource
    private InformationApiService informationApiService;

    @Crane("accident.notice.crane.task")
    public void accidentNoticeCraneTask() {
        Map<String, Long> allFieldMap =
                squirrelProxy.hgetall(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY, CommonConstant.KEY_ACCIDENT_HASH);

        if(MapUtils.isEmpty(allFieldMap)) {
            log.info("accident set is empty");
            return;
        }

        log.info("accident set: {}", allFieldMap);

        for(Map.Entry<String, Long> entry: allFieldMap.entrySet()) {
            String eventId = entry.getKey();
            Long eventTimeStamp = entry.getValue();

            // 如果距离事故发生时间不超过X分钟，不进行处理
            if(System.currentTimeMillis() - eventTimeStamp < accidentExceedMinutes * 60 * 1000) {
                continue;
            }

            BizCloudTriageEvent eventDetail = bizCloudTriageEventMapper.queryByUniqueKey(eventId, null);
            if(eventDetail == null) {
                continue;
            }

            // 如果事故状态为 已完成 或者 取消，不发送通知消息，需要在redis删除相应的field
            if(eventDetail.getStatus().intValue() == 2 || eventDetail.getStatus().intValue() == 3) {
                squirrelProxy.hdel(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY, CommonConstant.KEY_ACCIDENT_HASH, eventId);
                continue;
            }

            // 发送通知消息
            Map<String, Object> param = new HashMap<>();
            param.put("vin", eventDetail.getVin());
            param.put("eventTimestamp", eventTimeStamp);
            Long elaspedMinutes = (System.currentTimeMillis() - eventTimeStamp) / 60000;
            param.put("uncheckedTime", elaspedMinutes);
            informationApiService.notifyAccidentMsg(param);
        }
    }
}
