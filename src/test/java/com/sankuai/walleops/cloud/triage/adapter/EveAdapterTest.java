package com.sankuai.walleops.cloud.triage.adapter;

import com.sankuai.walleops.cloud.triage.BaseTest;
import com.sankuai.walleops.cloud.triage.adaptor.eve.EVEAdaptor;
import java.util.Arrays;
import javax.annotation.Resource;
import org.junit.Test;

public class EveAdapterTest extends BaseTest {

    @Resource
    EVEAdaptor eveAdaptor;

    @Test
    public void testGetVehicleStatusByDataBus(){
        eveAdaptor.getVehicleStatusByDataBus(Arrays.asList("LMTZSV027NC027844"));
        // key=all时的Response:
        //{
        //   "vehicle_manage": {
        //      "vehicle_name": "S20-387",
        //      "update_time": 1715544625
        //   },
        //   "maintenance_order": {
        //      "has_order": false,
        //      "order_name": "transactionOrder",
        //      "order_status": -1,
        //      "order_status_desc": "无工单",
        //      "update_time": 1715069110,
        //      "create_time": 1715069110
        //   },
        //   "rescue_order": {
        //      "has_order": true,
        //      "order_name": "rescueOrder",
        //      "order_status": 0,
        //      "order_status_desc": "待处理",
        //      "update_time": 1715589668,
        //      "create_time": 1715589668
        //   },
        //   "reserve_vehicle": {
        //      "vresv_list": [
        //         {
        //            "start_time": 1715529600000,
        //            "location_name": "尚唐",
        //            "approved": true,
        //            "deleted": 0,
        //            "telecontrol": "",
        //            "end_time": 1715615940000,
        //            "substitute": "",
        //            "resv_id": "4d241f23a5294eeba70d2c985e81acbe"
        //         }
        //      ],
        //      "update_time": 1715600125
        //   },
        //   "accident_order": {
        //      "has_order": true,
        //      "order_name": "accidentOrder",
        //      "update_time": 1715426105,
        //      "create_time": 1704949063
        //   },
        //   "vin": "LMTZSV027NC027844",
        //   "monitor": {
        //      "disk": {
        //         "sys_disk_type": "SATA",
        //         "data_disk": {
        //            "used_mb": 934532.44,
        //            "total_mb": 1802485.5,
        //            "usage_percent": 54.6
        //         },
        //         "update_time": 1715600321753787,
        //         "sys_disk": {
        //            "used_mb": 4165.59,
        //            "total_mb": 15785.285,
        //            "usage_percent": 26.4
        //         }
        //      },
        //      "update_time": 1715600324,
        //      "compute_unit_type": "IPC",
        //      "onboard_status": "INIT_FAILURE",
        //      "kmph": 0,
        //      "battery": {
        //         "present_bat": 2,
        //         "ret_time": 1715600325,
        //         "battery_list": [
        //            {
        //               "soc": 0,
        //               "bat_name": "bat_1",
        //               "mileage": 0
        //            },
        //            {
        //               "soc": 40,
        //               "bat_name": "bat_2",
        //               "mileage": 11.212961647727273
        //            }
        //         ],
        //         "virtual_bat": 0,
        //         "vin": "LMTZSV027NC027844"
        //      },
        //      "gear": "P"
        //   },
        //   "monitor_compute": {
        //      "drive_desc": [
        //         "未知",
        //         "自动驾驶状态",
        //         "云控软接管状态",
        //         "云控硬接管状态",
        //         "手动控制状态",
        //         "其他状态"
        //      ],
        //      "drive_mode": 5,
        //      "has_snow_chain": false,
        //      "is_home": true,
        //      "noise_status": "",
        //      "online_desc": [
        //         "未知",
        //         "离线",
        //         "在线"
        //      ],
        //      "online_status": 2,
        //      "position": {
        //         "latitude": 40.184105,
        //         "longitude": 116.62614,
        //         "source": "gnss_bestpos"
        //      },
        //      "remote_control_status": false,
        //      "top_speed": 0,
        //      "update_time": 1715600325
        //   },
        //   "label": {
        //      "tip": "仅展示有权限的标签",
        //      "tags": {}
        //   }
        //}
    }

}
