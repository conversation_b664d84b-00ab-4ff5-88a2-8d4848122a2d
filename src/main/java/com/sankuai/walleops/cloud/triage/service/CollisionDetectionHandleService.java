package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.thrift.vo.NearbyRescueCollisionDetectionDetailVO;

public interface CollisionDetectionHandleService {

    /**
     * 处理碰撞检测事件
     *
     * @param event
     */
    void handleCollisionDetectionEvent(BizCloudTriageEvent event);

    /**
     * 查询实时碰撞检测事件
     *
     * @param vin
     * @return
     */
    default NearbyRescueCollisionDetectionDetailVO queryRealtimeCollisionDetectionEvent(String vin) {
        return null;
    }

    /**
     * 更新碰撞检测状态
     *
     * @param request
     * @return
     */
    default ResponseCodeEnum updateCollisionDetectionEvent(CloudTriageEventUpdateRequest request) throws Exception {
        return null;
    }


}
