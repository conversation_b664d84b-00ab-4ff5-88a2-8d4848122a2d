package com.sankuai.walleops.cloud.triage.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.walleops.cloud.triage.adaptor.eve.EVEAdaptor;
import com.sankuai.walleops.cloud.triage.component.CommonFunctionService;
import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.component.handleStrategy.HandleStrategy;
import com.sankuai.walleops.cloud.triage.component.pike.PikeServer;
import com.sankuai.walleops.cloud.triage.constant.AccidentLevelEnum;
import com.sankuai.walleops.cloud.triage.constant.CollisionDetectionResponsibilityTagEnum;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.SmEventEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.PsEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VideoDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.config.CollisionDetectionControllerConfig;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CommonDateTransferRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.CockpitHandleService;
import com.sankuai.walleops.cloud.triage.service.ExternalInterfaceService;
import com.sankuai.walleops.cloud.triage.service.VideoService;
import com.sankuai.walleops.cloud.triage.service.impl.CloudSecurityHandleServiceImpl;
import com.sankuai.walleops.cloud.triage.service.impl.NearbyRescueHandleServiceImpl;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/statusMonitor")
@Slf4j
public class CollisionDetectionController {
    @Resource
    private InformationApiService informationApiService;

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private PikeServer pikeServer;

    @Resource
    ExternalInterfaceService externalInterfaceService;
    @Resource
    VideoService videoService;

    @Resource
    private CloudSecurityHandleServiceImpl cloudSecurityHandleService;

    @Resource
    private NearbyRescueHandleServiceImpl nearbyRescueHandleService;

    @Resource
    private EVEAdaptor eveAdaptor;

    @Resource
    private CommonFunctionService commonFunctionService;

    @Resource
    private CockpitHandleService cockpitHandleService;

    @MdpConfig("spiderHostName")
    String HostName;
    @MdpConfig("spider.clientId")
    String clientId;
    @MdpConfig("spider.clientSecret")
    String clientSecret;
    @MdpConfig("videoRequestDuration")
    private ArrayList<Integer> videoRequestDuration;

    @MdpConfig("collision.detection.controller.config")
    private CollisionDetectionControllerConfig controllerConfig;

    private static final String COLLISION_DETECTION_DETECT = "accidentDetect";


    @Resource
    private Map<String, HandleStrategy> eventHandleStrategyMap;

    @PostMapping("/collisionDetection")
    public CommonResponse getCollisionDetectionList(@RequestBody CommonDateTransferRequest dateTransferEq) {
        log.info("collisionDetection, dateTransferEq = {}", dateTransferEq);
        // todo: 根据配置进行碰撞检测时间的分派
        if (Objects.nonNull(controllerConfig)
                && Objects.nonNull(controllerConfig.getEnableDistribution())
                && controllerConfig.getEnableDistribution()) {
            return handleCollisionDetection(dateTransferEq);
        }

        // 1）过滤信息
        if (dateTransferEq.getKey() != null && dateTransferEq.getKey().equals("accidentDetect")) {

            // 2) 解析扩展字段
            PsEventDTO psEventDTO = null;
            try {
                psEventDTO = JSON.parseObject(dateTransferEq.getData(), PsEventDTO.class);
            } catch (Exception e) {
                log.error("/statusMonitor/collisionDetection, parse psEventDTO is failed: ", e);
                return CommonResponse.success();
            }

            //3）过滤异常事件
            if (psEventDTO.getEventType() == null || SmEventEnum.getTypeByCode(psEventDTO.getEventType()) == null) {
                log.info("/statusMonitor/collisionDetection, eventType is {}, need to filter",
                        psEventDTO.getEventType());
                return CommonResponse.success();
            }

            if (externalInterfaceService.getRescueOrderStatus(psEventDTO.getVin())) {
                log.info("/statusMonitor/collisionDetection, vin = {} is rescued", psEventDTO.getVin());
                return CommonResponse.success();
            }

            //4）数据格式转化
            BizCloudTriageEvent cloudTriageEvent = null;
            try {
                cloudTriageEvent = PsEventDTOToBizCloudTriageEvent(psEventDTO);
            } catch (Exception e) {
                log.error("bizCloudTriageEventTransfer is failed, psEventDTO ={}", psEventDTO);
                return CommonResponse.success();
            }
            // 屏蔽非关注的碰撞检测事件，比如碰撞等级为 0，4的事件
            Integer eventType = cloudTriageEvent.getEventType();
            if (eventType.equals(-1)) {
                log.info("accident_level is 0 or 4, not to care! psEventDTO = {}", psEventDTO);
                return CommonResponse.success();
            }
            addExtraInfo(cloudTriageEvent);
            // 5 解析碰撞检测的alert
            String alert = psEventDTO.getEventContentDTO().getEvaluationAlert();
            parseAlertAndSetEventDesc(cloudTriageEvent, alert);

            // 6 根据异常事件类型去执行响应的策略
            String strategyName = EventTypeEnum.getByCode(cloudTriageEvent.getEventType()).getEventName() + "-handle";
            if (eventHandleStrategyMap.containsKey(strategyName)) {
                HandleStrategy handleStrategy = eventHandleStrategyMap.get(strategyName);
                handleStrategy.process(cloudTriageEvent);
            } else {
                log.error("getCollisionDetectionList,can't find strategyName",
                        new IllegalArgumentException("当前碰撞检测事件找不到处置策略类"));
                commonFunctionService.setAutoCancel(cloudTriageEvent);
            }
            try {
                int result = cloudTriageEventService.save(cloudTriageEvent);
                if (result < 1) {
                    log.info("database write failure! cloudTriageEvent = {}", cloudTriageEvent);
                }

                //当数据插入成功后，开始触发高清视频打捞
                startHDVideoSalvage(cloudTriageEvent.getVin(), cloudTriageEvent.getEventTime());
                pikeServer.notifyNewEvent(cloudTriageEvent);
            } catch (Exception e) {
                log.error("save cloudTriageEvent error: {}", cloudTriageEvent, e);
            }
        }
        return CommonResponse.success();
    }

    /**
     * 碰撞检测事件处理
     *
     * @param dateTransferEq
     * @return
     */
    public CommonResponse handleCollisionDetection(CommonDateTransferRequest dateTransferEq) {
        log.info("handleCollisionDetection, dateTransferEq = {}", dateTransferEq);
        try {
            InputCheckUtil.isNotNull(dateTransferEq, "dateTransferEq is null");
            InputCheckUtil.isNotBlank(dateTransferEq.getKey(), "dateTransferEq.key is blank");
            InputCheckUtil.isNotBlank(dateTransferEq.getData(), "dateTransferEq.data is null");

            if (!Objects.equals(dateTransferEq.getKey(), COLLISION_DETECTION_DETECT)) {
                return CommonResponse.success();
            }
            // 参数解析
            PsEventDTO psEventDTO = JSON.parseObject(dateTransferEq.getData(), PsEventDTO.class);
            InputCheckUtil.isNotNull(psEventDTO, "parse psEventDTO is failed");
            InputCheckUtil.isNotNull(psEventDTO.getEventType(), "eventType is blank");
            InputCheckUtil.isNotBlank(psEventDTO.getVin(), "vin is blank");
            InputCheckUtil.isNotNull(psEventDTO.getEventTimestamp(), "eventTime is null");

            // todo: 增加黑名单/对于H24车辆支持灰度
            if (!CollectionUtils.isEmpty(controllerConfig.getGrayVinSet())
                    && controllerConfig.getGrayVinSet().contains(psEventDTO.getVin())) {
                log.info("handleCollisionDetection, grayVinSet contains vin = {}", psEventDTO.getVin());
                return CommonResponse.success();
            }

            // 参数转换
            BizCloudTriageEvent cloudTriageEvent = PsEventDTOToBizCloudTriageEventV2(psEventDTO);
            log.info("handle collision detection, cloudTriageEvent ={}", cloudTriageEvent);
            // 查询数据总线，获取近场VHR 和 云控 VHR 以及近场测试模式
            String nearbyRescueVHR = "";
            String telecontrolVHR = "";
            String responsibilityTag = "";
            String vehicleType = "";
            VehicleStatusDTO vehicleStatusDTO = eveAdaptor.getVehicleStatusFromDataBusByVin(psEventDTO.getVin());
            //todo: 假设数据总线查不到一辆车的近场/云控VHR信息，则会执行下方的兜底逻辑
            if (Objects.nonNull(vehicleStatusDTO)) {
                nearbyRescueVHR = vehicleStatusDTO.getNearbyRescueVHRValue();
                telecontrolVHR = vehicleStatusDTO.getTelecontrolVHRValue();
                vehicleType = vehicleStatusDTO.getVehicleType();
                responsibilityTag = vehicleStatusDTO.getReservationModeTag();
            }
            log.info("handle collision detection, controllerConfig = {}", controllerConfig);
            //  根据配置判断是否分发事件给不同的处理者
            if (controllerConfig.getEnableDistributionAdjust() && !CollectionUtils.isEmpty(
                    controllerConfig.getVehicleTypeSet()) && controllerConfig.getVehicleTypeSet()
                    .contains(vehicleType)) {
                log.info("handle collision detection, enable distribution adjust");
                distributeByResponsibilityTag(cloudTriageEvent, responsibilityTag, telecontrolVHR);
                return CommonResponse.success();
            }

            // todo: 为什么VHR可以小于1?
            if (Objects.equals(nearbyRescueVHR, "=1") || Objects.equals(nearbyRescueVHR, "<1")) {
                // 分发给近场处置
                cloudTriageEvent.setOperatorType(EventOperatorTypeEnum.NEARBY_RESCUE.getCode());
                nearbyRescueHandleService.handleCollisionDetectionEvent(cloudTriageEvent);
            } else {
                if (Objects.equals(telecontrolVHR, "=1")) {
                    cloudTriageEvent.setStatus(EventStatusEnum.CANCELED.getCode());
                }
                // todo: 判断指定异常事件类型是否命中灰度逻辑
                if (cockpitHandleService.isInGray(cloudTriageEvent.getEventType(),
                        cloudTriageEvent.getPurpose())) {
                    cloudTriageEvent.setOperatorType(EventOperatorTypeEnum.COCKPIT.getCode());
                } else {
                    cloudTriageEvent.setOperatorType(EventOperatorTypeEnum.CLOUD_SECURITY.getCode());
                }

                cloudSecurityHandleService.handleCollisionDetectionEvent(cloudTriageEvent);
            }
        } catch (Exception e) {
            log.error("collisionDetection is failed, dateTransferEq = {}", dateTransferEq, e);
        }
        return CommonResponse.success();
    }

    /**
     * 根据VHR和近场测试模式分发事件
     *
     * @param cloudTriageEvent
     * @param responsibilityTag
     * @param telecontrolVHR
     */
    private void distributeByResponsibilityTag(BizCloudTriageEvent cloudTriageEvent, String responsibilityTag,
            String telecontrolVHR) {

        // 1. 判断是否有人有责 - 分配近场处置
        if (Objects.equals(responsibilityTag,
                CollisionDetectionResponsibilityTagEnum.PERSON_WITH_RESPONSIBILITY.getDesc())) {
            cloudTriageEvent.setOperatorType(EventOperatorTypeEnum.NEARBY_RESCUE.getCode());
            nearbyRescueHandleService.handleCollisionDetectionEvent(cloudTriageEvent);
        }
        // 2. 判断无人无责 - 分配云安全处置
        else if (Objects.equals(responsibilityTag,
                CollisionDetectionResponsibilityTagEnum.NO_PERSON_NO_RESPONSIBILITY.getDesc())) {
            cloudTriageEvent.setOperatorType(EventOperatorTypeEnum.COCKPIT.getCode());
            cockpitHandleService.handleCollisionDetectionEvent(cloudTriageEvent);
            // 3. 判断无人有责 - 然后根据vhr近一步分发
        } else if (Objects.equals(responsibilityTag,
                CollisionDetectionResponsibilityTagEnum.PERSON_WITHOUT_RESPONSIBILITY.getDesc())) {
            // 3.1 云控VHR = 1 - 云控处置
            if (Objects.equals(telecontrolVHR, "=1")) {
                cloudTriageEvent.setOperatorType(EventOperatorTypeEnum.COCKPIT.getCode());
                cockpitHandleService.handleCollisionDetectionEvent(cloudTriageEvent);
            }
            // 3.2 云控VHR >1 || 不需要 - 分配近场处置
            else {
                cloudTriageEvent.setOperatorType(EventOperatorTypeEnum.NEARBY_RESCUE.getCode());
                nearbyRescueHandleService.handleCollisionDetectionEvent(cloudTriageEvent);
            }

        }
        // 4. 兜底 - 云安全处置
        else {
            log.info("handleCollisionDetection, cloudSecurityHandleService, cloudTriageEvent = {}", cloudTriageEvent);
            cloudTriageEvent.setOperatorType(EventOperatorTypeEnum.COCKPIT.getCode());
            cloudSecurityHandleService.handleCollisionDetectionEvent(cloudTriageEvent);
        }
    }

    private BizCloudTriageEvent PsEventDTOToBizCloudTriageEvent(PsEventDTO psEventDTO) {
        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
        String eventId = psEventDTO.getEventId();
        if (eventId != null) {
            cloudTriageEvent.setEventId(eventId);
        }
        //todo: 根据碰撞等级设置异常事件类型, 目前1001 和 1005 都承接碰撞检测事件，但是只有1005事件携带的分级字段有效
        Integer eventType = SmEventEnum.getTypeByCode(psEventDTO.getEventType());
        if (psEventDTO.getEventType().equals(SmEventEnum.COLLISION_DETECTION_GRADE.getCode())) {
            Integer accidentLevel = psEventDTO.getEventContentDTO().getAccidentLevel();
            eventType = AccidentLevelEnum.getEventTypeByLevel(accidentLevel);
        }
        cloudTriageEvent.setEventType(eventType);

        if(psEventDTO.getEventTimestamp() != null){
            cloudTriageEvent.setEventTime(new Date(psEventDTO.getEventTimestamp()));
        }

        String vin = psEventDTO.getVin();
        if(vin != null){
            cloudTriageEvent.setVin(vin);
        }
        //设置 VehicleId
        cloudTriageEvent.setVehicleId( psEventDTO.getVehicleId() != null? psEventDTO.getVehicleId() : psEventDTO.getVehicleName());

        if(eventId != null && vin != null){
            cloudTriageEvent.setInformationId(eventId.substring(0, eventId.lastIndexOf('_') + 1) + vin);
        }

        String latitude = Double.toString(psEventDTO.getEventContentDTO().getLatitude());
        cloudTriageEvent.setLatitude(latitude == null?"": latitude);

        String longitude = Double.toString(psEventDTO.getEventContentDTO().getLongitude());
        cloudTriageEvent.setLongitude(longitude == null?"":longitude);

        String recordName = psEventDTO.getEventContentDTO().getRecordName();
        cloudTriageEvent.setRecordName(recordName == null? "": recordName);

        return cloudTriageEvent;
    }

    private void startHDVideoSalvage(String vin, Date eventTime) throws KmsResultNullException, ParseException {

        //为什么不直接使用上文中的事故时间，因为该值与数据库值不统一，而前端是基于数据库中的值进行请求，因为这里读取数据库保持一致
        long accidentTime = DateUtil.roundAndConvertToSeconds(eventTime.getTime());
        //1)  请求环视视频  云端打捞视频是队列，先进先出，首先触发切换码率
        String bitrateChangePath = "/api/customer/v1/vehicle/cmd";
        String url = HostName + bitrateChangePath;
        //接口参数
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("type", "bitrate_change");
        requestParam.put("vin", vin);
        requestParam.put("data","");
        //BA鉴权参数
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("clientId", clientId);
        headerParam.put("clientSecret", clientSecret);
        headerParam.put("path", bitrateChangePath);
        headerParam.put("vin", vin);
        headerParam.put("type","bitrate_change");

        int code  = -1;
        try {
            String response = CommonUtil.doPostToSpiderCmdPath(url, requestParam, headerParam);
            JSONObject responseJson = JSONObject.parseObject(response);
            Gson gson = new Gson();
            code = gson.fromJson(responseJson.getString("code"), Integer.class);
            log.info("/api/customer/v1/vehicle/cmd, responseJson = {}", responseJson);

        } catch (Exception e) {
            log.error("/api/customer/v1/vehicle/cmd, request is failed", e);
            return;
        }
        log.info("accidentTime = {} is running!", accidentTime);
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(0), new Date().getTime() / 1000,
                Arrays.asList("v_loop"),
                true);

        //2）请求完整时间段的高清环视视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(0),
                accidentTime + videoRequestDuration.get(1), Arrays.asList("v_loop"), true);
        //3） 请求高清拼接视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(2), accidentTime, Arrays.asList("v_concat"),
                true);
        //4）请求完整时间段的高清拼接视频
        requestHDVideoByType(vin, accidentTime - videoRequestDuration.get(2),
                accidentTime + videoRequestDuration.get(3), Arrays.asList("v_concat"), true);
    }

    private void requestHDVideoByType(String vin, Long start_ts, Long end_ts, List<String> perspectives,
            Boolean immediateRetrieve) {
        VideoDTO videoDTO = new VideoDTO(
                vin,
                start_ts,
                end_ts,
                perspectives,
                immediateRetrieve);
        try {
            videoService.getHDVideo(videoDTO);
        } catch (Exception e) {
            log.error("/api/customer/v1/vehicle/cmd, request is failed", e);
        }
    }

    /**
     * 根据异常事件类型 + 碰撞等级确定异常类型
     *
     * @param psEventDTO
     * @return
     */
    private Integer getEventTypeByPsEvent(PsEventDTO psEventDTO) {
        // 保障系统异常类型 -> 云分诊异常类型
        SmEventEnum smEventEnum = SmEventEnum.getByCode(psEventDTO.getEventType());
        if (smEventEnum == null) {
            throw new IllegalArgumentException("psEventDTO.getEventType() is not expected");
        }
        Integer eventType = smEventEnum.getType();
        // 如果是碰撞检测分级信号则需要根据碰撞等级映射成不同的碰撞事件 1，24，26
        //todo: 根据碰撞等级设置异常事件类型, 目前1001 和 1005 都承接碰撞检测事件，但是只有1005事件携带的分级字段有效
        if (Objects.equals(psEventDTO.getEventType(), SmEventEnum.COLLISION_DETECTION_GRADE.getCode())) {
            // 根据异常类型 + 事故等级确定异常类型
            if (Objects.nonNull(psEventDTO.getEventContentDTO())
                    || Objects.nonNull(psEventDTO.getEventContentDTO().getAccidentLevel())) {
                eventType = AccidentLevelEnum.getEventTypeByLevel(psEventDTO.getEventContentDTO().getAccidentLevel());
            }
        }
        return eventType;
    }

    /**
     * 补充信息
     *
     * @param cloudTriageEvent
     */
    private void addExtraInfo(BizCloudTriageEvent cloudTriageEvent) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("vinList", cloudTriageEvent.getVin());
            List<VehicleRealtimeStatusDTO> resultList = informationApiService.callVehicleStatusService(param);
            if (!resultList.isEmpty()) {
                VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = resultList.get(0);
                //添加用车目的 以及 停车地点
                cloudTriageEvent.setPurpose(vehicleRealtimeStatusDTO.getPurpose());
                cloudTriageEvent.setPlace(vehicleRealtimeStatusDTO.getPark());
            }
        } catch (Exception e) {
            log.error("query information purpose and place error: {}", cloudTriageEvent);
        }
    }

    /**
     * 解析异常事件描述
     *
     * @param cloudTriageEvent
     * @param alert
     */
    private void parseAlertAndSetEventDesc(BizCloudTriageEvent cloudTriageEvent, String alert) {
        // 1 获取全量映射关系
        Map<String, Map<String, String>> alertReflect = externalInterfaceService.getEvaluationAlertsReflect();
        String collisionReason = "未知";
        if (alertReflect.containsKey(alert) && alertReflect.get(alert).containsKey("chinese_hint")) {
            collisionReason = alertReflect.get(alert).get("chinese_hint");
        }
        // 2 写入异常事件描述
        String remark = cloudTriageEvent.getRemark();
        if (!StringUtils.isEmpty(remark)) {
            JSONObject jsonObject = JSON.parseObject(remark);
            jsonObject.put("alert", collisionReason);
            cloudTriageEvent.setRemark(jsonObject.toJSONString());
        } else {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("alert", collisionReason);
            cloudTriageEvent.setRemark(jsonObject.toJSONString());
        }
    }

    /**
     * psEventDTO 转换为 BizCloudTriageEvent
     *
     * @param psEventDTO
     * @return
     */
    private BizCloudTriageEvent PsEventDTOToBizCloudTriageEventV2(PsEventDTO psEventDTO) {
        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
        String eventId = psEventDTO.getEventId();
        if (eventId != null) {
            cloudTriageEvent.setEventId(eventId);
        }

        Integer eventType = getEventTypeByPsEvent(psEventDTO);
        cloudTriageEvent.setEventType(eventType);

        if (psEventDTO.getEventTimestamp() != null) {
            cloudTriageEvent.setEventTime(new Date(psEventDTO.getEventTimestamp()));
        }

        String vin = psEventDTO.getVin();
        if (vin != null) {
            cloudTriageEvent.setVin(vin);
        }
        //设置 VehicleId
        cloudTriageEvent.setVehicleId(
                psEventDTO.getVehicleId() != null ? psEventDTO.getVehicleId() : psEventDTO.getVehicleName());

        if (eventId != null && vin != null) {
            cloudTriageEvent.setInformationId(eventId.substring(0, eventId.lastIndexOf('_') + 1) + vin);
        }

        String latitude = Double.toString(psEventDTO.getEventContentDTO().getLatitude());
        cloudTriageEvent.setLatitude(latitude == null ? "" : latitude);

        String longitude = Double.toString(psEventDTO.getEventContentDTO().getLongitude());
        cloudTriageEvent.setLongitude(longitude == null ? "" : longitude);

        String recordName = psEventDTO.getEventContentDTO().getRecordName();
        cloudTriageEvent.setRecordName(recordName == null ? "" : recordName);

        // 解析碰撞原因
        if (Objects.nonNull(psEventDTO.getEventContentDTO()) && StringUtils.isNotBlank(
                psEventDTO.getEventContentDTO().getEvaluationAlert())) {
            parseAlertAndSetEventDesc(cloudTriageEvent, psEventDTO.getEventContentDTO().getEvaluationAlert());
        }

        // 补充额外信息
        addExtraInfo(cloudTriageEvent);

        return cloudTriageEvent;
    }

}


