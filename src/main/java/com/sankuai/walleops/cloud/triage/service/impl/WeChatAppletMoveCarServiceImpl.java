package com.sankuai.walleops.cloud.triage.service.impl;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.walleops.cloud.triage.adaptor.DelivererQueryThriftServiceAdaptor;
import com.sankuai.walleops.cloud.triage.adaptor.eve.EVEAdaptor;
import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.component.LockService;
import com.sankuai.walleops.cloud.triage.component.ReportMoveCarEventProducer;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventOperatorTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.constant.VhrDescEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.RemarkDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.MoveCarRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.pojo.vo.VehicleMoveStatusVO;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.WeChatAppletMoveCarService;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class WeChatAppletMoveCarServiceImpl implements WeChatAppletMoveCarService {

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private DelivererQueryThriftServiceAdaptor delivererQueryAdaptor;

    @Resource
    private InformationApiService informationApiService;

    @Resource
    private LockService lockService;

    @Resource
    private ReportMoveCarEventProducer reportMoveCarEventProducer;

    @Resource
    private EVEAdaptor eveAdaptor;

    /**
     * 用户每天支持上报的最大数量
     */
    @MdpConfig("report.move.car.event.max.num")
    private Long reportMoveCarMaxNum = 5L;

    /**
     * 判断当前车辆是否存在工单时选取的有效时间(小时)
     */
    @MdpConfig("move.car.event.valid.hours")
    private Integer hours = 24;

    /**
     * 扫码挪车工单上报人
     */
    private static String REPORTER = "外部人员";

    /**
     * 根据车辆ID查询是否有未处理完成的工单
     *
     * @param vehicleId 车牌号
     * @return 如果有未处理完成的工单返回true，否则返回false
     */
    @Override
    public VehicleMoveStatusVO getMoveCarStatusByVehicleId(String vehicleId) throws Exception {
        // 1 校验车牌号
        String vin = delivererQueryAdaptor.getVinByVehicleId(vehicleId);
        if (StringUtils.isBlank(vin)) {
            // 车牌号校验失败直接返回
            return new VehicleMoveStatusVO();
        }
        VehicleMoveStatusVO vo = new VehicleMoveStatusVO();
        // 2 根据vehicleId 查询是否存在工单未处理完成的
        List<BizCloudTriageEvent> eventList = getUnprocessedMoveCarOrders(vin);
        log.info("getStatusByVehicleId, eventList = {}", eventList);
        vo.setVehicleId(vehicleId);
        vo.setIsMoving(CollectionUtils.isEmpty(eventList) ? false : true);
        return vo;
    }

    /**
     * 检查并创建挪车工单
     *
     * @param request 挪车请求对象
     * @return 创建工单的结果
     */
    @Override
    public CommonResponse checkAndCreateMoveCarOrder(MoveCarRequest request, String openId) {
        //1 统一转成大写
        String vehicleId = request.getVehicleId().toUpperCase();
        //2 车牌号校验, 调用basic服务接口查询是否有该车数据 -- 写redis
        String vin = delivererQueryAdaptor.getVinByVehicleId(vehicleId);
        if (StringUtils.isBlank(vin)) {
            log.error("车牌号[{}]校验失败,请重新填写", vehicleId);
            return CommonResponse.builder().ret(ResponseCodeEnum.CHECK_VEHICLE_ID_ERROR.getCode())
                    .msg("车牌号校验失败,请重新填写").build();
        }
        // 2 加锁防并发 - 车辆 , 获取锁失败则反馈创建成功
        boolean lockAcquired = false;
        try {
            // 尝试加锁创建工单
            lockAcquired = lockService.lock(CommonConstant.EVENT_INSERT_LOCK, vin, CommonConstant.LOCK_EXPIRE_SECONDS);
            if (!lockAcquired) {
                log.info("[checkAndCreateMoveCarOrder] 加锁失败， vin:{}", vin);
                return CommonResponse.success();
            }
            // 工单去重
            List<BizCloudTriageEvent> eventList = getUnprocessedMoveCarOrders(vin);
            log.info("getUnprocessedMoveCarOrders, eventList = {}", eventList);
            if (!CollectionUtils.isEmpty(eventList)) {
                return CommonResponse.builder().ret(ResponseCodeEnum.MOVE_CAR_REPEAT.getCode())
                        .msg("已有挪车工单").build();
            }
            // 挪车次数校验
            Long ordersNum = getMoveCarOrdersNumber(openId);
            if (ordersNum >= reportMoveCarMaxNum) {
                log.info("openId = {} 今日上报次数 {} 次超过最大值 {}", openId, ordersNum, reportMoveCarMaxNum);
                return CommonResponse.builder().ret(ResponseCodeEnum.REPORT_TIME_REACH_LIMIT.getCode())
                        .msg("今日上报数已达上线").build();
            }

            // 创建、存储工单实体
            BizCloudTriageEvent event = buildCloudEvent(request, vin, openId);
            int result = cloudTriageEventService.save(event);
            // 上报挪车事件到风险平台
            reportMoveCarEventProducer.reportMoveCarEvent(event, EventStatusEnum.INITED);

            return CommonResponse.success("ok");
        } catch (Exception e) {
            log.error("[checkAndCreateMoveCarOrder] event create failed, vin: {}. ", vin, e);
        } finally {
            // 只有在成功获取锁之后才尝试释放锁
            if (lockAcquired) {
                lockService.unLock(CommonConstant.EVENT_QU_LOCK, vin);
            }
        }
        return CommonResponse.failed("system error");
    }

    /**
     * 获取未处理的挪车工单列表
     *
     * @param vin 车辆识别码
     * @return 未处理的挪车工单列表
     */
    private List<BizCloudTriageEvent> getUnprocessedMoveCarOrders(String vin) {
        // 定义事件类型列表，仅包含公共挪车事件类型
        List<Integer> eventTypeList = Arrays.asList(EventTypeEnum.PUBLIC_REMOVAL.getCode());

        // 查询指定车辆在 hours 小时内未完成的挪车工单
        List<BizCloudTriageEvent> eventList = cloudTriageEventService.queryEventsByTimeVinStatus(vin,
                eventTypeList, EventStatusEnum.getUnCompletedStatus(),
                DateUtil.format(DateUtil.getBeforeTime(new Date(), TimeUnit.HOURS, hours)),
                DateUtil.format(new Date()),
                EventOperatorTypeEnum.CLOUD_SECURITY.getCode());

        // 记录日志，包含查询的车辆识别码、事件类型列表、时间范围和查询结果
        log.info("getEventIdList, vin = {},eventTypeList = {}, time = [{} - {}], eventList  ={}", vin,
                eventTypeList,
                DateUtil.format(DateUtil.getHeadOfToday()), DateUtil.format(new Date()), eventList);

        return eventList;
    }

    /**
     * 创建挪车事件工单
     *
     * @param request 挪车请求对象
     * @param vin     车辆识别码
     * @return 创建工单的结果
     */
    private BizCloudTriageEvent buildCloudEvent(MoveCarRequest request, String vin, String openId) {
        BizCloudTriageEvent event = new BizCloudTriageEvent();
        Date eventTime = new Date();
        event.setEventTime(eventTime);

        event.setStatus(EventStatusEnum.INITED.getCode());
        event.setReporter(REPORTER);
        event.setEventType(EventTypeEnum.PUBLIC_REMOVAL.getCode());
        event.setVin(vin);

        event.setVehicleId(request.getVehicleId());

        // 查询车辆实时状态
        VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = informationApiService.queryVehicleStatusByVin(vin);
        // 构建扩展字段信息
        RemarkDTO remarkDTO = buildRemarkDTO(request, openId, vehicleRealtimeStatusDTO, vin);
        event.setRemark(JacksonUtils.to(remarkDTO));
        // 获取车辆名称
        String vehicleName = "";
        // 获取用车目的和场地信息
        if (!Objects.isNull(vehicleRealtimeStatusDTO)) {
            event.setPurpose(vehicleRealtimeStatusDTO.getPurpose());
            event.setPlace(vehicleRealtimeStatusDTO.getPark());
            vehicleName = vehicleRealtimeStatusDTO.getName();
        }

        // 生成 eventID
        String eventId = CommonUtil.generateEventId(eventTime, EventTypeEnum.PUBLIC_REMOVAL.getEventName(),
                vehicleName);
        event.setEventId(eventId);

        log.info("[createCloudEvent] event create, event: {}", event);
        return event;
    }

    /**
     * 获取用户当天上报的挪车工单数量
     *
     * @param openId 用户唯一标识
     * @return 用户当天上报的挪车工单数量
     */
    private Long getMoveCarOrdersNumber(String openId) {
        // 1 查询当天所有的用户上报工单
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        request.setReporter(REPORTER);
        request.setStartTime(DateUtil.format(DateUtil.getHeadOfToday(), DateUtil.YMD_HMS_UNSIGNED));
        request.setEndTime(DateUtil.format(new Date(), DateUtil.YMD_HMS_UNSIGNED));
        List<BizCloudTriageEvent> eventList = cloudTriageEventService.pageQuery(request);
        log.info("getMoveCarOrdersNumber, request = {}, eventList = {}", request, eventList);
        // 2 直接使用流进行筛选和计数
        return eventList.stream()
                .filter(event -> {
                    String remark = event.getRemark();
                    if (StringUtils.isBlank(remark)) {
                        return false;
                    }
                    try {
                        RemarkDTO remarkDTO = JacksonUtils.from(remark, RemarkDTO.class);
                        return remarkDTO.getOpenId().equals(openId);
                    } catch (Exception e) {
                        log.error("Json format error", e);
                        return false;
                    }
                }).count();
    }

    /**
     * 构建挪车请求的备注信息DTO。 此方法主要用于将挪车请求的相关信息（如挪车原因、车辆位置、联系电话等）以及用户的openId封装成RemarkDTO对象。
     *
     * @param request 挪车请求对象，包含了挪车的详细信息。
     * @param openId  用户的唯一标识，用于追踪用户的挪车请求。
     * @return 封装好的RemarkDTO对象，包含了挪车请求的所有信息。
     */
    private RemarkDTO buildRemarkDTO(MoveCarRequest request, String openId,
            VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO, String vin) {
        // 设置基础信息
        // 将用户的唯一id(openId)放在扩展字段中，用处：检索用户的上报次数
        RemarkDTO remarkDTO = RemarkDTO.builder().openId(openId)
                .moveCarReason(request.getMoveCarReason())
                .carPosition(request.getCarPosition())
                .carPositionGps(request.getCarPositionGps())
                .urlList(request.getUrlList())
                .phoneNumber(request.getPhoneNumber()).build();
        // 获取vhr信息，以及云控安全员信息
        if (Objects.nonNull(vehicleRealtimeStatusDTO)) {
            Integer vhr = vehicleRealtimeStatusDTO.getIsVhrMultiple();
            //只有 vhr = 1 的车需要填写vhr\云控安全员信息
            if (Objects.nonNull(vhr) && Objects.equals(vhr, VhrDescEnum.VHR_EQUAL_ONE.getCode())) {
                remarkDTO.setVhrDesc(VhrDescEnum.VHR_EQUAL_ONE.getDesc());
                // 通过数据总线获取云控安全员信息
                VehicleStatusDTO vehicleStatusDTO = eveAdaptor.getVehicleStatusFromDataBusByVin(vin);
                log.info("getVehicleStatusFromDataBusByVin, vin = {}, vehicleStatusDTO = {}", vin, vehicleStatusDTO);
                if (Objects.nonNull(vehicleStatusDTO)) {
                    remarkDTO.setCloudSecurity(vehicleStatusDTO.getTelecontrol(new Date()));
                }
            }
        }
        return remarkDTO;

    }
}
