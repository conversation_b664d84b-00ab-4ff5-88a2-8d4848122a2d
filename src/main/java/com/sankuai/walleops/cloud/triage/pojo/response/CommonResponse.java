package com.sankuai.walleops.cloud.triage.pojo.response;

import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/8/24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonResponse {
    private Integer ret;
    private String msg;
    private Object data;

    public static CommonResponse success() {
        return CommonResponse.result(ResponseCodeEnum.SUCCESS);
    }

    public static CommonResponse success(Object data) {
        CommonResponse result = CommonResponse.result(ResponseCodeEnum.SUCCESS);
        result.setData(data);
        return result;
    }

    public static CommonResponse failed() {
        return CommonResponse.result(ResponseCodeEnum.FAILED);
    }

    public static CommonResponse failed(String msg) {
        return CommonResponse.result(ResponseCodeEnum.FAILED, msg);
    }

    public static CommonResponse failed(Object data) {
        CommonResponse result = CommonResponse.result(ResponseCodeEnum.FAILED);
        result.setData(data);
        return result;
    }

    public static CommonResponse result(ResponseCodeEnum responseCodeEnum) {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setRet(responseCodeEnum.getCode());
        commonResponse.setMsg(responseCodeEnum.getMsg());
        return commonResponse;
    }

    public static CommonResponse result(ResponseCodeEnum responseCodeEnum, String msg) {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setRet(responseCodeEnum.getCode());
        commonResponse.setMsg(msg);
        return commonResponse;
    }

    /**
     * 重新确认
     *
     * @param msg  响应信息
     * @return
     */
    public static CommonResponse reconfirm(String msg) {
        return CommonResponse.result(ResponseCodeEnum.RECONFIRM, msg);
    }
}
