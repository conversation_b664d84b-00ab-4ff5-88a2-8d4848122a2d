package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.pojo.dto.OperatorStatusDTO;
import com.sankuai.walleops.cloud.triage.service.impl.OperatorStatusServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
@Ignore
public class OperatorStatusServiceTest {

    @Resource
    private OperatorStatusServiceImpl operatorStatusService;

    @Before
    public void testSetOperatorStatus() {
        boolean result = operatorStatusService.setOperatorStatus("zhangsan", 1);
        
        Assert.assertEquals(result, true);
    }

    @Test
    public void testQueryOperatorStatus() {
        OperatorStatusDTO dto = operatorStatusService.queryOperatorStatus("zhangsan");
        Assert.assertNotNull(dto);
        Assert.assertEquals(dto.getMisId(), "zhangsan");
        Assert.assertEquals(dto.getStatus(), Integer.valueOf(1));
    }

    @After
    public void clear() {
        boolean result = operatorStatusService.delOperatorStatus("zhangsan");
        Assert.assertEquals(result, true);
    }
}
