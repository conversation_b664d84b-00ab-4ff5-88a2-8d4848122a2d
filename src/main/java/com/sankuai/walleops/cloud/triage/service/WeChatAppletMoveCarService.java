package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.pojo.request.MoveCarRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.pojo.vo.VehicleMoveStatusVO;
import com.sankuai.walleops.cloud.triage.util.exception.RemoteErrorException;

public interface WeChatAppletMoveCarService {
    VehicleMoveStatusVO getMoveCarStatusByVehicleId(String vehicleId) throws Exception;

    CommonResponse checkAndCreateMoveCarOrder(MoveCarRequest request, String openId) throws Exception;
}
