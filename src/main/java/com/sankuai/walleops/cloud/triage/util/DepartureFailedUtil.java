package com.sankuai.walleops.cloud.triage.util;

import com.sankuai.walleops.cloud.triage.pojo.po.DepartureFailedErrorSolution;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/9/16
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DepartureFailedUtil {
    public static final Map<String, DepartureFailedErrorSolution> ERROR_CODE_STRATEGY = new HashMap<>();

    public static final Map<String, List<DepartureFailedErrorSolution>> ERROR_CODE_MSG_LIKE_STRATEGY = new HashMap<>();

    static {
        ERROR_CODE_STRATEGY.put("02-005-0001",
                new DepartureFailedErrorSolution("02-005-0001", "车辆当前没有配送任务",
                        "请检查车辆是否有配送任务，若有任务请上报RE工单",
                        "将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-007-0004",
                new DepartureFailedErrorSolution("02-007-0004", "配送节点不存在",
                        "请检查车辆是否有配送任务，若有任务请上报RE工单",
                "将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-010-0001",
                new DepartureFailedErrorSolution("02-010-0001", "路由匹配失败",
                        "请上报 RE 工单",
                        "将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-010-0002",
                new DepartureFailedErrorSolution("02-010-0002", "路由下发超时",
                        "请重试 3 次，若未恢复请上报 RE 工单",
                        "1.确认车辆网络是否正常；2.请近场尝试挪动车辆后重试发车；3.如依然发车失败，联系应急组值班人。"));
        ERROR_CODE_STRATEGY.put("02-010-0003",
                new DepartureFailedErrorSolution("02-010-0003", "路由下发失败",
                        "请重试 3 次，若未恢复请上报 RE 工单",
                        "请将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-010-0006",
                new DepartureFailedErrorSolution("02-010-0006", "指定时间范围内车端生成路由失败",
                        "请重试 3 次，若未恢复请上报 RE 工单",
                        "1.确认车辆网络是否正常；2.请近场尝试挪动车辆后重试发车；3.如依然发车失败，联系应急组值班人。"));
        ERROR_CODE_STRATEGY.put("02-015-0002",
                new DepartureFailedErrorSolution("02-015-0002", "还有批次未装车",
                        "请在小程序刷新页面后点击「确认装车」，若仍未恢复请上报 RE 工单",
                        "将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-015-0003",
                new DepartureFailedErrorSolution("02-015-0003", "批次未绑定餐筐",
                        "请绑定餐箱，若仍未恢复请上报 RE 工单",
                        "将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-018-0010",
                new DepartureFailedErrorSolution("02-018-0010", "车云通信失败",
                        "请重试 3 次，若未恢复请上报 RE 工单",
                        "1.确认车辆网络是否正常；2.将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-018-0011",
                new DepartureFailedErrorSolution("02-018-0011", "获取不到车架号",
                        "请上报 RE 工单",
                        "请将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-018-0012",
                new DepartureFailedErrorSolution("02-018-0012", "已处于发车中",
                        "发车中，请稍后再试",
                        "1.请现场等待 30s 后重试；2.如果 30s 后车辆未发出，且重试依然产生此错误，请将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」。"));
        ERROR_CODE_STRATEGY.put("02-018-0014",
                new DepartureFailedErrorSolution("02-018-0014", "车云连接超时",
                        "请重试 3 次，若未恢复请上报 RE 工单",
                        "1.确认车辆网络是否正常；2.确认网络正常后引导现场重试；"));
        ERROR_CODE_STRATEGY.put("02-018-0097",
                new DepartureFailedErrorSolution("02-018-0097", "请求车端业务模块超时",
                        "请重试 3 次，请上报 RE 工单",
                        "发车请求超时，车机上未能体现真实结果；通过 发车链路追踪 (sankuai.com)工具，根据反馈的车号及时间，查看实际的发车结果；根据实际结果的错误码做对应的处理。如有疑问，请联系履约系统组-李文礼(liwenli06)。"));
        ERROR_CODE_STRATEGY.put("02-018-0098",
                new DepartureFailedErrorSolution("02-018-0098", "连接车端业务模块失败",
                        "请重试 3 次，若未恢复请上报 RE 工单",
                        "车端业务模块异常，将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-018-0099",
                new DepartureFailedErrorSolution("02-018-0099", "车端业务模块未知错误",
                        "请重试 3 次，若未恢复请上报 RE 工单",
                        "车端业务模块异常，将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
//        ERROR_CODE_STRATEGY.put("02-018-0100",
//                new DepartureFailedErrorSolution("02-018-0100", "获取不到RFID设备状态",
//                        "请上报 RE 工单",
//                        "1、请检查RFID设备指示灯，若蓝色指示灯不亮，请检查供电线路；若蓝色指示灯亮，请检查网线是否接触好；" +
//                                "2、若网线和电源线都确认链接好，请联系履约系统组-李文礼liwenli06。"));
        ERROR_CODE_STRATEGY.put("02-019-0001",
                new DepartureFailedErrorSolution("02-019-0001", "LiveExchange响应超时",
                        "请重试 3 次，请上报 RE 工单",
                        "1.确认车辆网络是否正常；2.请近场尝试挪动车辆后重试发车；3.如依然发车失败，联系应急组值班人。"));
        ERROR_CODE_STRATEGY.put("02-019-0002",
                new DepartureFailedErrorSolution("02-019-0002", "LiveExchange未知错误",
                        "请上报 RE 工单",
                        "LiveExchange 服务异常，请检查车辆是否在线，若确认车辆在线，请联系应急组值班人。"));
        ERROR_CODE_STRATEGY.put("02-019-0003",
                new DepartureFailedErrorSolution("02-019-0003", "LiveExchange车辆未在线",
                        "请重试 3 次，请上报 RE 工单",
                        "1.确认车辆网络是否正常；2.请近场尝试挪动车辆后重试发车；3.如依然发车失败，联系应急组值班人。"));
        ERROR_CODE_STRATEGY.put("02-019-0004",
                new DepartureFailedErrorSolution("02-019-0004", "LiveExchange鉴权失败",
                        "请上报 RE 工单",
                        "业务系统请求 LiveExchange 异常，车端业务模块异常，将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-019-0005",
                new DepartureFailedErrorSolution("02-019-0005", "LiveExchange不支持此指令",
                        "请上报 RE 工单",
                        "业务系统请求 LiveExchange 异常，车端业务模块异常，将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-019-0006",
                new DepartureFailedErrorSolution("02-019-0006", "LiveExchange响应结果不合法",
                        "请上报 RE 工单",
                        "LiveExchange 服务异常，请检查车辆是否在线，若确认车辆在线，请联系应急组值班人。"));

        ERROR_CODE_STRATEGY.put("02-021-0001",
                new DepartureFailedErrorSolution("02-021-0001", "启动自动驾驶失败",
                        "请重试 3 次，若未恢复请上报 RE 工单",
                        "1.使用 https://bi.sankuai.com/dashboard/114045报表查询「起自动失败原因」；2.引导现场或呼叫云控排除故障，并重试；3.如果无法排除故障，通知现场换车。"));
        ERROR_CODE_STRATEGY.put("02-021-0002",
                new DepartureFailedErrorSolution("02-021-0002", "车门未关闭",
                        "请关闭车门后重试",
                        "1.引导现场确认车门关闭后重试；2.如果确认门关闭的情况下发车产生此错误，且无法排除，请将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-021-0003",
                new DepartureFailedErrorSolution("02-021-0003", "急停",
                        "请解除急停后重试",
                        "1.确认车辆是否处于急停状态，引导现场解除急停后重试；2.如果确认车辆在非急停情况下产生此错误，且无法排除，请通知现场换车，并将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-021-0004",
                new DepartureFailedErrorSolution("02-021-0004", "切电中",
                        "请等待切电完成后重试",
                        "1.确认车辆是否处于切电状态（或产生问题的时间点是否切电中），引导现场等待切电完成后重试；2.如果确认车辆在非切电情况下产生此错误，且无法排除，请通知现场换车，并将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-021-0005",
                new DepartureFailedErrorSolution("02-021-0005", "磁盘满",
                        "磁盘满，请更换磁盘后重试",
                        "1.确认车辆是否磁盘满（或产生问题的时间点是否磁盘满），引导现场换盘后重试；2.如果确认车辆在非磁盘满情况下产生此错误，且无法排除，请通知现场换车，并将工单转到 RG 「智慧交通平台/无人车组/业务系统 - 问题反馈」"));
        ERROR_CODE_STRATEGY.put("02-021-0006",
                new DepartureFailedErrorSolution("02-021-0006", "启动自动驾驶超时",
                        "请重试 3 次，若未恢复请上报 RE 工单",
                        "1.使用 https://bi.sankuai.com/dashboard/114045报表查询「起自动失败原因」；2.引导现场或呼叫云控排除故障，并重试；3.如果无法排除故障，通知现场换车。"));

//        ERROR_CODE_STRATEGY.put("2004",
//                new DepartureFailedErrorSolution("2004", "live exchange处理失败",
//                        "联系研发工程师麻晔睿",
//                        "联系研发工程师麻晔睿"));
//        ERROR_CODE_STRATEGY.put("2005",
//                new DepartureFailedErrorSolution("2005", "未收到车端response",
//                        "联系研发工程师麻晔睿",
//                        "联系研发工程师麻晔睿"));
//        ERROR_CODE_STRATEGY.put("2006",
//                new DepartureFailedErrorSolution("2006", "车端处理失败",
//                        "联系研发工程师吕可馨",
//                        "联系研发工程师吕可馨"));


        ERROR_CODE_STRATEGY.put("NOT_READY_TO_ENGAGE",
                new DepartureFailedErrorSolution("NOT_READY_TO_ENGAGE",
                        "系统没有收到chassis、planning的消息；pnc进自动没有ready，接的engage_advice字段",
                        "", "请联系云控挪车后，在重试自动路由发车"));
        ERROR_CODE_STRATEGY.put("IN_TAKEOVER_AREA",
                new DepartureFailedErrorSolution("IN_TAKEOVER_AREA", "车辆在电子围栏区域内",
                        "", "请联系云控挪车，离开电子围栏后，在重试自动路由发车"));
//        ERROR_CODE_STRATEGY.put("LIDAR_POINT_CLOUD_QUALITY_ABNORMAL",
//                new DepartureFailedErrorSolution("LIDAR_POINT_CLOUD_QUALITY_ABNORMAL", "lidar点云异常",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("HARD_BRAKE_RISK",
//                new DepartureFailedErrorSolution("HARD_BRAKE_RISK",
//                        "车辆速度大于1.5m/s并且pnc规划的第一个轨迹点加速度小于-3.0，车辆这种情况下进自动会有急刹的风险",
//                        "", ""));
        ERROR_CODE_STRATEGY.put("DOOR_OPENED",
                new DepartureFailedErrorSolution("DOOR_OPENED", "车门开开了",
                        "", "车门未关闭，请关门后重新下发路由起步"));
//        ERROR_CODE_STRATEGY.put("ARBITRATION_MONITOR_ABNORMAL",
//                new DepartureFailedErrorSolution("ARBITRATION_MONITOR_ABNORMAL", "应急模块超时",
//                        "", ""));
        ERROR_CODE_STRATEGY.put("BATTERY_IS_SWITCHING",
                new DepartureFailedErrorSolution("BATTERY_IS_SWITCHING", "换电",
                        "", "车辆换电中，请稍后重试自动路由发车。"));
        ERROR_CODE_STRATEGY.put("CLOSE_TO_TAKE_OVER_AREA",
                new DepartureFailedErrorSolution("CLOSE_TO_TAKE_OVER_AREA", "距离电子围栏小于10米",
                        "", "请联系云控挪车，离开电子围栏后，在重试自动路由发车"));

//        ERROR_CODE_STRATEGY.put("STEER_DIFF_TO_LARGE",
//                new DepartureFailedErrorSolution("STEER_DIFF_TO_LARGE",
//                        "control规划的横向转角和实际车辆的转角差距过大",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("CPU_USAGE_PERCENT_TOO_HIGH",
//                new DepartureFailedErrorSolution("CPU_USAGE_PERCENT_TOO_HIGH",
//                        "cpu占用大",
//                        "", ""));
        ERROR_CODE_STRATEGY.put("DATA_DISK_USAGE_PERCENT_TOO_HIGH",
                new DepartureFailedErrorSolution("DATA_DISK_USAGE_PERCENT_TOO_HIGH",
                        "数据盘占用大",
                        "", "车辆存储空间不足，请呼叫云控处理，协调更换磁盘"));
//        ERROR_CODE_STRATEGY.put("GNSS_UNAVAILABLE",
//                new DepartureFailedErrorSolution("GNSS_UNAVAILABLE",
//                        "gnss imu延迟大；gnss imu时间戳异常",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("GPU_USAGE_PERCENT_TOO_HIGH",
//                new DepartureFailedErrorSolution("GPU_USAGE_PERCENT_TOO_HIGH",
//                        "GPU占用大",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("PERCEPTION_EMERGENCY",
//                new DepartureFailedErrorSolution("PERCEPTION_EMERGENCY",
//                        "感知模块异常",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("IMU_CALIBRATION_ABNORMAL",
//                new DepartureFailedErrorSolution("IMU_CALIBRATION_ABNORMAL",
//                        "IMU标定异常",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("LIDAR_CALIBRATION_ABNORMAL",
//                new DepartureFailedErrorSolution("LIDAR_CALIBRATION_ABNORMAL",
//                        "雷达标定异常",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("LIDAR_ABNORMAL",
//                new DepartureFailedErrorSolution("LIDAR_ABNORMAL",
//                        "雷达延迟高、点云数据乱序",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("LIDAR_MAIN_STATUS_ABNORMAL",
//                new DepartureFailedErrorSolution("LIDAR_MAIN_STATUS_ABNORMAL",
//                        "主雷达异常",
//                        "", ""));
        ERROR_CODE_STRATEGY.put("MAP_ACCESS_OUT_OF_RANGE",
                new DepartureFailedErrorSolution("MAP_ACCESS_OUT_OF_RANGE",
                        "在地图外",
                        "", "请联系云控挪车，进入高精地图范围，再重试自动路由发车"));
//        ERROR_CODE_STRATEGY.put("MAP_LOAD_TIMEOUT",
//                new DepartureFailedErrorSolution("MAP_LOAD_TIMEOUT",
//                        "地图加载超时",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("MEMORY_STATUS_ABNORMAL",
//                new DepartureFailedErrorSolution("MEMORY_STATUS_ABNORMAL",
//                        "内存异常",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("MEMORY_USAGE_PERCENT_TOO_HIGH",
//                new DepartureFailedErrorSolution("MEMORY_USAGE_PERCENT_TOO_HIGH",
//                        "内存占用过大",
//                        "", ""));
        ERROR_CODE_STRATEGY.put("OBSTACLE_COLLISION_DETECTTED",
                new DepartureFailedErrorSolution("OBSTACLE_COLLISION_DETECTTED",
                        "与障碍物发生碰撞事故",
                        "", "车辆发生疑似碰撞，请呼叫云控处理。"));
//        ERROR_CODE_STRATEGY.put("CHASSIS_LATENCY_TOO_HIGH",
//                new DepartureFailedErrorSolution("CHASSIS_LATENCY_TOO_HIGH",
//                        "canbus延迟高",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("LIDAR_FREQUENCY_ABNORMAL",
//                new DepartureFailedErrorSolution("LIDAR_FREQUENCY_ABNORMAL",
//                        "雷达频率异常",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("CONTROL_LATENCY_TOO_HIGH",
//                new DepartureFailedErrorSolution("CONTROL_LATENCY_TOO_HIGH",
//                        "control模块延迟高",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("LOCALIZATION_LATENCY_TOO_HIGH",
//                new DepartureFailedErrorSolution("LOCALIZATION_LATENCY_TOO_HIGH",
//                        "定位延迟高",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("BOUNDARY_COLLISION_DETECTTED",
//                new DepartureFailedErrorSolution("BOUNDARY_COLLISION_DETECTTED",
//                        "撞马路伢子",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("PERCEPTION_LATENCY_TOO_HIGH",
//                new DepartureFailedErrorSolution("PERCEPTION_LATENCY_TOO_HIGH",
//                        "感知延迟高",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("PLANNING_LATENCY_TOO_HIGH",
//                new DepartureFailedErrorSolution("PLANNING_LATENCY_TOO_HIGH",
//                        "Planing延迟高",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("TRAFFIC_LIGHT_BROKEN",
//                new DepartureFailedErrorSolution("TRAFFIC_LIGHT_BROKEN",
//                        "红绿灯坏了",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("TRAJECTORY_INVALID",
//                new DepartureFailedErrorSolution("TRAJECTORY_INVALID",
//                        "pnc规划的轨迹无效(轨迹点个数为0等情况)",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("CAMERA_ABNORMAL",
//                new DepartureFailedErrorSolution("CAMERA_ABNORMAL",
//                        "相机延迟高",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("GPU_TEMPERATURE_TOO_HIGH",
//                new DepartureFailedErrorSolution("GPU_TEMPERATURE_TOO_HIGH",
//                        "GPU温度高",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("LOCALIZATION_RELIABLE",
//                new DepartureFailedErrorSolution("LOCALIZATION_RELIABLE",
//                        "定位不可用",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("SATA_LINK_SPEED_STATUS_ABNORMAL",
//                new DepartureFailedErrorSolution("SATA_LINK_SPEED_STATUS_ABNORMAL",
//                        "数据盘速度异常",
//                        "", ""));
        ERROR_CODE_STRATEGY.put("REFERENCE_LINES_NOT_MATCH_ROUTING",
                new DepartureFailedErrorSolution("REFERENCE_LINES_NOT_MATCH_ROUTING",
                        "参考线路由不匹配",
                        "", "请重新下发自动路由，若依然报错，请联系云控处理。"));
//        ERROR_CODE_STRATEGY.put("PLANNING_FAR_FROM_LOCALIZATION",
//                new DepartureFailedErrorSolution("PLANNING_FAR_FROM_LOCALIZATION",
//                        "检查planning规划的轨迹点与自车之间的最短距离",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("CONTROL_INVALID",
//                new DepartureFailedErrorSolution("CONTROL_INVALID",
//                                "contro异常",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("UNSTABLE_PATH",
//                new DepartureFailedErrorSolution("UNSTABLE_PATH",
//                        "路径抖动",
//                        "", ""));
        ERROR_CODE_STRATEGY.put("IS_BUMPER_TRIGGER",
                new DepartureFailedErrorSolution("IS_BUMPER_TRIGGER",
                        "BUMPER触发",
                        "", "车辆发生疑似碰撞，请呼叫云控处理。"));
//        ERROR_CODE_STRATEGY.put("EPS_FAULT",
//                new DepartureFailedErrorSolution("EPS_FAULT",
//                        "主车失去与EPS通信",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("VCU_FAULT",
//                new DepartureFailedErrorSolution("VCU_FAULT",
//                                "VCU故障",
//                        "", ""));
//
//
//        ERROR_CODE_STRATEGY.put("FILED_EMERGENCY_BUTTON",
//                new DepartureFailedErrorSolution("FILED_EMERGENCY_BUTTON",
//                        "按了急停",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("WRONG_GEAR_POSITION",
//                new DepartureFailedErrorSolution("WRONG_GEAR_POSITION",
//                        "没挂N档或D档",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("SPEED_TOO_HIGHT",
//                new DepartureFailedErrorSolution("SPEED_TOO_HIGHT",
//                        "速度大于3.0m/s(允许无缝切换的最大速度)",
//                        "", ""));
//        ERROR_CODE_STRATEGY.put("TAKEOVER_BY_TELEOPERATION",
//                new DepartureFailedErrorSolution("TAKEOVER_BY_TELEOPERATION",
//                        "远摇踩了刹车",
//                        "", ""));


        List<DepartureFailedErrorSolution> routingErrorMsgLikeSolutions = new ArrayList<>();
        routingErrorMsgLikeSolutions.add(new DepartureFailedErrorSolution("ROUTING_ERROR",
                "Search route timeout in", "",
                "请您重试自动路由。若失败，间隔10秒继续重试。3次失败后，联系云控挪动重试。并在自动路由oncall群反馈。"));
        routingErrorMsgLikeSolutions.add(new DepartureFailedErrorSolution("ROUTING_ERROR",
                "Failed to search route with waypoint from",
                "1、地图不联通 2、点的类型不匹配，地图张静华，点高磊",
                "自动路由无法搜通，请您下发兜底静态路由发车。并在自动路由oncall群反馈。"));
        routingErrorMsgLikeSolutions.add(new DepartureFailedErrorSolution("ROUTING_ERROR",
                "Failed to refine routing request", "",
                "请您重试自动路由。若失败，间隔10秒继续重试。3次失败后，联系云控挪动重试。并在自动路由oncall群反馈。"));
        routingErrorMsgLikeSolutions.add(new DepartureFailedErrorSolution("ROUTING_ERROR",
                "Failed to merge route", "",
                "请您重试自动路由。若失败，间隔10秒继续重试。3次失败后，联系云控挪动重试。并在自动路由oncall群反馈。"));
        ERROR_CODE_MSG_LIKE_STRATEGY.put("ROUTING_ERROR", routingErrorMsgLikeSolutions);


        List<DepartureFailedErrorSolution> routingErrorRequestMsgLikeSolutions = new ArrayList<>();
        routingErrorRequestMsgLikeSolutions.add(new DepartureFailedErrorSolution("ROUTING_ERROR_REQUEST",
                "Routing request should have at least 2 waypoints",
                "", "自动路由无法搜通，请您下发兜底静态路由发车。并在自动路由oncall群反馈。"));
        routingErrorRequestMsgLikeSolutions.add(new DepartureFailedErrorSolution("ROUTING_ERROR_REQUEST",
                "Way node is not found in topo graph",
                "",
                "自动路由无法搜通，请您下发兜底静态路由发车。并在自动路由oncall群反馈。"));
        routingErrorRequestMsgLikeSolutions.add(new DepartureFailedErrorSolution("ROUTING_ERROR_REQUEST",
                "Black list node is not found in topo graph", "",
                "自动路由无法搜通，请您下发兜底静态路由发车。并在自动路由oncall群反馈。"));
        ERROR_CODE_MSG_LIKE_STRATEGY.put("ROUTING_ERROR_REQUEST", routingErrorRequestMsgLikeSolutions);


        List<DepartureFailedErrorSolution> routingErrorNotReadyMsgLikeSolutions = new ArrayList<>();
        routingErrorNotReadyMsgLikeSolutions.add(new DepartureFailedErrorSolution("ROUTING_ERROR_NOT_READY",
                "Navigator is not ready",
                "", "路由模块初始化失败，请重启脚本后再次起步"));
        routingErrorNotReadyMsgLikeSolutions.add(new DepartureFailedErrorSolution("ROUTING_ERROR_NOT_READY",
                "Failed to initialize navigator",
                "", "路由模块初始化失败，请重启脚本后再次起步"));
        ERROR_CODE_MSG_LIKE_STRATEGY.put("ROUTING_ERROR_NOT_READY", routingErrorNotReadyMsgLikeSolutions);


        List<DepartureFailedErrorSolution> initRoutingErrorResponseMsgLikeSolutions = new ArrayList<>();
        initRoutingErrorResponseMsgLikeSolutions.add(new DepartureFailedErrorSolution(
                "INIT_ROUTING_ERROR_RESPONSE",
                "Failed to get valid lane of nearest lane position",
                "", "请联系云控挪车后，在重试自动路由发车。"));
        ERROR_CODE_MSG_LIKE_STRATEGY.put("INIT_ROUTING_ERROR_RESPONSE", initRoutingErrorResponseMsgLikeSolutions);


        List<DepartureFailedErrorSolution> routingErrorResponseMsgLikeSolutions = new ArrayList<>();
        routingErrorResponseMsgLikeSolutions.add(new DepartureFailedErrorSolution("ROUTING_ERROR_RESPONSE",
                "Failed to result nodes",
                "",
                "请您重试自动路由。若失败，间隔10秒继续重试。3次失败后，联系云控挪动重试。并在自动路由oncall群反馈。"));
        routingErrorResponseMsgLikeSolutions.add(new DepartureFailedErrorSolution("ROUTING_ERROR_RESPONSE",
                "Failed to generate passage regions based on result lanes",
                "",
                "请您重试自动路由。若失败，间隔10秒继续重试。3次失败后，联系云控挪动重试。并在自动路由oncall群反馈。"));
        ERROR_CODE_MSG_LIKE_STRATEGY.put("ROUTING_ERROR_RESPONSE", routingErrorResponseMsgLikeSolutions);
    }

    public static String getSolutionByCodeAndMsg(String code, String msg) {
        DepartureFailedErrorSolution solution = ERROR_CODE_STRATEGY.get(code);
        if (solution != null) {
            String resolution = solution.getReSolution();
            return !StringUtils.isEmpty(resolution) ? resolution : solution.getFieldSolution();
        }
        List<DepartureFailedErrorSolution> solutions = ERROR_CODE_MSG_LIKE_STRATEGY.get(code);
        if (solutions != null && !StringUtils.isEmpty(msg)) {
            Optional<DepartureFailedErrorSolution> first =
                    solutions.stream().filter(item -> item.getErrorMsg().startsWith(msg)).findFirst();
            if (first.isPresent()) {
                DepartureFailedErrorSolution msgSolution = first.get();
                String resolution = msgSolution.getReSolution();
                return !StringUtils.isEmpty(resolution) ? resolution : msgSolution.getFieldSolution();
            }
        }
        return "请呼叫云控处理";
    }

}
