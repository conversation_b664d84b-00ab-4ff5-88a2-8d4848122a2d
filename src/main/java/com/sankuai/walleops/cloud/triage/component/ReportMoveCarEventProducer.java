package com.sankuai.walleops.cloud.triage.component;

import com.meituan.mafka.client.exception.MafkaCommonException;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.walleeve.domain.enums.MessageType;
import com.sankuai.walleeve.domain.enums.RiskCaseStatusEnum;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMessageDTO;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMessageExtInfoDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.RemarkDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import java.util.Arrays;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * 上报扫码挪车事件
 */
@Component
@Slf4j
public class ReportMoveCarEventProducer {

    @Autowired
    @Qualifier("moveCarEventProducer")
    private IProducerProcessor iProducerProcessor;

    /**
     * 挪车事件对应风险事件类型
     */
    private static final Integer MOVE_CAR_EVENT_RISK_CASE_TYPE = 4;

    /**
     * 上报挪车事件数据源
     */
    private static final Integer MOVE_CAR_EVENT_RISK_CASE_SOURCE = 4;

    /**
     * 发送消息
     *
     * @param riskCaseMessageDTO
     */
    public void sendMessage(RiskCaseMessageDTO riskCaseMessageDTO) {
        try {
            EveMqCommonMessage<RiskCaseMessageDTO> mqCommonMessage =
                    new EveMqCommonMessage<>(MessageType.RISK_CASE_MESSAGE.getCode(), riskCaseMessageDTO,
                            System.currentTimeMillis());
            log.info("ReportMoveCarEventProducer# sendMessage, mqCommonMessage = {}", mqCommonMessage);
            ProducerResult result = iProducerProcessor.sendMessage(JacksonUtils.to(mqCommonMessage));
            if (result.getProducerStatus().equals(ProducerStatus.SEND_FAILURE)) {
                throw new MafkaCommonException(
                        String.format("sendMessage error, riskCaseMessageDTO = %s", riskCaseMessageDTO));
            }
        } catch (Exception e) {
            log.error("ReportMoveCarEventProducer# sendMessage error", e);
        }
    }

    /**
     * 上报挪车事件
     *
     * @param event
     */
    public void reportMoveCarEvent(BizCloudTriageEvent event, EventStatusEnum statusEnum) {
        if (Objects.isNull(statusEnum)) {
            log.info("reportMoveCarEvent# statusEnum is null");
            return;
        }
        RiskCaseStatusEnum riskCaseStatusEnum = convertStatus(statusEnum);
        if (Objects.isNull(riskCaseStatusEnum)) {
            log.info("reportMoveCarEvent# riskCaseStatusEnum is null");
            return;
        }
        RiskCaseMessageDTO riskCaseMessageDTO = new RiskCaseMessageDTO();

        riskCaseMessageDTO.setEventId(event.getEventId());
        riskCaseMessageDTO.setType(MOVE_CAR_EVENT_RISK_CASE_TYPE);
        riskCaseMessageDTO.setSource(MOVE_CAR_EVENT_RISK_CASE_SOURCE);
        riskCaseMessageDTO.setStatus(riskCaseStatusEnum.getCode());
        riskCaseMessageDTO.setVinList(Arrays.asList(event.getVin()));

        RiskCaseMessageExtInfoDTO extInfoDTO = new RiskCaseMessageExtInfoDTO();
        RemarkDTO remarkDTO = JacksonUtils.from(event.getRemark(), RemarkDTO.class);
        if (Objects.nonNull(remarkDTO)) {
            extInfoDTO.setRiskEventDesc(remarkDTO.getMoveCarReason());
        }
        riskCaseMessageDTO.setExtInfo(JacksonUtils.to(extInfoDTO));
        sendMessage(riskCaseMessageDTO);
    }

    /**
     * 状态转换
     *
     * @param eventStatusEnum
     * @return
     */
    private RiskCaseStatusEnum convertStatus(EventStatusEnum eventStatusEnum) {
        switch (eventStatusEnum) {
            case INITED:
                return RiskCaseStatusEnum.NO_DISPOSAL;
            case HANDLING:
                return RiskCaseStatusEnum.IN_DISPOSAL;
            case COMPLETED:
            case CANCELED:
                return RiskCaseStatusEnum.DISPOSED;
        }
        return null;
    }
}
