package com.sankuai.walleops.cloud.triage.component.stratery;

import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component("trafficCongestion-alert-end")
@Slf4j
public class TrafficJamEndStrategy extends EventStrategy {

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Override
    public BizCloudTriageEvent transferEvent(EventDTO eventDTO, EventTypeEnum eventTypeEnum) {
        setAutoCancel(eventDTO.getEventId());
        return null;
    }

    /**
     * 自动取消
     *
     * @param eventId
     */
    public void setAutoCancel(String eventId) {
        try {
            //指定模糊查询的范围，避免全表查询
            Date minCreateTime = DateUtil.getBeforeTime(new Date(), TimeUnit.HOURS, 12);
            BizCloudTriageEvent bizCloudTriageEvent = cloudTriageEventService.queryLatestEventDetailByUniqueKey(
                    eventId, minCreateTime);
            if (bizCloudTriageEvent != null && Objects.equals(bizCloudTriageEvent.getStatus(),
                    EventStatusEnum.INITED.getCode())) {
                CloudTriageEventUpdateRequest cloudTriageEventUpdateRequest = new CloudTriageEventUpdateRequest();
                cloudTriageEventUpdateRequest.setId(bizCloudTriageEvent.getId());
                cloudTriageEventUpdateRequest.setStatus(EventStatusEnum.CANCELED.getCode());
                cloudTriageEventUpdateRequest.setOperator("system");
                cloudTriageEventService.update(cloudTriageEventUpdateRequest);
            }
        } catch (Exception e) {
            log.error("TrafficJamEndStrategy,setAutoCancel# eventId: {}", eventId, e);
        }
    }
}
