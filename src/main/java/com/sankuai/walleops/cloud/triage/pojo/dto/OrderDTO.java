package com.sankuai.walleops.cloud.triage.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/28
 */
@Data
public class OrderDTO {
    private String orderId;
    private Integer tripStatus;
    private Integer orderStatus;
    private String poiSeq;
    private String subWaybillId;
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date estimateTime;
    private Integer deliveryType;
    private String riderName;
    private String riderWaybillId;
    private Integer riderWaybillStatus;
    private String vinWaybillId;
    private Integer vinWaybillStatus;
    private String orderSource;
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date orderTime;
    private String recipientAddress;
    private String recipientName;
    private String recipientPhoneShort;
    private Integer phoneStatus;
    private Integer mssStatus;
    private Integer doorStatus;
}
