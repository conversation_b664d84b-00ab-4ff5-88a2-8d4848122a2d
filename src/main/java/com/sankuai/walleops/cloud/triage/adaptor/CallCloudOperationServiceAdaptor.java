package com.sankuai.walleops.cloud.triage.adaptor;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudOperationRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.ExternalSystemResponse;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 呼叫云控服务适配器
 */
@Slf4j
@Component
public class CallCloudOperationServiceAdaptor {

    /**
     * 呼叫云控操作url
     */
    private static final String CALL_CLOUD_OPERATION_URL = "/v1/cloud-operation/tasks";

    /**
     * 呼叫云控服务域名
     */
    @MdpConfig("cloud.operation.host.name")
    private String cloudOperationHostName;

    /**
     * 呼叫云控服务接口BA鉴权clientId
     */
    @MdpConfig("cloud.operation.interface.ba.clientId")
    private String clientId;

    /**
     * 呼叫云控服务接口BA鉴权clientSecret
     */
    @MdpConfig("cloud.operation.interface.ba.clientSecret")
    private  String clientSecret;

    /**
     * 向保障系统请求呼叫云控资源
     * @param cloudOperationRequest  呼叫云控请求体
     * @return  返回接口的响应信息
     */
    public ExternalSystemResponse requestCloudOperation(CloudOperationRequest cloudOperationRequest) throws Exception {
        String url = cloudOperationHostName + CALL_CLOUD_OPERATION_URL;
        // 1 填充BA鉴权参数
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("clientId", clientId);
        headerParam.put("clientSecret", clientSecret);
        headerParam.put("path", CALL_CLOUD_OPERATION_URL);

        // 2 向保障系统请求云控资源
        try {
            log.info("requestCloudOperation, requestParam = {}", cloudOperationRequest);
            String response = CommonUtil.doPostBA(url, JSONObject.toJSONString(cloudOperationRequest), headerParam);
            ExternalSystemResponse externalSystemResponse = JSONObject.parseObject(response,
                    ExternalSystemResponse.class);
            log.info("requestCloudOperation, response = {}", externalSystemResponse);
            if (externalSystemResponse == null || !Objects.equals(externalSystemResponse.getCode(), 200)) {
                throw new Exception("requestCloudOperation failed!");
            }
            return externalSystemResponse;
        } catch (Exception e) {
            log.error("requestCloudOperation failed!", e);
            throw new Exception(e);
        }
    }

}
