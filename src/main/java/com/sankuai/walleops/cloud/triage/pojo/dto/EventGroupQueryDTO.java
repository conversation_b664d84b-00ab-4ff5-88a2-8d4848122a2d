package com.sankuai.walleops.cloud.triage.pojo.dto;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/28
 */
@Data
public class EventGroupQueryDTO {
    private Long id;

    /**
     * 事件id
     */
    private String eventId;

    /**
     * 信息id
     */
    private String informationId;

    /**
     * record名称
     */
    private String recordName;

    /**
     * 车架号
     */
    private List<String> vinList;

    /**
     * 车辆id
     */
    private List<String> vehicleIdList;


    /**
     * 事件类型，-1表示未知，0表碰撞检测事件，1表示车辆停止不前，其他待补充
     */
    private Integer eventType;

    /**
     * 状态， 0表示未处理，1表示进行中，2表示完成，3表示取消
     */
    private Integer status;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 用车目的
     */
    private String purpose;

    /**
     * 场地
     */
    private String place;

    /**
     * 事件开始时间
     */
    private String startTime;

    /**
     * 事件结束事件
     */
    private String endTime;

    /**
     * 处置方
     */
    private Integer operatorType;
}
