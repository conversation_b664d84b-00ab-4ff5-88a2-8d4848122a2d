package com.sankuai.walleops.cloud.triage.thrift.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import java.io.Serializable;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 前端基础通用信息
 */
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
public class BasicFrontRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 用户ID
     */
    @FieldDoc(description = "用户ID")
    @Getter(onMethod_ = @ThriftField(value = 100))
    @Setter(onMethod_ = @ThriftField)
    private UserInfoVO ssoUserInfo;


    public String getSsoUserId() {
        return Optional.ofNullable(ssoUserInfo).map(UserInfoVO::getLogin).orElse("");
    }

}
