package com.sankuai.walleops.cloud.triage.proxy;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.squirrel.common.exception.StoreTimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

@Component
@Slf4j
public class SquirrelProxy {
    @Resource
    @Qualifier(value = "redisClient")
    private RedisStoreClient redisStoreClient;

    public boolean hset(String category, String key, String field, Object value) {
        StoreKey storeKey = new StoreKey(category, key);
        try {
            redisStoreClient.hset(storeKey, field, value);
        } catch (StoreTimeoutException e) {
            try {
                // 可能发生超时异常，重试一次
                redisStoreClient.hset(storeKey, field, value);
            } catch (Exception ex) {
                log.error("squirrel set failed, storeKey: {}, field: {}, value: {}", storeKey, field, value, ex);
                return false;
            }
        } catch (Exception e) {
            log.error("squirrel set failed, storeKey: {}, field: {}, value: {}", storeKey, field, value, e);
            return false;
        }

        return true;
    }

    public boolean hdel(String category, String key, String field) {
        StoreKey storeKey = new StoreKey(category, key);
        try {
            Long rmCount = redisStoreClient.hdel(storeKey, field);

            if (rmCount == 0) {
                return false;
            }
        } catch (StoreTimeoutException e) {
            try {
                // 可能发生超时异常，重试一次
                Long rmCount = redisStoreClient.hdel(storeKey, field);

                if (rmCount == 0) {
                    return false;
                }
            } catch (Exception ex) {
                log.error("squirrel delete failed, storeKey: {}, field: {}", storeKey, field, ex);
                return false;
            }
        } catch (Exception e) {
            log.error("squirrel delete failed, storeKey: {}, field: {}", storeKey, field, e);
            return false;
        }

        return true;
    }

    public <T> T hget(String category, String key, String field) {
        StoreKey storeKey = new StoreKey(category, key);

        try {
            T value = redisStoreClient.hget(storeKey, field);
            return value;
        } catch (StoreTimeoutException e) {
            try {
                // TimeoutException, retry once time
                T value = redisStoreClient.hget(storeKey, field);
                return value;
            } catch (Exception exception) {
                log.error("squirrel get failed, storyKey: {}", storeKey);
                return null;
            }
        } catch (Exception e) {
            log.error("squirrel get failed, storyKey: {}", storeKey);
            return null;
        }
    }

    public <T> Map<String, T> hgetall(String category, String key) {
        StoreKey storeKey = new StoreKey(category, key);

        try {
            Map<String, T> allField = redisStoreClient.hgetAll(storeKey);
            return allField;
        } catch (StoreTimeoutException e) {
            try {
                // 可能发生超时异常，重试一次
                Map<String, T> allField = redisStoreClient.hgetAll(storeKey);
                return allField;
            } catch (Exception ex) {
                log.error("squirrel get failed, storeKey: {}", storeKey);
                return Collections.emptyMap();
            }
        } catch (Exception e) {
            log.error("squirrel get failed, storyKey: {}", storeKey);
            return null;
        }
    }
}
