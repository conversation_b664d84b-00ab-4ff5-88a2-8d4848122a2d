package com.sankuai.walleops.cloud.triage.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ManualReportEventTypeEnum {

    //    VEHICLE_STANDSTILL(0, "vehicle-standstill", "车辆停滞不前",2007),
    DEPARTURE_FAILED(2, "departure-failed", "信号不佳，移至门口发车", 2008),
    COREDUMP(8, "coredump", "恢复行驶", 2010),
    //    BATTERY_REPLACE_STOP(16, "battery-replace-stop", "电池需换电请停车", 2014),
    ROADBLOCKAGE_MOVEVEHICLE(17, "roadBlockage-MoveVehicle", "堵路需挪车", 2009),
    AUTOPARKING_NOT_MEETEXPECTATION(18, "autoParking-MeetExpectation", "自动泊车不符合预期", 2012),
    PARKINGPOSITIONNOTREASONABLE_MOVEVEHICLE(19, "parkingPosition-MoveVehicle", "车辆停靠位置不合理需挪车", 2013),
    RIDERHOLDGOODS_PARKING(20, "riderHoldGoods-Parking", "骑手未取完货需停车", 2011),
    //    ROUTING_ISSUE(22,"routing-issue", "路由问题",null),
    ORDER_COMPLETED_RETURN(27, "order-completed-return", "订单完成可以返回", 2015),
    VEHICLE_NO_PARK_LONG_TIME(28, "vehicle-not-parked-long-time", "车辆长时间没有驻车", 2016),
    CLOSE_CLEANING_SENSOR(29, "close-cleaning-sensor", "关闭清洁传感器", 2017),
    VIRTUAL_ORDER_DEPARTURE(30, "virtual-order-departure", "虚拟单发车", null),
    //    ROAD_TEST_DEPARTURE(31,"road-test-departure","路测发车", 2018),
    AUXILIARY_ROAD_TO_MAIN_ROAD(33, "auxiliary-road-to-main-road", "辅路车借道主路", 2201),
    MAIN_ROAD_TO_AUXILIARY_ROAD_RETURN(34, "main-road-to-auxiliary-road-return", "主路车误入辅路后返回", 2202),
    RESUME_ROUTE_AFTER_RETURN(35, "resume-route-after-return", "掉头后恢复路由", 2203),
    PULL_OVER_AND_EXIT_CONTROL(36, "pull-over-and-exit-control", "靠边停车退控", 2204),
    SCAN_CODE_TO_LIFT_BARRIER(37, "scan-code-to-lift-barrier", "扫码抬杆", 2205),

    ;

    private final Integer code;
    private final String eventName;
    private final String msg;
    /**
     * 异常时间对应呼叫云控任务的code码
     */
    private final Integer callCode;

    /**
     * 根据 code 查询 eventName
     * @param code
     * @return
     */
    public static String getEventNameByCode(Integer code) {
        for (ManualReportEventTypeEnum value : ManualReportEventTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getEventName();
            }
        }
        return CommonConstant.UNKNOWN;
    }

    /**
     * 根据 code 查询 呼叫云控code码
     * @param code
     * @return
     */
    public static Integer getCallCodeByCode(Integer code) {
        for (ManualReportEventTypeEnum value : ManualReportEventTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getCallCode();
            }
        }
        return null;
    }
}
