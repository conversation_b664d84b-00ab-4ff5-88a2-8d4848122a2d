package com.sankuai.walleops.cloud.triage.controller;

import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperateHistoryPageDTO;
import com.sankuai.walleops.cloud.triage.pojo.request.OperateHistoryPageRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.OperateHistoryRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.service.OperateHistoryService;
import com.sankuai.walleops.cloud.triage.util.InputCheckUtil;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/9/19
 */
@RestController
@RequestMapping(value = "/operate/history")
@Slf4j
public class OperateHistoryController {
    @Resource
    private OperateHistoryService operateHistoryService;

    @PostMapping(value = "/add")
    public CommonResponse addOperateHistory(@RequestBody OperateHistoryRequest request) {
        if (StringUtils.isEmpty(request.getOperator())) {
            User user = UserUtils.getUser();
            request.setOperator(user != null ? user.getLogin() : null);
        }
        int result = operateHistoryService.addOperateHistory(request);
        if (result == 0) {
            return CommonResponse.failed();
        }
        return CommonResponse.success();
    }

    @GetMapping("/list")
    public CommonResponse queryOperateList(OperateHistoryPageRequest request) {
        log.info("查询操作记录, request: {}", request);

        if(StringUtils.isEmpty(request.getEventId()) && StringUtils.isEmpty(request.getVin())) {
            log.info("查询操作记录, vin和eventId不能同时为空");
            CommonResponse response = new CommonResponse();
            response.setRet(ResponseCodeEnum.BAD_REQUEST.getCode());
            response.setMsg("vin和eventId不能同时为空");
            return response;
        }

        try {
            OperateHistoryPageDTO pageDTO = operateHistoryService.queryOperateHistoryList(request);
            log.info("查询操作记录, result: {}", pageDTO);
            CommonResponse response = CommonResponse.success(pageDTO);
            return response;
        } catch (Exception e) {
            log.error("查询操作记录, 查询失败", e);
            CommonResponse response = CommonResponse.failed();
            return response;
        }
    }

    @GetMapping("/list/v2")
    public CommonResponse queryOperateListV2(OperateHistoryPageRequest request) {
        log.info("查询操作记录, request: {}", request);

        if (StringUtils.isEmpty(request.getEventId()) && StringUtils.isEmpty(request.getVin())) {
            log.info("查询操作记录, vin和eventId不能同时为空");
            CommonResponse response = new CommonResponse();
            response.setRet(ResponseCodeEnum.BAD_REQUEST.getCode());
            response.setMsg("vin和eventId不能同时为空");
            return response;
        }

        try {
            InputCheckUtil.isNotNull(request.getStartTime(), "startTime不能为空");
            InputCheckUtil.isNotNull(request.getEndTime(), "endTime不能为空");
            OperateHistoryPageDTO pageDTO = operateHistoryService.queryOperateHistoryList(request);
            log.info("查询操作记录, result: {}", pageDTO);
            CommonResponse response = CommonResponse.success(pageDTO);
            return response;
        } catch (Exception e) {
            log.error("查询操作记录, 查询失败", e);
            CommonResponse response = CommonResponse.failed();
            return response;
        }
    }
}
