package com.sankuai.walleops.cloud.triage.pojo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DataBusGetRequest {
    /**
     * 业务方，全部则为all（key代表需要默写特定业务方数据）
     */
    String key = "all";

    /**
     * 大写车架号列表
     */
    @JsonProperty("vin_list")
    List<String> vinList;
}
