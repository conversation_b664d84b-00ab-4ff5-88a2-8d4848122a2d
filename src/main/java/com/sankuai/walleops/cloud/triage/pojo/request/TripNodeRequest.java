package com.sankuai.walleops.cloud.triage.pojo.request;

import lombok.Data;


/**
 * <AUTHOR>
 * @date 2022/8/28
 */
@Data
public class TripNodeRequest extends RangeTimePageRequest {
    private String misId;
    private String clientIp;
    private String vin;
    private String tripId;
    private String tripNodeId;
    private String stationCode;
    /**
     * 0:已创建, 100:配送中, 200:已完成, 201:超时完成, 300:已取消
     */
    private Integer tripStatus;
    /**
     * 0: 已生成, 100:去往该点, 200:已到达, 300:已离开, 400:取消
     */
    private Integer tripNodeStatus;
}
