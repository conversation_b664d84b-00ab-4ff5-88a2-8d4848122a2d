package com.sankuai.walleops.cloud.triage.controller;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.VideoDTO;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.pojo.response.VideoReponse;
import com.sankuai.walleops.cloud.triage.service.VideoService;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import com.google.gson.Gson;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping(value = "/videos")
@Slf4j
public class VideoController {
    @Resource
    VideoService videoService;

    @PostMapping("/all")
    public CommonResponse getVideosByVin(@RequestBody VideoDTO videoDTO){
        log.info("videos/all, videoDTO = {}", videoDTO);
        CommonResponse commonResponse = new CommonResponse();

        VideoReponse videoReponse =  videoService.getHDVideo(videoDTO);

        commonResponse.setRet(0);
        commonResponse.setData(videoReponse);

        return commonResponse;
    }
}
