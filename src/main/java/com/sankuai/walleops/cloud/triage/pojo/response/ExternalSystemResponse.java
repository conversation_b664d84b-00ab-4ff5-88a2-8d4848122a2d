package com.sankuai.walleops.cloud.triage.pojo.response;

import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExternalSystemResponse {

    /**
     * 错误码
     */
    private Integer code;
    /**
     * msg
     */
    private String msg;

    /**
     * data
     */
    private Object data;

    /**
     * 成功响应方法
     * @param data 响应数据
     * @return 外部系统响应对象
     */
    public static ExternalSystemResponse success(Object data) {
        ExternalSystemResponse response = ExternalSystemResponse.builder().code(ResponseCodeEnum.SUCCESS.getCode())
                .data(data).build();

        return response;
    }

    /**
     * 失败响应方法
     * @param msg 失败消息
     * @return 外部系统响应对象
     */
    public static ExternalSystemResponse failed(String msg) {
        ExternalSystemResponse response = ExternalSystemResponse.builder().code(ResponseCodeEnum.FAILED.getCode())
                .msg(msg).build();

        return response;
    }

}
