package com.sankuai.walleops.cloud.triage.pojo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/9/19
 */
@Data
public class OperateHistoryRequest {
    /**
     * 车架号
     */
    private String vin;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 车辆id
     */
    private String vehicleId;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 操作内容描述
     */
    private String description;

    /**
     * 操作时间
     */
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date operateTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作响应结果
     */
    private String operateResponse;

    /**
     * 关联的事件id
     */
    private String relatedEventId;

}
