package com.sankuai.walleops.cloud.triage.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/24
 */
@Data
public class CloudTriageEventVO {
    private Long id;

    /**
     * 事件id
     */
    private String eventId;

    /**
     * record名称
     */
    private String recordName;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 车辆id
     */
    private String vehicleId;

    /**
     * 事件发生时间
     */
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date eventTime;

    /**
     * 事件类型，-1表示未知，0表碰撞检测事件，1表示车辆停止不前，其他待补充
     */
    private Integer eventType;

    /**
     * 事件描述
     */
    private String eventDesc;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 状态， 0表示未处理，1表示进行中，2表示完成，3表示取消
     */
    private Integer status;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 处理开始时间
     */
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date operateStartTime;

    /**
     * 处理结束时间
     */
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date operateEndTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用车目的
     */
    private String purpose;

    /**
     * 场地
     */
    private String place;

    /**
     * 持续分钟数
     */
    private Long durationMinute;

    /**
     * 应对方法
     */
    private String solution;

    /**
     * create_time
     */
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date createTime;

    /**
     * update_time
     */
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date updateTime;

    // 上报人
    private String reporter;
}
