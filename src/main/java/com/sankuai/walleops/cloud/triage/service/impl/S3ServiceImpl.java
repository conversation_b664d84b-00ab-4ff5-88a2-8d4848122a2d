package com.sankuai.walleops.cloud.triage.service.impl;

import com.meituan.image.client.ImageUploadClient;
import com.meituan.image.client.impl.ImageUploadClientImpl;
import com.meituan.image.client.pojo.ImageResult;
import com.sankuai.walleeve.utils.HMacUtils;
import com.sankuai.walleops.cloud.triage.pojo.vo.NormalUploadSignVO;
import com.sankuai.walleops.cloud.triage.service.S3Service;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class S3ServiceImpl implements S3Service {

    /**
     * s3访问秘钥
     */
    @Value("$KMS{s3plus_service_access_key}")
    private String s3AccessKey;

    /**
     * s3加密秘钥
     */
    @Value("$KMS{s3plus_service_secret_key}")
    private String s3SecretKey;

    private static final String MAC_NAME = "HmacSHA1";

    private static final String ENCODING = "UTF-8";

    /**
     * 该方法位于某Service类中，返回一个封装好的Response实体类，该实体类只有AWSAccessKeyId、签名Signature、加签时间Date三项属性： 表单直传方式 获取表单直传加签函
     * 参考JS：https://km.sankuai.com/page/373900930
     *
     * @return
     */
    @Override
    public NormalUploadSignVO getNormalUploadSign(String fileName) throws Exception {
        String bucketName = "cloudtriage-movecar";
        String accessKey = s3AccessKey;
        String secretKey = s3SecretKey;
        String expiration;
        String signature;
        String policyEncoded;
        String policy;

        // 将date转换为指定格式并设置过期时间为1天后（这个过期时间可以根据自己业务需求自定，应该在满足自己业务需求的前提下尽量小，才更安全）
        Date date = new Date();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 1);
        date = calendar.getTime();
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        // 格式如"2018-12-01T00:00:00Z"
        expiration = df.format(date);
        policy = String.format(
                "{\"expiration\":\"%s\",\"conditions\":[{\"bucket\":\"%s\"},[\"starts-with\",\"$key\",\"\"]]}",
                expiration, bucketName);
        policyEncoded = Base64.getEncoder().encodeToString(policy.getBytes(StandardCharsets.UTF_8));
        try {
            signature = Base64.getEncoder().encodeToString(HMacUtils.encrypt(policyEncoded, secretKey));
        } catch (Exception e) {
            log.error("getNormalUploadSign - 签名生成异常信息 {}, 异常堆栈信息 {}", e.getMessage(), e);
            throw new Exception(e);
        }

        NormalUploadSignVO normalUploadSignResp = new NormalUploadSignVO();
        normalUploadSignResp.setSignature(signature);
        normalUploadSignResp.setPolicy(policyEncoded);
        normalUploadSignResp.setAWSAccessKeyId(accessKey);
        // 如果不传key给前端，前端会默认把文件名作为key，这有可能会引发s3同名覆盖问题。
        // 前端也可以在上传过程中填写key这个可选项。
        normalUploadSignResp.setKey(
                fileName +"_" + DateUtil.format(new Date(), DateUtil.YMD) + "_" + (new Date()).getTime());

        return normalUploadSignResp;
    }
}
