package com.sankuai.walleops.cloud.triage.component;

import com.meituan.mafka.client.exception.MafkaCommonException;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventMessageDTO;
import com.sankuai.walleops.cloud.triage.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OutputEventMsgProducer {

    @Autowired
    @Qualifier("eventMsgProducer")
    private IProducerProcessor eventMsgProducer;

    /**
     * 发送消息
     *
     * @param messageDTO MQ消息结构体
     */
    public void sendMessage(EventMessageDTO messageDTO) {
        try {
            log.info("sendMessage, EventMessageDTO = {}", messageDTO);
            ProducerResult result = eventMsgProducer.sendMessage(JsonUtil.toJson(messageDTO));
            if (result.getProducerStatus().equals(ProducerStatus.SEND_FAILURE)) {
                throw new MafkaCommonException(String.format("sendMessage error, messageDTO = %s", messageDTO));
            }
        } catch (Exception e) {
            log.error("eventMsgProducer# sendMessage error", e);

        }
    }
}
