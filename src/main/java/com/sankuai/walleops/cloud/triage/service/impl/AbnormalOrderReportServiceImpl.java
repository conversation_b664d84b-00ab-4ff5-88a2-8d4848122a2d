package com.sankuai.walleops.cloud.triage.service.impl;

import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.walleops.cloud.triage.adaptor.CallCloudOperationServiceAdaptor;
import com.sankuai.walleops.cloud.triage.adaptor.eve.EVEAdaptor;
import com.sankuai.walleops.cloud.triage.component.ReportToDaXiangService;
import com.sankuai.walleops.cloud.triage.constant.ActionEnum;
import com.sankuai.walleops.cloud.triage.constant.CharConstant;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.ManualReportEventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.OperationChangeTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.OrderOperationTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.VehicleStatusEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.NotificationMessageDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperationLogDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.StatusCheckResultDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudOperationRequest;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.ExternalSystemResponse;
import com.sankuai.walleops.cloud.triage.service.AbnormalOrderReportService;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.OperationChangeLogService;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import com.sankuai.walleops.cloud.triage.util.JsonUtil;
import com.sankuai.walleops.cloud.triage.util.exception.RemoteErrorException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.InputMismatchException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class AbnormalOrderReportServiceImpl implements AbnormalOrderReportService {

    @Resource
    ReportToDaXiangService reportToDaXiangService;

    @Resource
    EVEAdaptor eveAdaptor;

    @Autowired
    CloudTriageEventService cloudTriageEventService;

    @Resource
    private CallCloudOperationServiceAdaptor cloudControlServiceAdaptor;

    @Resource
    private OperationChangeLogService operationChangeLogService;

    /**
     * 交由人工(分诊值班角色)处置的异常事件列表
     */
    @ConfigValue(key = "cloud.operation.event.type.set")
    private Set<Integer> cloudOperationEventTypeSet;

    /**
     * 上报分诊异常工单工逻辑灰度开关
     */
    @ConfigValue(key = "report.csm.order.gray.event.type.set")
    private Set<Integer> reportCSMOrderGrayEventTypeSet;

    /**
     * 上报分诊异常工单工逻辑灰度开关
     */
    @ConfigValue(key = "report.csm.order.gray.vin.set")
    private Set<String> reportCSMOrderGrayVinSet;


    /**
     * 云分诊车辆详情页链接
     */
    private static final String CSM_URL = "https://walle.sankuai.com/m/csm/event?vin=";

    /**
     * 当工单状态积发生变更时通知上报人
     *
     * @param reporter
     * @param notificationMessageDTO
     */
    @Override
    public void sendProcessMessageToReporter(String reporter, NotificationMessageDTO notificationMessageDTO) {
        try {
            String notificationMessageContent = buildNotificationMessage(notificationMessageDTO);
            Map<String, Object> map = new HashMap<>();
            map.put("text", notificationMessageContent);
            reportToDaXiangService.sendToReporter(JsonUtil.toJson(map), reporter);
        } catch (Exception e) {
            log.error("sendProcessMessageToReporter# error, content = {}, reporter = {}", notificationMessageDTO,
                    reporter, e);
        }
    }

    /**
     * 校验车辆状态并输出结果、提示文案
     * 
     * @param vin
     * @return
     */
    @Override
    public StatusCheckResultDTO validateVehicleStatusAndGenerateResult(String vin) {

        List<VehicleStatusDTO> vehicleStatusDTOList = eveAdaptor.getVehicleStatusByDataBus(Arrays.asList(vin));
        if (CollectionUtils.isEmpty(vehicleStatusDTOList)) {
            return StatusCheckResultDTO.builder().result(true).build();
        }
        VehicleStatusDTO vehicleStatusDTO = vehicleStatusDTOList.get(0);
        // 维护当前车辆状态
        List<VehicleStatusEnum> vehicleStatusEnumList = new ArrayList<>();
        // 判断车辆是否在线
        if (!vehicleStatusDTO.isVehicleOnline()) {
            vehicleStatusEnumList.add(VehicleStatusEnum.OFFLINE);
        }
        // 是否存在事故工单
        if (vehicleStatusDTO.hasAccidentOrder()) {
            vehicleStatusEnumList.add(VehicleStatusEnum.ACCIDENT);
        }
        // 是否存在救援工单
        if (vehicleStatusDTO.hasRescueOrder()) {
            vehicleStatusEnumList.add(VehicleStatusEnum.RESCUE);
        }
        // 是否存在维修工单
        if (vehicleStatusDTO.hasMaintenanceOrder()) {
            vehicleStatusEnumList.add(VehicleStatusEnum.MAINTAIN);
        }
        // 是否连接云控
        if (vehicleStatusDTO.hasRemoteControlConnected()) {
            vehicleStatusEnumList.add(VehicleStatusEnum.CLOUD_CONTROL);
        }
        // 当车辆状态符合校验条件时，返回前端展示文本
        if (!CollectionUtils.isEmpty(vehicleStatusEnumList)) {
            return StatusCheckResultDTO.builder().result(false).noticeTxt(buildFrontEndNoticeTxt(vehicleStatusEnumList))
                    .build();
        }

        return StatusCheckResultDTO.builder().result(true).build();
    }

    /**
     * 分诊异常工单入库/记录操作日志/呼叫云控 --- 事务性
     *
     * @param request
     * @param vehicleRealtimeStatusDTO
     * @return
     * @throws Exception
     */
    @Override
    public Integer insertEventOrder(CloudTriageEventUpdateRequest request,
            VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO) throws Exception {

        // 1 数据格式转换
        BizCloudTriageEvent cloudTriageEvent = convertToBizCloudTriageEvent(request, vehicleRealtimeStatusDTO);
        log.info("convertToBizCloudTriageEvent# cloudTriageEvent = {}", cloudTriageEvent);

        // 2 插入数据，呼叫云控(获取当前代理类调用事务接口)
        int result = ((AbnormalOrderReportService) AopContext.currentProxy()).insertOrderAndCallCloudOperation(cloudTriageEvent);

        // 判断上报异常事件类型是否进行灰度
        // 只有插入工单数据成功,然后记录流水
        if (result > 0 && isReportCSMOrder(cloudTriageEvent.getEventType(), cloudTriageEvent.getVin())) {
            try{
                // 3 记录流水
                operationChangeLogService.insertOperationLog(OperationLogDTO.builder().relatedId(cloudTriageEvent.getId())
                        .changeType(OperationChangeTypeEnum.CREATE_ORDER.getType())
                        .changeContent(CommonUtil.getChangeDiff(null, cloudTriageEvent))
                        .changeDesc(JsonUtil.toJson(validateVehicleStatusAndGenerateResult(cloudTriageEvent.getVin())))
                        .status(EventStatusEnum.INITED.getCode())
                        .operator(cloudTriageEvent.getReporter()).build());

                // 4 发送通知
                sendProcessMessageToReporter(cloudTriageEvent.getReporter(),
                        NotificationMessageDTO.builder()
                                .vehicleName(getVehicleNameFromEventId(cloudTriageEvent.getEventId()))
                                .vehicleId(cloudTriageEvent.getVehicleId())
                                .operationTime(cloudTriageEvent.getEventTime())
                                .eventType(cloudTriageEvent.getEventType())
                                .action(ActionEnum.determineAction(cloudTriageEvent.getStatus(), cloudTriageEvent.getStatus()))
                                .status(cloudTriageEvent.getStatus()).vin(cloudTriageEvent.getVin())
                                .operationMisId(cloudTriageEvent.getReporter()).build());
            }
            catch (Exception e){
                log.error("insertOperationLog or sendProcessMessageToReporter failed", e);
            }
        }
        return result;
    }


    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    @Override
    public int insertOrderAndCallCloudOperation(BizCloudTriageEvent cloudTriageEvent) throws Exception {
        // 1 工单入库
        int result = cloudTriageEventService.save(cloudTriageEvent);

        // 设置灰度开关
        if (isReportCSMOrder(cloudTriageEvent.getEventType(), cloudTriageEvent.getVin())) {
            // 2 只有数据插入成功后，呼叫云控（添加对 result的判断）
            if (result > 0 && cloudTriageEvent.getOperationType()
                    .equals(OrderOperationTypeEnum.AUTOMATIC_CALL_CLOUD_CONTROL.getType())) {
                ExternalSystemResponse response = cloudControlServiceAdaptor
                        .requestCloudOperation(CloudOperationRequest.builder().vin(cloudTriageEvent.getVin())
                                .reason(ManualReportEventTypeEnum.getCallCodeByCode(cloudTriageEvent.getEventType()))
                                .source(CommonConstant.REQUEST_CLOUD_OPERATION_SOURCE).timestamp(new Date().getTime())
                                .action(CommonConstant.REQUEST_CLOUD_OPERATION_ACTION).build());
                if (response == null || !response.getCode().equals(CommonConstant.RESPONSE_SUCCESS)) {
                    String errorMsg = response == null ? CommonConstant.NO_RESPONSE : response.getMsg();
                    throw new RemoteErrorException("呼叫云控资源失败, 原因：" + errorMsg);
                }
            }
        }
        return result;
    }

    /**
     * 从 eventId 中获取 车辆名称
     *
     * @param eventId 工单的唯一异常ID 格式：time_type_name
     * @return 车辆名称
     */
    @Override
    public String getVehicleNameFromEventId(String eventId) {
        if (!StringUtils.isEmpty(eventId)) {
            int lastIndex = eventId.lastIndexOf(CharConstant.CHAR_XH);
            if (lastIndex != -1) {
                return eventId.substring(lastIndex + 1);
            }
        }
        return CommonConstant.UNKNOWN;
    }

    @Override
    public Boolean isReportCSMOrder(Integer eventType, String vin){
        return cloudOperationEventTypeSet.contains(eventType) && isReportCSMOrderGray(eventType,vin);
    }

    /**
     * 判断上报的异常事件是否执行灰度逻辑
     * 
     * @param eventType 异常事件类型
     * @return 是否进行灰度
     */
    @Override
    public Boolean isReportCSMOrderGray(Integer eventType, String vin) {
        // 1 检查是否打开灰度开关
        if (CollectionUtils.isEmpty(reportCSMOrderGrayEventTypeSet)) {
            return false;
        }

        if(CollectionUtils.isEmpty(reportCSMOrderGrayVinSet)){
            return false;
        }
        // 2 检查是否全量发布，或者异常事件类型命中灰度开关
        return (reportCSMOrderGrayEventTypeSet.contains(CommonConstant.ALL)
                || reportCSMOrderGrayEventTypeSet.contains(eventType)) && (reportCSMOrderGrayVinSet.contains(CommonConstant.ALL_STRING)
                || reportCSMOrderGrayVinSet.contains(vin));
    }

    /**
     * 根据 operationType 字段过滤非人工可见的异常事件列表
     * 
     * @param eventList 全量的异常事件列表
     * @return 返回仅供分诊角色可见的异常事件列表
     */
    @Override
    public List<BizCloudTriageEvent> filterEvent(List<BizCloudTriageEvent> eventList) {
        try {
            // 1 计算需要过滤的异常事件类型
            if(CollectionUtils.isEmpty(eventList)){
                return eventList;
            }
            // 2 对灰度 且 交给云控处置的异常事件 且 operationType 字段为1 的进行过滤 （主要是兼容某一异常事件可能导致）
            List<BizCloudTriageEvent> filterList = new ArrayList<>();
            List<BizCloudTriageEvent> resList = eventList.stream()
                    .filter(event -> {
                        boolean shouldKeep = !isReportCSMOrder(event.getEventType(), event.getVin())
                                || !event.getOperationType().equals(OrderOperationTypeEnum.AUTOMATIC_CALL_CLOUD_CONTROL.getType());
                        if (!shouldKeep) {
                            filterList.add(event);
                        }
                        return shouldKeep;
                    }).collect(Collectors.toList());
            log.info("filterEvent#, filterList = {}", filterList);
            return resList;
        } catch (Exception e) {
            log.info("filterEvent# error", e);
        }
        return eventList;
    }

    /**
     * 构建通知消息
     *
     * @return
     */
    private String buildNotificationMessage(NotificationMessageDTO messageDTO) {
        String eventTypeDesc = Objects.isNull(messageDTO.getEventType()) ? CommonConstant.UNKNOWN
                : EventTypeEnum.getByCode(messageDTO.getEventType()).getMsg();
        String statusDesc = Objects.isNull(messageDTO.getStatus()) ? CommonConstant.UNKNOWN
                : EventStatusEnum.getMsgByCode(messageDTO.getStatus());
        Date operationTime = Objects.isNull(messageDTO.getOperationTime()) ? new Date() : messageDTO.getOperationTime();
        return String.format(
                "【云控工单状态更新提示】%n" + "车辆号：%s / %s%n" + "事件类型：%s%n" + "工单状态：%s%n" + "动作：%s,[[跳转云分诊|%s%s]]%n" + "操作时间：%s%n"
                        + "操作人：%s%n",
                messageDTO.getVehicleName(), messageDTO.getVehicleId(), eventTypeDesc, statusDesc,
                messageDTO.getAction(), CSM_URL, messageDTO.getVin(), DateUtil.format(operationTime),
                messageDTO.getOperationMisId());
    }

    /**
     * 构建提供给前端展示的提示文案
     *
     * @return
     */
    private String buildFrontEndNoticeTxt(List<VehicleStatusEnum> vehicleStatusEnumList) {
        String result = vehicleStatusEnumList.stream().map(VehicleStatusEnum::getStatusTypeDesc)
                .collect(Collectors.joining("、"));
        return String.format("车辆处于 %s 状态中, 是否还要继续上报联系云控?", result);
    }

    /**
     * 将格式转化为 BizCloudTriageEvent
     * 
     * @param request
     * @param vehicleRealtimeStatusDTO
     * @return
     * @throws Exception
     */
    private BizCloudTriageEvent convertToBizCloudTriageEvent(CloudTriageEventUpdateRequest request,
            VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO) throws InputMismatchException {

        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();

        // 1 拼接 eventId 信息
        String vehicleName = StringUtils.isEmpty(vehicleRealtimeStatusDTO.getName()) ? CommonConstant.UNKNOWN
                : vehicleRealtimeStatusDTO.getName();
        String eventTime = DateUtil.format(new Date(), DateUtil.YMD_HMS_SSS_UNSIGNED);
        String eventName = ManualReportEventTypeEnum.getEventNameByCode(request.getEventType());
        cloudTriageEvent.setEventId(eventTime + CharConstant.CHAR_XH + eventName + CharConstant.CHAR_XH + vehicleName);
        cloudTriageEvent.setEventType(request.getEventType());
        cloudTriageEvent.setVin(request.getVin());
        cloudTriageEvent.setReporter(getMisId());
        cloudTriageEvent.setStatus(EventStatusEnum.INITED.getCode());
        cloudTriageEvent.setVehicleId(StringUtils.isEmpty(vehicleRealtimeStatusDTO.getVehicleId())
                ? CommonConstant.UNKNOWN : vehicleRealtimeStatusDTO.getVehicleId());
        cloudTriageEvent.setPurpose(StringUtils.isEmpty(vehicleRealtimeStatusDTO.getPurpose()) ? CommonConstant.UNKNOWN
                : vehicleRealtimeStatusDTO.getPurpose());
        cloudTriageEvent.setPlace(StringUtils.isEmpty(vehicleRealtimeStatusDTO.getPark()) ? CommonConstant.UNKNOWN
                : vehicleRealtimeStatusDTO.getPark());
        cloudTriageEvent.setLongitude(String.valueOf(vehicleRealtimeStatusDTO.getLongitude()));
        cloudTriageEvent.setLatitude(String.valueOf(vehicleRealtimeStatusDTO.getLatitude()));
        cloudTriageEvent.setEventTime(new Date());
        // 2 设置工单处理类型 (注意结合灰度开关)，即交由云控处置的异常事件类型 与 灰度取交集决定处置类型
        Integer operationType = isReportCSMOrder(request.getEventType(), request.getVin())
                        ? OrderOperationTypeEnum.AUTOMATIC_CALL_CLOUD_CONTROL.getType()
                        : OrderOperationTypeEnum.MANUAL_DISPOSAL.getType();
        cloudTriageEvent.setOperationType(operationType);
        return cloudTriageEvent;
    }

    /**
     * 使用sso鉴权的接口可以获取用户mis
     *
     * @return
     * @throws Exception
     */
    private String getMisId() throws InputMismatchException {
        User user = UserUtils.getUser();
        if (user == null) {
            log.info("permission/operate, user is null");
            throw new InputMismatchException("获取用户名称失败");
        }
        return user.getLogin();
    }

}
