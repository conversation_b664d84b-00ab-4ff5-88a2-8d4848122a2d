<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.walleops.cloud.triage.mapper.OperationChangeLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.walleops.cloud.triage.dal.entity.OperationChangeLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="related_id" jdbcType="BIGINT" property="relatedId" />
    <result column="change_type" jdbcType="INTEGER" property="changeType" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="change_desc" jdbcType="CHAR" property="changeDesc" />
    <result column="change_time" jdbcType="TIMESTAMP" property="changeTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.walleops.cloud.triage.dal.entity.OperationChangeLog">
    <result column="change_content" jdbcType="LONGVARCHAR" property="changeContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, related_id, change_type, operator, change_desc, change_time, update_time, is_deleted, 
    status
  </sql>
  <sql id="Blob_Column_List">
    change_content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.walleops.cloud.triage.dal.example.OperationChangeLogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from operation_change_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.walleops.cloud.triage.dal.example.OperationChangeLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from operation_change_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from operation_change_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from operation_change_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.walleops.cloud.triage.dal.example.OperationChangeLogExample">
    delete from operation_change_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.walleops.cloud.triage.dal.entity.OperationChangeLog">
    insert into operation_change_log (id, related_id, change_type, 
      operator, change_desc, change_time, 
      update_time, is_deleted, status, 
      change_content)
    values (#{id,jdbcType=BIGINT}, #{relatedId,jdbcType=BIGINT}, #{changeType,jdbcType=INTEGER}, 
      #{operator,jdbcType=VARCHAR}, #{changeDesc,jdbcType=CHAR}, #{changeTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{changeContent,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.walleops.cloud.triage.dal.entity.OperationChangeLog">
    insert into operation_change_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="relatedId != null">
        related_id,
      </if>
      <if test="changeType != null">
        change_type,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="changeDesc != null">
        change_desc,
      </if>
      <if test="changeTime != null">
        change_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="changeContent != null">
        change_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=BIGINT},
      </if>
      <if test="changeType != null">
        #{changeType,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="changeDesc != null">
        #{changeDesc,jdbcType=CHAR},
      </if>
      <if test="changeTime != null">
        #{changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="changeContent != null">
        #{changeContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.walleops.cloud.triage.dal.example.OperationChangeLogExample" resultType="java.lang.Long">
    select count(*) from operation_change_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update operation_change_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.relatedId != null">
        related_id = #{record.relatedId,jdbcType=BIGINT},
      </if>
      <if test="record.changeType != null">
        change_type = #{record.changeType,jdbcType=INTEGER},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.changeDesc != null">
        change_desc = #{record.changeDesc,jdbcType=CHAR},
      </if>
      <if test="record.changeTime != null">
        change_time = #{record.changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.changeContent != null">
        change_content = #{record.changeContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update operation_change_log
    set id = #{record.id,jdbcType=BIGINT},
      related_id = #{record.relatedId,jdbcType=BIGINT},
      change_type = #{record.changeType,jdbcType=INTEGER},
      operator = #{record.operator,jdbcType=VARCHAR},
      change_desc = #{record.changeDesc,jdbcType=CHAR},
      change_time = #{record.changeTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      change_content = #{record.changeContent,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update operation_change_log
    set id = #{record.id,jdbcType=BIGINT},
      related_id = #{record.relatedId,jdbcType=BIGINT},
      change_type = #{record.changeType,jdbcType=INTEGER},
      operator = #{record.operator,jdbcType=VARCHAR},
      change_desc = #{record.changeDesc,jdbcType=CHAR},
      change_time = #{record.changeTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.walleops.cloud.triage.dal.entity.OperationChangeLog">
    update operation_change_log
    <set>
      <if test="relatedId != null">
        related_id = #{relatedId,jdbcType=BIGINT},
      </if>
      <if test="changeType != null">
        change_type = #{changeType,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="changeDesc != null">
        change_desc = #{changeDesc,jdbcType=CHAR},
      </if>
      <if test="changeTime != null">
        change_time = #{changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="changeContent != null">
        change_content = #{changeContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.walleops.cloud.triage.dal.entity.OperationChangeLog">
    update operation_change_log
    set related_id = #{relatedId,jdbcType=BIGINT},
      change_type = #{changeType,jdbcType=INTEGER},
      operator = #{operator,jdbcType=VARCHAR},
      change_desc = #{changeDesc,jdbcType=CHAR},
      change_time = #{changeTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      change_content = #{changeContent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.walleops.cloud.triage.dal.entity.OperationChangeLog">
    update operation_change_log
    set related_id = #{relatedId,jdbcType=BIGINT},
      change_type = #{changeType,jdbcType=INTEGER},
      operator = #{operator,jdbcType=VARCHAR},
      change_desc = #{changeDesc,jdbcType=CHAR},
      change_time = #{changeTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>