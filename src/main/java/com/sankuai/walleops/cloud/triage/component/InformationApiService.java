package com.sankuai.walleops.cloud.triage.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.CockpitEventHistoryDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperateHistoryDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperateHistoryPageDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.OrderResponseDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.TripNodeResponseDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleHistoryStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusResponseDTO;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import com.sankuai.walleops.cloud.triage.util.RequestContext;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/8/28
 */
@Component
@Slf4j
public class InformationApiService {
    @MdpConfig("information.center.url.prefix:http://walledata.mad.test.sankuai.com")
    private String informationCenterUrlPrefix = "http://walledata.mad.test.sankuai.com";

    @MdpConfig("vehicle.status.uri:/iiirc/openapi/queryVehicleStatusBatch")
    private String vehicleStatusUri = "/iiirc/openapi/queryVehicleStatusBatch";

    @MdpConfig("trip.node.uri:/iiirc/openapi/tripNodeList")
    private String tripNodeUri = "/iiirc/openapi/tripNodeList";

    @MdpConfig("trip.node.uri:/iiirc/openapi/orderList")
    private String orderListUri = "/iiirc/openapi/orderList";

    @MdpConfig("vehicle.history.status.uri:/iiirc/openapi/vehicleStatusHistory")
    private String vehicleHistoryUri = "/iiirc/openapi/vehicleStatusHistory";

    @MdpConfig("cockpit.history.event.uri:/iiirc/openapi/cockpitEventHistory")
    private String cockpitEventHistoryUri = "/iiirc/openapi/cockpitEventHistory";

    @MdpConfig("operate.log.uri:/iiirc/openapi/madLogger/common")
    private String operateLogUri = "/iiirc/openapi/madLogger/common";

    @MdpConfig("data.center.url.prefix:http://walledata.mad.test.sankuai.com")
    private String dataCenterUrlPrefix = "http://walledata.mad.test.sankuai.com";

    @MdpConfig("date.center.call.notice.uri:/data-center/accident/unchecked/call_and_notice")
    private String callAndNoticeUri = "/data-center/accident/unchecked/call_and_notice";

    private static Long HOUR_MILLISECOND = 3600 * 1000L;

    @MdpConfig("stop_watch_threshold: 1000")
    private Long stopWatchThreshold = 1000L;

    public List<VehicleRealtimeStatusDTO> callVehicleStatusService(Map<String, Object> param) {
        StopWatch stopWatch = new StopWatch("CallVehicleStatusService");
        String url = new StringBuilder(informationCenterUrlPrefix).append(vehicleStatusUri).toString();
        try {
            stopWatch.start("query");
            String response = CommonUtil.doGet(url, param);
            stopWatch.stop();

            stopWatch.start("log_response");
            log.info("查询车辆实时信息-信息中心, request: {}, response: {}", param, response);
            stopWatch.stop();

            stopWatch.start("json_parse");
            VehicleRealtimeStatusResponseDTO vehicleRealtimeStatusDTO = JSON.parseObject(response,
                    VehicleRealtimeStatusResponseDTO.class);
            stopWatch.stop();

            if(stopWatch.getTotalTimeMillis() >= stopWatchThreshold) {
                long totalTimeMs = stopWatch.getTotalTimeMillis();
                String name = "";
                if(totalTimeMs >= 1000 && totalTimeMs < 2000) {
                    name = "1";
                } else if (totalTimeMs >= 2000 && totalTimeMs < 4000) {
                    name = "2";
                } else if (totalTimeMs >= 4000 && totalTimeMs < 8000) {
                    name = "3";
                } else {
                    name = "4";
                }
                Cat.logEvent("vehicle.status", name);
                log.info("[InformationApiService#callvehicleStatusService] StopWatch: {}", stopWatch.prettyPrint());
            }

            return vehicleRealtimeStatusDTO.getData();
        } catch (Exception e) {
            log.error("call url [{}] error, param: {}", url, param, e);
        }
        return Lists.newArrayList();
    }

    public TripNodeResponseDTO callTripNodeList(Map<String, Object> param) {
        String url = new StringBuilder(informationCenterUrlPrefix).append(tripNodeUri).toString();
        try {
            String response = CommonUtil.doGet(url, param);
            log.info("查询TripNode数据-信息中心, request: {}, response: {}", param, response);
            if(response.contains("Gateway Time-out")) {
                return null;
            }

            return JSON.parseObject(response, TripNodeResponseDTO.class);
        } catch (Exception e) {
            log.error("call url [{}] error, param: {}", url, param, e);
        }
        return null;
    }

    public OrderResponseDTO callOrderList(Map<String, Object> param) {
        String url = new StringBuilder(informationCenterUrlPrefix).append(orderListUri).toString();

        try {
            String response = CommonUtil.doGet(url, param);
            log.info("查询订单数据-信息中心, request: {}, response: {}", param, response);
            return JSON.parseObject(response, OrderResponseDTO.class);
        } catch (Exception e) {
            log.error("call url [{}] error, param: {}", url, param, e);
        }
        return null;
    }

    public List<VehicleHistoryStatusDTO> queryVehicleHistoryStatus(Map<String, Object> param) {
        String url = new StringBuilder(informationCenterUrlPrefix).append(vehicleHistoryUri).toString();
        try {
            String response = CommonUtil.doGet(url, param);
            log.info("查询状态回放数据-信息中心, url: {}, response: {}", url, response);
            Integer ret = JSONObject.parseObject(response).getInteger("ret");
            if(ret.equals(ResponseCodeEnum.SUCCESS.getCode())) { //返回成功
                String data = JSONObject.parseObject(response).getString("data");
                List<VehicleHistoryStatusDTO> historyStatusList =
                        JSON.parseObject(data, new TypeReference<List<VehicleHistoryStatusDTO>>(){});

                return historyStatusList;
            } else { //返回失败
                throw new RuntimeException();
            }
        } catch (Exception e) { //处理业务失败、超时异常等情况
            log.error("查询信息中心状态回放数据失败, url: {}, param: {}", url, param, e);
            throw e;
        }
    }

    public OperateHistoryPageDTO queryOperateHistoryList(Map<String, Object> param) {
        String url = new StringBuilder(informationCenterUrlPrefix).append(operateLogUri).toString();
        try {
            String response = CommonUtil.doGet(url, param);
            log.info("查询信息中心操作记录, url: {}, param: {}, response: {}", url, param, response);

            JSONObject responseJSON = JSONObject.parseObject(response);
            Integer ret = responseJSON.getInteger("ret");
            if(ret.equals(ResponseCodeEnum.SUCCESS.getCode())) { //返回成功
                Integer page = responseJSON.getInteger("page");
                Integer size = responseJSON.getInteger("size");
                Integer totalPage = responseJSON.getInteger("totalPage");
                Integer totalSize = responseJSON.getInteger("totalSize");
                String data = responseJSON.getString("data");
                List<OperateHistoryDTO> operateList =
                        JSON.parseObject(data, new TypeReference<List<OperateHistoryDTO>>(){});

                // 整合云控坐席事件
                operateList = joinCockpitEventHistory(operateList);

                OperateHistoryPageDTO pageDTO = new OperateHistoryPageDTO();
                pageDTO.setPage(page);
                pageDTO.setSize(size);
                pageDTO.setTotalPage(totalPage);
                pageDTO.setTotalSize(totalSize);
                pageDTO.setContent(operateList);
                return pageDTO;
            } else { //返回失败
                throw new RuntimeException();
            }
        } catch (Exception e) { //处理业务失败、超时异常等情况
            log.error("查询信息中心操作记录失败, url: {}, param: {}", url, param, e);
            throw e;
        }
    }

    public void notifyAccidentMsg(Map<String, Object> param) {
        String url = new StringBuilder(dataCenterUrlPrefix).append(callAndNoticeUri).toString();
        try {
            String response = CommonUtil.doPost(url, param, null);
            log.info("发送消息-数据中心, request: {}, response: {}", param, response);

            JSONObject responseJson = JSONObject.parseObject(response);
            Integer ret = responseJson.getInteger("ret");
            if(!ret.equals(ResponseCodeEnum.SUCCESS.getCode())) {
                log.info("发送消息-数据中心 failed, response: {}", response);
            }
        } catch (Exception e) {
            log.error("发送消息-数据中心 error", e);
        }
    }

    /**
     * 查询单辆车的实时状态信息
     *
     * @param vin
     * @return 返回vin对应的车辆信息，否则返回null
     */
    public VehicleRealtimeStatusDTO queryVehicleStatusByVin(String vin){
        // 1 入参校验
        if(StringUtils.isEmpty(vin)){
            return null;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("vinList", vin);
        List<VehicleRealtimeStatusDTO> resultList = callVehicleStatusService(param);
        if (CollectionUtils.isEmpty(resultList)){
            return null;
        }
        return resultList.get(0);
    }

    /**
     * 整合云控坐席事件，根据「呼叫云控」来查询相关时间范围内的历史事件
     * @param operateList 操作记录列表
     * @return List<OperateHistoryDTO> 整合后的操作记录列表
     */
    private List<OperateHistoryDTO> joinCockpitEventHistory(List<OperateHistoryDTO> operateList) throws RuntimeException {
        List<OperateHistoryDTO> callRemoteControlOperateHistoryList = operateList.stream()
                .filter(operateHistoryDTO -> "callRemoteControl".equals(operateHistoryDTO.getLogName()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(callRemoteControlOperateHistoryList)) {
            return operateList;
        }
        List<OperateHistoryDTO> resultOperateList = operateList.stream()
                .filter(operateHistoryDTO -> !"callRemoteControl".equals(operateHistoryDTO.getLogName()))
                .collect(Collectors.toList());

        if (callRemoteControlOperateHistoryList.size() > 2) {
            // 按时间阈值分组 -- 每两小时进行划分
            List<List<OperateHistoryDTO>> groupList = new ArrayList<>();
            List<OperateHistoryDTO> subgroupList = new ArrayList<>();
            callRemoteControlOperateHistoryList.sort(Comparator.comparingLong(OperateHistoryDTO::getLogTime));
            long endTimestamp = callRemoteControlOperateHistoryList.get(0).getLogTime() + HOUR_MILLISECOND * 2;
            for (OperateHistoryDTO operateHistoryDTO : callRemoteControlOperateHistoryList) {
                if (operateHistoryDTO.getLogTime() <= endTimestamp) {
                    subgroupList.add(operateHistoryDTO);
                } else {
                    endTimestamp = operateHistoryDTO.getLogTime() + HOUR_MILLISECOND * 2;
                    groupList.add(subgroupList);
                    subgroupList = new ArrayList<>();
                    subgroupList.add(operateHistoryDTO);
                }
            }
            groupList.add(subgroupList);
            // 分组查询
            for (List<OperateHistoryDTO> operateHistoryDTOList : groupList) {
                OperateHistoryDTO first = operateHistoryDTOList.stream().findFirst().get();
                List<CockpitEventHistoryDTO> cockpitEventHistoryDTOList = queryCockpitEvent(
                        first.getVin().toUpperCase(), first.getLogTime(), first.getLogTime() + HOUR_MILLISECOND * 4);
                // 匹配坐席事件
                for (OperateHistoryDTO operateHistoryDTO : operateHistoryDTOList) {
                    matchCockpitEvent(operateHistoryDTO, cockpitEventHistoryDTOList);
                    resultOperateList.add(operateHistoryDTO);
                }
            }
        } else {
            for (OperateHistoryDTO operateHistoryDTO : callRemoteControlOperateHistoryList) {
                List<CockpitEventHistoryDTO> cockpitEventHistoryDTOList = queryCockpitEvent(
                        operateHistoryDTO.getVin().toUpperCase(), operateHistoryDTO.getLogTime(),
                        operateHistoryDTO.getLogTime() + HOUR_MILLISECOND * 2);
                // 匹配坐席事件
                matchCockpitEvent(operateHistoryDTO, cockpitEventHistoryDTOList);
                resultOperateList.add(operateHistoryDTO);
            }
        }
        resultOperateList.sort(Comparator.comparingLong(OperateHistoryDTO::getLogTime).reversed());
        return resultOperateList;
    }

    private List<CockpitEventHistoryDTO> queryCockpitEvent(String vin, Long startTimestamp, Long endTimestamp)
            throws RuntimeException {
        String url = new StringBuilder(informationCenterUrlPrefix).append(cockpitEventHistoryUri).toString();
        Map<String, Object> param = new HashMap<>();
        param.put("misId", UserUtils.getUser().getLogin());
        param.put("clientIp", RequestContext.getIp());
        param.put("vin", vin);
        param.put("startTimestamp", startTimestamp);
        param.put("endTimestamp", endTimestamp);
        log.info("查询云控坐席事件历史-信息中心, param: {}", param);
        String response = CommonUtil.doGet(url, param);
        log.info("查询云控坐席事件历史-信息中心, url: {}, param: {}, response: {}", url, param, response);

        JSONObject responseJSON = JSONObject.parseObject(response);
        Integer ret = responseJSON.getInteger("ret");
        if(ret.equals(ResponseCodeEnum.SUCCESS.getCode())) { // 返回成功
            String data = responseJSON.getString("data");
            List<CockpitEventHistoryDTO> cockpitEventHistoryDTOList =
                    JSON.parseObject(data, new TypeReference<List<CockpitEventHistoryDTO>>(){});
            return cockpitEventHistoryDTOList;
        } else { //返回失败
            throw new RuntimeException();
        }
    }

    private OperateHistoryDTO matchCockpitEvent(
            OperateHistoryDTO operateHistoryDTO, List<CockpitEventHistoryDTO> cockpitEventHistoryDTOList) {
        // 匹配坐席事件
        List<CockpitEventHistoryDTO> cockpitEventList = new ArrayList<>(2);
        CockpitEventHistoryDTO cockpitConnect = cockpitEventHistoryDTOList.stream()
                .filter(event -> event.getTimestamp() >= operateHistoryDTO.getLogTime() && event.getEvent().equals(3))
                .min(Comparator.comparingLong(CockpitEventHistoryDTO::getTimestamp))
                .orElse(null);
        if (Objects.nonNull(cockpitConnect)) {
            cockpitEventList.add(cockpitConnect);
            CockpitEventHistoryDTO cockpitDisconnect = cockpitEventHistoryDTOList.stream()
                    .filter(event -> event.getTimestamp() >= cockpitConnect.getTimestamp() && event.getEvent().equals(4))
                    .min(Comparator.comparingLong(CockpitEventHistoryDTO::getTimestamp))
                    .orElse(null);
            if (Objects.nonNull(cockpitDisconnect)) {
                cockpitEventList.add(cockpitDisconnect);
            }
        }
        // 封装消息体
        JSONObject contentJSON = JSONObject.parseObject(JSONObject.toJSONString(operateHistoryDTO.getContent()));
        contentJSON.put("cockpitEvent", cockpitEventList);
        operateHistoryDTO.setContent(contentJSON);
        return operateHistoryDTO;
    }

    public Boolean queryIsVhrMultipleByVin(String vin){
        Map<String, Object> param = new HashMap<>();
        param.put("vinList", vin);
        List<VehicleRealtimeStatusDTO> resultList = callVehicleStatusService(param);
        if (!resultList.isEmpty()) {
            VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = resultList.get(0);
            Integer isVhrMultiple = vehicleRealtimeStatusDTO.getIsVhrMultiple();
            //只有查到vhr 且 vhr等于0
            if (isVhrMultiple != null && isVhrMultiple.intValue() == 0) { // VHR<=1
                return false;
            }
        }
        return true;
    }



}
