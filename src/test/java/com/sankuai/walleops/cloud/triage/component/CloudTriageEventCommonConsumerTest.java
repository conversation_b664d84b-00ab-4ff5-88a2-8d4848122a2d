package com.sankuai.walleops.cloud.triage.component;

import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
@Slf4j
//@Ignore
public class CloudTriageEventCommonConsumerTest {

    @Resource
    private CloudTriageEventCommonConsumer consumer;

    @Test
    public void testStartEndTime() {

        String msg = "{\"type\":130,\"body\":{\"eventId\":\"20241125083625849_MOVE_CAR_EVENT_s20-364\",\"vin\":\"LMTZSV025NC010556\",\"status\":\"0\",\"eventTime\":\"2024-11-25 08:36:26\",\"eventType\":40,\"reporter\":\"外部人员\",\"extInfo\":\"{\\\"openId\\\":\\\"oBOa-5Xd1kA3MVXaYVF0ucu-i0Ak\\\",\\\"carPosition\\\":\\\"\\\",\\\"carPositionGps\\\":\\\"0,0\\\",\\\"moveCarReason\\\":\\\"车辆挡道\\\",\\\"urlList\\\":[]}\"},\"timestamp\":1732494990076}";
        consumer.consume(msg, null);
    }
}
