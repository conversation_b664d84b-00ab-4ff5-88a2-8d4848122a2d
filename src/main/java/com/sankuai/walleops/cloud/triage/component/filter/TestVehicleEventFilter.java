package com.sankuai.walleops.cloud.triage.component.filter;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * 测试车辆的异常事件过滤器，过滤测试车辆的所有异常事件。
 * */

@Component
@Slf4j
@Order(1)
public class TestVehicleEventFilter implements EventFilter {

    @Resource
    private InformationApiService informationApiService;

    @MdpConfig("filter.test.vehicle.list:[\"LK6CACE16NG600042\"]")
    private ArrayList<String> filterTestVinList;

    public boolean doFilter(InformationDTO information, EventFilterChain filterChain) {
        JSONObject jsonObject = JSONObject.parseObject(information.getData());
        if(jsonObject.containsKey("vin")) {
            String vin  = jsonObject.getString("vin");
            if(filterTestVinList.contains(vin)) {
                return Boolean.TRUE;
            }
        }

        return Boolean.FALSE;
    }
}
