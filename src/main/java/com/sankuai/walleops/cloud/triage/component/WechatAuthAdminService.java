package com.sankuai.walleops.cloud.triage.component;

import com.sankuai.walleeve.dto.TokenCheckDTO;
import com.sankuai.walleeve.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WechatAuthAdminService {


    /**
     * 用户鉴权加密密钥
     */
    @Value("${wechat.secret}")
    private String wechatAuthSecret;

    /**
     * 检查token的有效性和主题信息
     *
     * @param token 待检查的token
     * @return TokenCheckDTO对象，包含token的有效性和主题信息
     */
    public TokenCheckDTO checkToken(String token) {
        TokenCheckDTO tokenCheckDTO = JwtUtil.getTokenValidAndSubject(token, wechatAuthSecret);
        log.info("getTokenValidAndSubject,wechatAuthSecret = {}, tokenCheckDTO = {}",wechatAuthSecret, tokenCheckDTO);
        return tokenCheckDTO;
    }

}
