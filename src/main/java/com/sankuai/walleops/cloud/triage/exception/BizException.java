package com.sankuai.walleops.cloud.triage.exception;


import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;

/**
 * @<PERSON> <PERSON><PERSON><PERSON><PERSON>an
 * @Date 2022/8/24
 */
public class BizException extends RuntimeException {

    private ResponseCodeEnum responseCode;
    private String extMsg;

    public BizException(ResponseCodeEnum responseCode) {
        super(responseCode.getMsg());
        this.responseCode = responseCode;
    }

    public BizException(ResponseCodeEnum responseCode, String extMsg) {
        super(responseCode.getMsg());
        this.responseCode = responseCode;
        this.extMsg = extMsg;
    }

    public BizException(String message) {
        super(message);
    }

    public BizException(Throwable cause) {
        super(cause);
    }

    public BizException(String message, Throwable cause) {
        super(message, cause);
    }

    public ResponseCodeEnum getResponseCode() {
        return responseCode;
    }

    public String getExtMsg() {
        return extMsg;
    }
}

