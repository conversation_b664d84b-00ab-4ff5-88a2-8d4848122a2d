package com.sankuai.walleops.cloud.triage.controller;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mtrace.util.StringUtils;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperatorStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.request.OperatorStatusRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.service.OperatorStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

@RestController
@RequestMapping(value = "/operator/status")
@Slf4j
public class OperatorStatusController {

    @Resource
    private OperatorStatusService operatorStatusService;

    @MdpConfig("operator.status.client.id")
    private String operatorStatusClientId;

    @GetMapping("/query")
    public CommonResponse queryOperatorStatus(HttpServletRequest request) {
        String misId = getLogin(request);
        log.info("[/operator/status/query] request: {}, mis_id: {}", request, misId);
        if(StringUtils.isBlank(misId)) {
            return CommonResponse.failed();
        }

        OperatorStatusDTO dto = operatorStatusService.queryOperatorStatus(misId);
        log.info("[/operator/status/query] response: {}", dto);
        if(dto == null) {
            return CommonResponse.failed();
        } else {
            return CommonResponse.success(dto);
        }
    }

    @PostMapping("/set")
    public CommonResponse setOperatorStatus(@RequestBody OperatorStatusRequest param,
                                            HttpServletRequest request) {
        String misId = getLogin(request);
        log.info("[/operator/status/set] request: {}, param: {}, mis_id: {}", request, param, misId);
        if(StringUtils.isBlank(misId)
                || (param.getStatus().intValue() != 0 && param.getStatus().intValue() != 1)) {
            return CommonResponse.failed();
        }

        boolean result = operatorStatusService.setOperatorStatus(misId, param.getStatus());
        log.info("[/operator/status/set] response: {}", result);
        if(result) {
            return CommonResponse.success();
        } else {
            return CommonResponse.failed();
        }
    }

    private String getLogin(HttpServletRequest request) {
        Cookie ssoCookie = Arrays.stream(request.getCookies())
                .filter(item -> item.getName().contains(operatorStatusClientId + "_ssoid"))
                .findAny().orElse(null);
        if(ssoCookie == null) {
            return null;
        }

        User user = UserUtils.getUser(ssoCookie.getValue());
        if(user == null) {
            return null;
        }

        return user.getLogin();
    }
}
