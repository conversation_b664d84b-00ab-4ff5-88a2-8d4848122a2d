package com.sankuai.walleops.cloud.triage.component.pike;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.StringUtils;
import com.sankuai.pike.message.api.rpc.business.entity.ConnectRequest;
import com.sankuai.pike.message.api.rpc.business.entity.ConnectResponse;
import com.sankuai.pike.message.api.rpc.business.entity.DisconnectRequest;
import com.sankuai.pike.message.sdk.listener.ConnectListener;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.proxy.SquirrelProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Map;

@Component
@Slf4j
public class PikeConnectListener implements ConnectListener {
    @Resource
    private SquirrelProxy squirrelProxy;

    /**
     * 建连回调
     *
     * @param connectRequest 建连请求参数
     */
    public ConnectResponse onConnect(ConnectRequest connectRequest) {
        log.info("建立Pike连接, connectRequest: {}", connectRequest);

        String misId = connectRequest.getAlias();
        String token = connectRequest.getToken();
        Map<String, String> extra = connectRequest.getExtra();
        if(CollectionUtils.isEmpty(extra)) {
            // 默认筛选条件，所有筛选条件都使用默认值
            extra.put("default", "");
        }

        boolean result = squirrelProxy.hset(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY, CommonConstant.TRIAGE_NEW_EVENT_CLIENT_KEY,
                            misId + "_" + token, JSON.toJSONString(extra));
        ConnectResponse response = new ConnectResponse();
        response.setAlias(misId);
        if(result) {
            response.setSuccess(true);
            log.info("建立Pike连接, 存储筛选条件成功, misId: {}, token: {}, extra: {}", misId, token, extra);
        } else {
            response.setSuccess(false);
            log.error("建立Pike连接, 存储筛选条件失败, misId: {}, token: {}, extra: {}", misId, token, extra);
        }
        return response;
    }

    /**
     * 断连回调
     *
     * @param disconnectRequest 断连请求参数
     */
    public void onDisconnect(DisconnectRequest disconnectRequest) {
        log.info("断开Pike连接, disconnectRequest: {}", disconnectRequest);

        String misId = disconnectRequest.getAlias();
        if(StringUtils.isEmpty(misId) && !CollectionUtils.isEmpty(disconnectRequest.getAliasList())) {
            misId = disconnectRequest.getAliasList().get(0);
        }
        String token = disconnectRequest.getToken();

        boolean result = squirrelProxy.hdel(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY, CommonConstant.TRIAGE_NEW_EVENT_CLIENT_KEY,
                                            misId + "_" + token);
        if(result) {
            log.info("断开Pike连接, 删除筛选条件成功, misId: {}, token: {}", misId, token);
        } else {
            log.error("断开Pike连接, 删除筛选条件失败, misId: {}, token: {}", misId, token);
        }
    }
}
