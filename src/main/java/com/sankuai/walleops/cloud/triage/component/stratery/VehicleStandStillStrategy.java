package com.sankuai.walleops.cloud.triage.component.stratery;

import com.alibaba.fastjson.JSON;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.StandstillEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/25
 */
@Slf4j
@Component("vehicle_standstill")
public class VehicleStandStillStrategy extends EventStrategy {
    @Override
    public BizCloudTriageEvent transferEvent(InformationDTO information, EventTypeEnum eventTypeEnum) {
        StandstillEventDTO eventDTO;
        try {
            eventDTO = JSON.parseObject(information.getData(), StandstillEventDTO.class);
        } catch (Exception e) {
            log.error("information data parse error: {}", information.getData(), e);
            return null;
        }

        Date eventTime = new Date(eventDTO.getStartTimestamp());
        String eventId = CommonUtil.generateEventId(eventTime, eventTypeEnum.getEventName(),
                eventDTO.getVehicleName());

        if (exceptionRecover(eventId, eventDTO.getStatus())) {
            return null;
        }

        BizCloudTriageEvent cloudTriageEvent = new BizCloudTriageEvent();
        cloudTriageEvent.setEventId(eventId);
        cloudTriageEvent.setEventTime(eventTime);
        cloudTriageEvent.setVehicleId(eventDTO.getVehicleId());
        cloudTriageEvent.setEventType(eventTypeEnum.getCode());
        cloudTriageEvent.setVin(eventDTO.getVin());
        cloudTriageEvent.setLongitude(eventDTO.getLongitude().toString());
        cloudTriageEvent.setLatitude(eventDTO.getLatitude().toString());
        return cloudTriageEvent;
    }
}
