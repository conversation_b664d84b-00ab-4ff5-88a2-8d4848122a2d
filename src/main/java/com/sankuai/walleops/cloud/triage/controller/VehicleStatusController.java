package com.sankuai.walleops.cloud.triage.controller;

import com.sankuai.walleops.cloud.triage.constant.ResponseCodeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleHistoryStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.request.VideoHistoryStatusRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.service.VehicleStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.Errors;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/video")
@Slf4j
public class VehicleStatusController {

    @Resource
    private VehicleStatusService vehicleStatusService;

    @GetMapping("/history/status")
    public CommonResponse queryVideoHistoryStatus(@Valid VideoHistoryStatusRequest request,
                                                  Errors errors) {

        log.info("查询状态回放数据, request: {}", request);
        if(errors.hasErrors()) {
            log.info("查询状态回放数据, 请求参数错误, errors: {}",
                    errors.getFieldError().getDefaultMessage());

            CommonResponse response = new CommonResponse();
            response.setRet(ResponseCodeEnum.BAD_REQUEST.getCode());
            response.setMsg(errors.getFieldError().getDefaultMessage());
            response.setData(new ArrayList<VehicleHistoryStatusDTO>());
            return response;
        }

        try {
            List<VehicleHistoryStatusDTO> historyStatusDTOList =
                    vehicleStatusService.queryVehicleHistoryStatus(request);
            log.info("查询状态回放数据, result: {}",
                    historyStatusDTOList);

            CommonResponse response = CommonResponse.success();
            response.setData(historyStatusDTOList);
            return response;
        } catch (Exception e) {
            log.error("查询状态回放数据失败", e);

            CommonResponse response = CommonResponse.failed();
            response.setData(new ArrayList<VehicleHistoryStatusDTO>());
            return response;
        }
    }
}
