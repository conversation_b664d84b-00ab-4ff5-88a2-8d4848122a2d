package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.pojo.dto.OperatorStatusDTO;

public interface OperatorStatusService {

    /**
     * 查询运营人员上下线状态
     * @param misId
     * @return OperatorStatusDTO
     * */
    OperatorStatusDTO queryOperatorStatus(String misId);

    /**
     * 设置运营人员上下线状态
     * @param misId
     * @param status 0: offline, 1: online
     * @return true: 设置成功, false: 设置失败
     * */
    boolean setOperatorStatus(String misId, Integer status);

    /**
     * 删除运营人员上下线状态信息
     * @param misId
     * @return true: 删除成功, false: 删除失败
     * */
    boolean delOperatorStatus(String misId);
}
