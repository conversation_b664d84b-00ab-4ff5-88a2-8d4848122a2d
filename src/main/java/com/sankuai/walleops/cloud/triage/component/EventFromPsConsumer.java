package com.sankuai.walleops.cloud.triage.component;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.AbstractMdpListener;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleops.cloud.triage.adaptor.eve.EVEAdaptor;
import com.sankuai.walleops.cloud.triage.component.pike.PikeServer;
import com.sankuai.walleops.cloud.triage.component.stratery.EventStrategy;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.PsEventEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.EventDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EventFromPsConsumer {

    @Resource
    private CloudTriageEventService cloudTriageEventService;

    @Resource
    private PikeServer pikeServer;

    @Resource
    private Map<String, EventStrategy> eventStrategyMap;

    @Resource
    private EVEAdaptor eveAdaptor;

    @ConfigValue(key = CommonConstant.LION_KEY_EVENT_VEHICLE_FILTER_CONFIG, defaultValue = "{}")
    private Map<String, Set<String>> eventVehicleFilterConfig;


    @MdpMafkaMsgReceive
    public ConsumeStatus consume(String message, AbstractMdpListener.MdpMqContext context) {
        log.info("msgId: {}, ps receive information: {}", context.getMessage().getMessageID(), message);

        //1 解析mafka数据
        EventDTO eventDTO = null;
        try {
            eventDTO = JSON.parseObject(message, EventDTO.class);
        } catch (Exception e) {
            log.error("parse cloud triage message error: {}", message, e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //2 根据异常异常事件类型触发不同的处理逻辑
        BizCloudTriageEvent cloudTriageEvent = null;
        try {
            Integer eventType = PsEventEnum.getTypeByCode(eventDTO.getEventType());
            // 事件类型车辆过滤逻辑
            if (eventType != null && shouldFilterEvent(eventType, eventDTO)) {
                log.info("事件被过滤: eventType={}, vehicleName={}, vin={}, eventId={}",
                        eventType, eventDTO.getVehicleName(), eventDTO.getVin(), eventDTO.getEventId());
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            //  InputCheckUtil.isNotNull(eventType, "eventType is null");
            if (eventType == null || !eventStrategyMap.containsKey(EventTypeEnum.getByCode(eventType).getEventName())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            EventStrategy eventStrategy = eventStrategyMap.get(EventTypeEnum.getByCode(eventType).getEventName());
            cloudTriageEvent = eventStrategy.transferEvent(eventDTO, EventTypeEnum.getByCode(eventType));
        } catch (Exception e) {
            log.error("eventDTO transferEvent cloudTriageEvent is error, eventDTO = {} ,cloudTriageEvent = {}",
                    eventDTO, cloudTriageEvent, e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if (cloudTriageEvent == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            int result = cloudTriageEventService.save(cloudTriageEvent);
            if (result < 1) {
                log.info("database write failure! cloudTriageEvent = {}", cloudTriageEvent);
            }
            pikeServer.notifyNewEvent(cloudTriageEvent);
        } catch (Exception e) {
            log.error("save cloudTriageEvent error: {}", cloudTriageEvent, e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 判断事件是否需要被过滤
     *
     * @param eventType 事件类型
     * @param eventDTO  事件数据
     * @return true-需要过滤, false-不需要过滤
     */
    private boolean shouldFilterEvent(Integer eventType, EventDTO eventDTO) {
        try {
            // 检查配置是否为空
            if (Objects.isNull(eventVehicleFilterConfig)) {
                return false;
            }

            // 将eventType转换为字符串作为key
            String eventTypeKey = String.valueOf(eventType);

            // 检查是否有对应eventType的配置
            if (!eventVehicleFilterConfig.containsKey(eventTypeKey)) {
                return false;
            }

            // 获取需要过滤的车辆列表
            Set<String> filterVehicleList = eventVehicleFilterConfig.get(eventTypeKey);
            if (CollectionUtils.isEmpty(filterVehicleList)) {
                return false;
            }

            // 获取车辆信息进行匹配
            String firstClassModel = null;
            String vin = eventDTO.getVin();
            // 调用远程服务获取车辆信息
            VehicleStatusDTO vehicleInfo = eveAdaptor.getVehicleStatusFromDataBusByVin(vin);
            if (vehicleInfo != null && vehicleInfo.getVehicleManage() != null) {
                firstClassModel = vehicleInfo.getVehicleManage().getFirstClassModel();
            }

            // 检查一级车型是否在过滤列表中
            if (firstClassModel != null && filterVehicleList.contains(firstClassModel)) {
                log.info("车辆一级车型匹配过滤规则: firstClassModel={}, eventType={}",
                        firstClassModel, eventType);
                return true; // 匹配到过滤规则，返回true表示需要过滤
            }
            return false;
        } catch (Exception e) {
            log.error("shouldFilterEvent执行异常: eventType={}, eventDTO={}", eventType, eventDTO, e);
            return false;
        }
    }


}
