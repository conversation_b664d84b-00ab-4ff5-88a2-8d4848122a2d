package com.sankuai.walleops.cloud.triage.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.walleops.cloud.triage.constant.ActionEnum;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventStatusEnum;
import com.sankuai.walleops.cloud.triage.constant.OperationChangeTypeEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.NotificationMessageDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.OperationLogDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.service.AbnormalOrderReportService;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import com.sankuai.walleops.cloud.triage.service.OperationChangeLogService;
import com.sankuai.walleops.cloud.triage.util.CommonUtil;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@CraneConfiguration
@Slf4j
public class MonitorTimeoutUnprocessedEventCrane {

    @Resource
    CloudTriageEventService cloudTriageEventService;

    @Resource
    AbnormalOrderReportService abnormalOrderReportService;

    @Resource
    OperationChangeLogService operationChangeLogService;

    /**
     * 工单状态未处理的最大超时时长
     */
    @MdpConfig("order.unprocessed.status.max.duration")
    private Integer orderUnprocessedStatusMaxDuration = 5;

    /**
     * 工单状态进行中未处理的最大超时时长
     */
    @MdpConfig("order.uncompleted.status.max.duration")
    private Integer orderUncompletedStatusMaxDuration = 15;

    /**
     * 兜底N分钟没有云控接入的工单、以及M分钟内接入但是没有退出的工单
     */
    @Crane("monitor.timeout.unprocessed.event.crane")
    public void monitorTimeoutUnprocessedEvent() {
        // 1 查询当天起始时间到N分钟前未处理以及 当天起始时间到M分钟前处理未完成的工单列表
        Date minCreateTime = DateUtil.getHeadOfToday();
        Date maxCreateTime = DateUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, orderUnprocessedStatusMaxDuration);
        Date minOperateStartTime = DateUtil.getHeadOfToday();
        Date maxOperateStartTime = DateUtil.getBeforeTime(new Date(), TimeUnit.MINUTES,
                orderUncompletedStatusMaxDuration);
        List<BizCloudTriageEvent> bizCloudTriageEventList = cloudTriageEventService.getUnprocessedAndUncompletedEvents(
                minCreateTime,
                maxCreateTime,
                minOperateStartTime,
                maxOperateStartTime);
        if (CollectionUtils.isEmpty(bizCloudTriageEventList)) {
            log.info("monitorTimeoutUnprocessedEvent# 最近无{}分钟前未处理以及{}分钟前处理未完成的工单",
                    orderUnprocessedStatusMaxDuration,
                    orderUncompletedStatusMaxDuration);
            return;
        }
        log.info("monitorTimeoutUnprocessedEvent# {}分钟前未处理以及{}分钟前处理未完成的工单={}",orderUnprocessedStatusMaxDuration,
                orderUncompletedStatusMaxDuration,
                bizCloudTriageEventList);

        // 2 将超时未处理的工单设置为取消状态，然后发送消息通知
        bizCloudTriageEventList.stream().forEach(cloudTriageEvent -> {
            // 获取工单历史状态,用于获取处理动作
            Integer oldStatus = cloudTriageEvent.getStatus();
            // 关闭工单（设置为已取消）
            cloudTriageEvent.setStatus(EventStatusEnum.CANCELED.getCode());
            cloudTriageEvent.setOperator(CommonConstant.SYSTEM);
            Boolean result = cloudTriageEventService.updateBizCloudTriageEvent(cloudTriageEvent);
            if(result){
                // 2 记录流水
                operationChangeLogService.insertOperationLog(OperationLogDTO.builder().relatedId(cloudTriageEvent.getId())
                        .changeType(OperationChangeTypeEnum.UPDATE_ORDER.getType())
                        .changeContent(CommonUtil.getChangeDiff(EventStatusEnum.getMsgByCode(oldStatus)
                                ,EventStatusEnum.CANCELED.getMsg()))
                        .status(cloudTriageEvent.getStatus())
                        .operator(cloudTriageEvent.getOperator()).build());

                // 3 发送通知
                // 计算通知中的超时时间，需要区分未处理/处理中未完成
                Integer timeoutMinutes = oldStatus == EventStatusEnum.INITED.getCode()? orderUnprocessedStatusMaxDuration:
                        orderUncompletedStatusMaxDuration;
                abnormalOrderReportService.sendProcessMessageToReporter(cloudTriageEvent.getReporter(),
                        NotificationMessageDTO.builder()
                                .vehicleName(
                                        abnormalOrderReportService.getVehicleNameFromEventId(cloudTriageEvent.getEventId()))
                                .vehicleId(cloudTriageEvent.getVehicleId()).operationTime(new Date())
                                .eventType(cloudTriageEvent.getEventType())
                                .action(String.format(ActionEnum.determineAction(oldStatus, cloudTriageEvent.getStatus()),
                                        timeoutMinutes ))
                                .status(cloudTriageEvent.getStatus()).vin(cloudTriageEvent.getVin())
                                .operationMisId(cloudTriageEvent.getOperator()).build());
            }
        });

    }
}
