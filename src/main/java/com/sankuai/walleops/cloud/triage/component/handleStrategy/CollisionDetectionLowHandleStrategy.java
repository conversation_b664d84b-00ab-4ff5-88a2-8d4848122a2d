package com.sankuai.walleops.cloud.triage.component.handleStrategy;

import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Component("collision-detection-low-handle")
@Slf4j
public class CollisionDetectionLowHandleStrategy extends HandleStrategy {
    @Resource
    private InformationApiService informationApiService;
    @Override
    public void process(BizCloudTriageEvent cloudTriageEvent) {
        log.info("CollisionDetectionLowHandleStrategy is processing, param is {}", cloudTriageEvent);
        setAutoCancel(cloudTriageEvent);
    }

    public void setAutoCancel(BizCloudTriageEvent cloudTriageEvent){
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("vinList", cloudTriageEvent.getVin());
            List<VehicleRealtimeStatusDTO> resultList = informationApiService.callVehicleStatusService(param);
            if (!resultList.isEmpty()) {
                VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = resultList.get(0);
                //对于VHR <= 1  的车设置 系统自动取消，这里的VHR <= 1 包括两种情况
                //1）VHR 为 空   2）VHR == 1
                Integer isVhrMultiple = vehicleRealtimeStatusDTO.getIsVhrMultiple();
                if(isVhrMultiple != null && isVhrMultiple.intValue() == 0) { // VHR<=1
                    cloudTriageEvent.setOperator("system");
                    // 3 表示取消状态
                    cloudTriageEvent.setStatus(3);
                }
            }
        } catch (Exception e) {
            log.error("query information vhr error: {}", cloudTriageEvent);
        }
    }

}
