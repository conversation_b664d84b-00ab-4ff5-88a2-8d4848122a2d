package com.sankuai.walleops.cloud.triage.thrift.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "车机碰撞检测更新请求")
public class CollisionDetectionUpdateRequest extends BasicFrontRequest {

    @ThriftField(1)
    @FieldDoc(description = "事件ID")
    private String eventId;

    @ThriftField(2)
    @FieldDoc(description = "状态")
    private Integer status;
}
