package com.sankuai.walleops.cloud.triage.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.servicecatalog.api.annotations.MediaType;
import com.sankuai.walleops.cloud.triage.component.InformationApiService;
import com.sankuai.walleops.cloud.triage.component.pike.PikeServer;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.constant.EventTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.InformationTypeEnum;
import com.sankuai.walleops.cloud.triage.constant.PsEventEnum;
import com.sankuai.walleops.cloud.triage.pojo.dto.InformationDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.LastEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.PsEventDTO;
import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventQueryPageRequest;
import com.sankuai.walleops.cloud.triage.pojo.response.CommonResponse;
import com.sankuai.walleops.cloud.triage.proxy.SquirrelProxy;
import com.sankuai.walleops.cloud.triage.service.CloudTriageEventService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import javax.annotation.Resource;
import java.sql.*;
import java.util.*;
import java.util.Date;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
@Ignore
public class CloudTriageEventControllerTest {
    @Resource
    private CloudTriageEventController controller;

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void testStartEndTime() {
        CloudTriageEventQueryPageRequest request = new CloudTriageEventQueryPageRequest();
        request.setStartTime("2022-12-19 00:00:00");
        request.setEndTime("2022-12-23 23:59:59");

        CommonResponse response = controller.queryEvent(request);
        System.out.println(response);

        Assert.assertEquals(3, response.getRet().intValue());
    }

    @Test
    public void testQueryLastEvent() {
        List<String> vinList = Arrays.asList("LMTZSV029NC042698", "LMTZSV024MC048701");
        Long startTime = 1679407200L;
        Long endTime = 1679490000L;
        Integer eventType = 1;
        CommonResponse response = controller.queryLastEvent(vinList, startTime, endTime, eventType);

        Assert.assertEquals(0, response.getRet().intValue());
        List<LastEventDTO> lastEventDTOList = (ArrayList<LastEventDTO>)response.getData();
        Assert.assertEquals(2, lastEventDTOList.size());
    }

    @Test
    public void testQueryEvent() throws Exception {
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.get("/event/list")
                        .param("eventType", String.valueOf(26))
                        .param("status", String.valueOf(0))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        JSONObject jsonObject = JSON.parseObject(result.getResponse().getContentAsString());
        JSONObject jsonObjectData  = (JSONObject) jsonObject.get("data");
        Assert.assertNotNull(jsonObjectData);
        Assert.assertNotNull(jsonObjectData.get("page"));
    }


    @Test
    public void getEventReportListTest() throws Exception {
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.get("/event/getEventReportList")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        JSONObject jsonObject = JSON.parseObject(result.getResponse().getContentAsString());
        JSONObject jsonObjectData  = (JSONObject) jsonObject.get("data");
        Assert.assertNotNull(jsonObjectData);
    }


}
