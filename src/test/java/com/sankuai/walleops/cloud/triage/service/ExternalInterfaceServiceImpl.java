package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.walleops.cloud.triage.BaseTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import javax.annotation.Resource;

@RunWith(MockitoJUnitRunner.class)
public class ExternalInterfaceServiceImpl extends BaseTest {

    @Resource
    ExternalInterfaceService externalInterfaceService;

    @Test
    public void getLocationNameByGpsTest() throws Exception {
        String locationGps = "116.54158597846497,40.09979333603567";
        String locationName = externalInterfaceService.getLocationNameByGps(locationGps);
        System.out.println("locationName: "+ locationName);
    }

    @Test
    public void getRescueOrderStatusTest(){
       Boolean result =  externalInterfaceService.getRescueOrderStatus("LMTZSV027MC042469");
       System.out.println("result: "+result);
    }
}
