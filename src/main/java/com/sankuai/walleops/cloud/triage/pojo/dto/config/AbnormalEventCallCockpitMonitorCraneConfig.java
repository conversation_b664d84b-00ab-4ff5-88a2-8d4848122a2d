package com.sankuai.walleops.cloud.triage.pojo.dto.config;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AbnormalEventCallCockpitMonitorCraneConfig {

    /**
     * 未处理超时时间（分钟）
     */
    private Integer unhandledTimeoutMins;
    /**
     * 已处理超时时间（分钟）
     */
    private Integer handledTimeoutMins;
    /**
     * 群ID
     */
    private long gid;

    /**
     * 处置方列表
     */
    private List<Integer> operatorTypeList;
    /**
     * 异常事件类型列表
     */
    private List<Integer> eventTypeList;

    /**
     * 关闭事件链接
     */
    private String closeEventUrl;

}
