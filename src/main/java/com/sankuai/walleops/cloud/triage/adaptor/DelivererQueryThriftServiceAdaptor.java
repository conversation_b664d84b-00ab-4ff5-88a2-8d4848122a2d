package com.sankuai.walleops.cloud.triage.adaptor;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.walledelivery.basic.client.request.deliverer.DelivererQueryByAccountRequest;
import com.sankuai.walledelivery.basic.client.response.deliverer.DelivererResponse;
import com.sankuai.walledelivery.basic.client.thrift.inner.deliverer.DelivererQueryThriftService;
import com.sankuai.walledelivery.thrift.response.ThriftResponse;
import com.sankuai.walledelivery.utils.JacksonUtils;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
public class DelivererQueryThriftServiceAdaptor {

    @MdpThriftClient(remoteAppKey = "com.sankuai.walledelivery.basic", timeout = 2000)
    private DelivererQueryThriftService delivererQueryThriftService;

    /**
     * 根据车辆名称获取车辆识别码（VIN）
     *
     * @param vehicleId 车牌号
     * @return 车辆识别码（VIN），如果车辆不存在则返回空字符串
     */
    public String getVinByVehicleId(String vehicleId) {
        DelivererQueryByAccountRequest request = new DelivererQueryByAccountRequest();
        request.setCarNameList(Arrays.asList(vehicleId));

        try {
            // 1 通过查询该车牌号是否对应车辆信息为基准判断车牌是否有效
            ThriftResponse<List<DelivererResponse>> thriftResponse = delivererQueryThriftService.queryDelivererWithPlace(
                    request);
            log.info("getVehicleInfoByVehicleName, responseList = {}", JacksonUtils.to(thriftResponse));
            // 2 查不到消息则说明无效
            if (Objects.isNull(thriftResponse) || CollectionUtils.isEmpty(thriftResponse.getData())) {
                log.error("queryDelivererWithPlace error， 车牌号[{}]不存在", vehicleId);
                return "";
            }
            DelivererResponse delivererResponse = thriftResponse.getData().get(0);
            return delivererResponse.getIdentifyNum();
        } catch (Exception e) {
            log.error("queryDelivererWithPlace error, 根据车牌号[{}]获取车辆信息失败", vehicleId);
        }
        return "";
    }

    /**
     * 根据vinList获取车辆信息
     *
     * @param vinList
     * @return
     */
    public List<DelivererResponse> getDelivererListByVinList(List<String> vinList) {
        if (CollectionUtils.isEmpty(vinList)) {
            return Collections.emptyList();
        }
        DelivererQueryByAccountRequest request = new DelivererQueryByAccountRequest();
        request.setVinList(vinList);

        try {
            ThriftResponse<List<DelivererResponse>> thriftResponse = delivererQueryThriftService.queryDelivererWithPlace(
                    request);
            log.info("getDelivererListByVinList, responseList = {}", JacksonUtils.to(thriftResponse));
            // 2 查不到消息则说明无效
            if (Objects.isNull(thriftResponse) || CollectionUtils.isEmpty(thriftResponse.getData())) {
                throw new Exception("getDelivererListByVinList error, 根据vinList获取车辆信息失败");
            }
            return thriftResponse.getData();
        } catch (Exception e) {
            log.error("getDelivererListByVinList error", e);
        }
        return Collections.emptyList();
    }
}
