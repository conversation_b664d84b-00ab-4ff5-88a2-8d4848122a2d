package com.sankuai.walleops.cloud.triage.component;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.walleops.cloud.triage.BaseTest;
import com.sankuai.walleops.cloud.triage.controller.CloudTriageEventController;
import com.sankuai.walleops.cloud.triage.crane.AbnormalEventCallCockpitCrane;
import com.sankuai.walleops.cloud.triage.crane.AbnormalEventCallCockpitMonitorCrane;
import com.sankuai.walleops.cloud.triage.pojo.request.CloudTriageEventUpdateRequest;
import com.sankuai.walleops.cloud.triage.thrift.impl.ThriftEventQueryServiceImpl;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 通用事件消费者测试
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
@Slf4j
public class CloudTriageConsumerTest extends BaseTest {

    @Resource
    private CloudTriageEventCommonConsumer cloudTriageEventCommonConsumer;

    /**
     * 测试事件过滤功能
     */
    @Test
    public void testEventFilterdemo() {
        try {
            // 构建测试消息
            String message = buildTestMessage();
            System.out.println("测试消息: " + message);

            // 直接调用消费方法
            ConsumeStatus status = cloudTriageEventCommonConsumer.consume(message, null);

            // 打印结果
            System.out.println("消费结果: " + status);
            System.out.println("测试完成!");
        } catch (Exception e) {
            System.out.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 构建测试消息
     */
    private String buildTestMessage() {
        // 构建消息主体
        Map<String, Object> body = new HashMap<>();
        body.put("eventId", "test_event_" + System.currentTimeMillis());
        body.put("vin", "LMTZSV025NC010556");
        body.put("status", 0);

        // 格式化当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        body.put("eventTime", sdf.format(new Date()));

        body.put("eventType", 23);  // 使用23作为测试事件类型
        body.put("reporter", "测试用户");
        body.put("extInfo", "{\"source\":\"单元测试\",\"desc\":\"测试事件过滤功能\"}");

        // 构建完整消息
        Map<String, Object> message = new HashMap<>();
        message.put("type", 130);
        message.put("body", body);
        message.put("timestamp", System.currentTimeMillis());

        return JSON.toJSONString(message);
    }

    @Resource
    private ThriftEventQueryServiceImpl thriftEventQueryService;

    @Test
    public void testEventFilter() {
        thriftEventQueryService.queryUnCheckCollisionDetectionEventList("LMTZSV022NC017593");
    }

    @Resource
    private AbnormalEventCallCockpitCrane callCockpitCrane;

    @Resource
    private AbnormalEventCallCockpitMonitorCrane abnormalEventCallCockpitMonitorCrane;

    @Test
    public void testEventFilter4() throws Exception {
        abnormalEventCallCockpitMonitorCrane.AbnormalEventCallCockpitMonitorCrane();
    }

    @Test
    public void testEventFilter2() throws Exception {
        callCockpitCrane.checkAbnormalEventCallCockpit();
    }

    @Resource
    private CloudTriageEventController cloudTriageEventController;

    @Test
    public void testEventFilter3() throws JsonProcessingException {
        CloudTriageEventUpdateRequest request = new CloudTriageEventUpdateRequest();
        request.setEventId("20250620193127004_accident-detection_s20-173");
        request.setStatus(2);
        cloudTriageEventController.update(request);
    }
} 