package com.sankuai.walleops.cloud.triage.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OperationLogDTO {
    /**
     * 关联ID
     */
    Long relatedId;

    /**
     * 变更类型
     */
    Integer changeType;

    /**
     * 变更内容
     */
    String changeContent;

    /**
     * 变更描述
     */
    String changeDesc;

    /**
     * 工单变更后状态
     */
    Integer status;

    /**
     * 变更人员
     */
    String operator;

}
