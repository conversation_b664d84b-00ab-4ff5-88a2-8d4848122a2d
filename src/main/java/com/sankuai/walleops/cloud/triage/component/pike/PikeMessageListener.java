package com.sankuai.walleops.cloud.triage.component.pike;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.pike.message.api.rpc.business.entity.ClientMessageRequest;
import com.sankuai.pike.message.api.rpc.business.entity.ServerMessageResult;
import com.sankuai.pike.message.sdk.listener.MessageListener;
import com.sankuai.walleops.cloud.triage.constant.CommonConstant;
import com.sankuai.walleops.cloud.triage.proxy.SquirrelProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Map;

@Component
@Slf4j
public class PikeMessageListener implements MessageListener {
    @Resource
    private SquirrelProxy squirrelProxy;

    /**
     * 客户端发送来的上行消息
     *
     * @param clientMessage 客户端消息
     */
    public void onClientMessage(ClientMessageRequest clientMessage) {
        log.info("接收Pike消息, clientMessage: {}", clientMessage);

        String misId = clientMessage.getAlias();
        String token = clientMessage.getToken();
        Map<String, String> message = JSON.parseObject(clientMessage.getMessage(), new TypeReference<Map<String, String>>(){}.getType());
        if(CollectionUtils.isEmpty(message)) {
            // 默认筛选条件，所有筛选条件都使用默认值
            message.put("default", "");
        }

        boolean result = squirrelProxy.hset(CommonConstant.TRIAGE_NEW_EVENT_CATEGORY, CommonConstant.TRIAGE_NEW_EVENT_CLIENT_KEY,
                                            misId + "_" + token, JSON.toJSONString(message));
        if(result) {
            log.info("接收Pike消息, 存储筛选条件成功, misId: {}, token: {}, message: {}", misId, token, message);
        } else {
            log.error("接收Pike消息, 存储筛选条件失败, misId: {}, token: {}, message: {}", misId, token, message);
        }
    }


    /**
     * 服务端发送消息的最终结果，使用 同步/异步方法后无需再设置监听函数
     * <p>
     * 消息失败原因状态码参见：
     *
     * @param serverMessageResult 服务端消息发送最终结果参见
     * @see com.sankuai.pike.message.sdk.entity.MessageFailedReasonEnum
     */
    @Deprecated
    public void onSendMessageResult(ServerMessageResult serverMessageResult) {
        throw new RuntimeException("unimplemented onSendMessageResult method");
    }
}
