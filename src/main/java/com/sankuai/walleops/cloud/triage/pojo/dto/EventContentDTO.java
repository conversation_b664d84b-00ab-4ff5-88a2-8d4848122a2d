package com.sankuai.walleops.cloud.triage.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class EventContentDTO {
    @JSONField(name = "longitude")
    private Double longitude;

    @JSONField(name = "latitude")
    private Double latitude;

    @JSONField(name = "evaluation_alert")
    private String evaluationAlert;

    @JSONField(name = "record_name")
    private String recordName;

    @JSONField(name = "accident_level")
    private Integer accidentLevel;

    @JSONField(name = "need_handle")
    private Boolean needHandle;
}
