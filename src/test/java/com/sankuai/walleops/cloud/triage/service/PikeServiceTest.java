package com.sankuai.walleops.cloud.triage.service;

import com.sankuai.pike.message.client.ClientBuilder;
import com.sankuai.pike.message.client.PikeClient;
import com.sankuai.pike.message.client.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.nio.charset.StandardCharsets;

@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class PikeServiceTest {
    private ClientBuilder clientBuilder = new ClientBuilder();
    private PikeClient pikeClient = clientBuilder.appId(100).businessId("cloud_triage_new_event")
                                    .alias("zhangxin46").env(ENV.TEST).build();

    @Before
    public void initPike() {
        pikeClient.on(new PikeListener() {
            @Override
            public void onMessage(ServerMessage message) {
                // 服务端消息
                log.info("onMessage:" + message);
            }

            @Override
            public void onConnect(ConnectResponse response) {
                // 建连事件
                log.info("onConnect:" + response);
            }

            @Override
            public void onDisconnect(DisconnectResponse response) {
                // 断连事件
                log.info("onDisconnect:" + response);
            }
        });

        pikeClient.connect();

        // 等待pike server返回token
        try {
            Thread.sleep(1000);
        } catch (Exception e) {
            log.error("thread sleep error", e);
        }
    }

    @Test
    public void sendMessageToServer() {
        // 发送客户端消息
        String str = "hello pike.";
        pikeClient.sendMessage(str.getBytes(StandardCharsets.UTF_8), new MessageCallback() {
            @Override
            public void onSuccess(String messageId) {
                log.info("send client message success. id:" + messageId);
            }

            @Override
            public void onFail(String messageId, String reason) {
                log.info("send client message fail. id:" + messageId + " , reason:" + reason);
            }
        });
    }

    @After
    public void disconnectPike() {
        pikeClient.disconnect();
    }

    public static void main(String[] args) {
        PikeServiceTest pikeServiceTest = new PikeServiceTest();
        pikeServiceTest.initPike();

        pikeServiceTest.sendMessageToServer();

        try {
            Thread.sleep(10000);
        } catch (Exception e) {
            log.error("thread sleep error", e);
        }

        pikeServiceTest.disconnectPike();
    }
}
