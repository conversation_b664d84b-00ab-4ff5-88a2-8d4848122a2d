package com.sankuai.walleops.cloud.triage.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sankuai.walleops.cloud.triage.util.DateUtil;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/28
 */
@Data
public class VehicleRealtimeStatusVO {
    private String vin;
    private String vehicleId;
    private String name;
    private String vehicleType;
    private String vehicleTypeDesc;
    private String place;
    private String placeDesc;
    private String purpose;
    private Double height;
    private Double speed;
    private Integer driveMode;
    private String driveModeDesc;
    private Double mileage;
    private Double enableBattery;
    private Double backupBattery;
    private Integer gear;
    private String gearDesc;
    private Integer frontDoorStatus;
    private String frontDoorDesc;
    private Integer rearDoorStatus;
    private String rearDoorDesc;
    private Integer cockpitStatus;
    private String cockpitStatusDesc;
    private Integer subwaybillStatus;
    private String subwaybillStatusDesc;
    private Integer tripStatus;
    private String tripStatusDesc;
    private Integer accidentStatus;
    private String accidentStatusDesc;
    private Integer faultStatus;
    private String faultStatusDesc;
    private Long updateTimestamp;

    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateUtil.YMD_HMS, timezone="GMT+8")
    private Date updateTime;

    private Integer latestEventType;
    private Integer eventCount;
}
