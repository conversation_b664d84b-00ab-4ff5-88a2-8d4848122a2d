package com.sankuai.walleops.cloud.triage.component;

import com.meituan.image.client.ImageUploadClient;
import com.meituan.image.client.ImageUploadClient.Environment;
import com.meituan.image.client.impl.ImageUploadClientImpl;
import com.meituan.image.client.pojo.ImageResult;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.walleops.cloud.triage.pojo.vo.VenusBATokenVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class VenusService {

    /**
     * venus client id
     */
    @Value("$KMS{venus_client_id}")
    private String venusClientId;

    /**
     * venus
     */
    @Value("$KMS{venus_client_secret}")
    private String venusClientSecret;

    /**
     * venus bucket name
     */
    private static String BUCKET_NAME = "publicmovecar";

    /**
     * 获取Venus服务的访问令牌
     * @return VenusBATokenVO对象，包含访问令牌和过期时间
     */
    public VenusBATokenVO getToken() {
        //申请得到的bucket
        String bucket = BUCKET_NAME;
        //申请得到的client_id
        String client_id = venusClientId;
        //申请得到的client_secret
        String client_secret = venusClientSecret;
        ImageUploadClient client = new ImageUploadClientImpl(bucket, client_id, client_secret, 5000,
                5000); //后2个参数为socketTimeout和connectTimeout
        //此接口对应的路径是: https://pic-in.vip.sankuai.com/token/{bucket}
        client.setEnv(Environment.PROD);

        try{
            ImageResult result = client.getToken2();
            log.info("result = {}", JacksonUtils.to(result) );
            return VenusBATokenVO.builder().authorization(result.getToken()).expiretime(result.getExpireTime()).build();
        }
        catch (Exception e){
            log.error("getToken2 error", e);
        }
        return null;
    }

}
