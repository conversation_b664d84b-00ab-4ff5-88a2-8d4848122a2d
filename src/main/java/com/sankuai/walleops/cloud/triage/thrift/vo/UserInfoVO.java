package com.sankuai.walleops.cloud.triage.thrift.vo;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@Builder

public class UserInfoVO {

    /**
     * mis号
     */

    @FieldDoc(description = "sso-登录者的mis号")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    String login;

    /**
     * 姓名
     */

    @FieldDoc(description = "车架号 列表")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    String name;
}
