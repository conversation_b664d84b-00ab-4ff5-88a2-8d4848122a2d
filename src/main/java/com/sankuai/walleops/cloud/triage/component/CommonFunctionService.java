package com.sankuai.walleops.cloud.triage.component;

import com.sankuai.walleops.cloud.triage.pojo.dto.VehicleRealtimeStatusDTO;
import com.sankuai.walleops.cloud.triage.pojo.entity.BizCloudTriageEvent;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CommonFunctionService {

    @Resource
    private InformationApiService informationApiService;

    /**
     * 判断碰撞车辆VHR信息学，系统自动取消
     *
     * @param cloudTriageEvent
     */
    public void setAutoCancel(BizCloudTriageEvent cloudTriageEvent) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("vinList", cloudTriageEvent.getVin());
            List<VehicleRealtimeStatusDTO> resultList = informationApiService.callVehicleStatusService(param);
            if (!resultList.isEmpty()) {
                VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = resultList.get(0);
                //对于VHR <= 1  的车设置 系统自动取消，这里的VHR <= 1 包括两种情况
                //1）VHR 为 空   2）VHR == 1
                Integer isVhrMultiple = vehicleRealtimeStatusDTO.getIsVhrMultiple();
                if (isVhrMultiple != null && isVhrMultiple.intValue() == 0) { // VHR<=1
                    cloudTriageEvent.setOperator("system");
                    // 3 表示取消状态
                    cloudTriageEvent.setStatus(3);
                }
            }
        } catch (Exception e) {
            log.error("query information vhr error: {}", cloudTriageEvent);
        }
    }

}
